# SD WhatsApp Chatbot - Complete Project Flow Explanation

## Overview

The SD WhatsApp Chatbot is a sophisticated AI-powered knowledge base chatbot platform built with FastAPI, designed to provide intelligent customer support through structured Q&A flows and semantic search capabilities. The system combines multiple technologies to deliver a scalable, multi-tenant solution with comprehensive monitoring and billing features.

## Architecture Overview

### Core Technologies
- **Backend Framework**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL for structured data storage
- **Search Engine**: Elasticsearch for vector search and semantic retrieval
- **AI Models**: OpenAI's o4-mini for chat completions and text-embedding-3-small for embeddings
- **Caching**: Redis for conversation state management
- **Message Queue**: RabbitMQ for asynchronous processing and event handling
- **File Storage**: AWS S3 for document storage
- **Monitoring**: Prometheus for metrics collection
- **Containerization**: Docker and Docker Compose

### System Components

#### 1. API Gateway Layer (`app/main.py`)
- **FastAPI Application**: Central entry point with comprehensive middleware stack
- **Authentication Middleware**: JWT token validation and user context extraction
- **Request Logging**: Structured JSON logging with unique request IDs
- **Prometheus Metrics**: Performance monitoring and metrics collection
- **CORS Support**: Cross-origin resource sharing configuration
- **Health Checks**: Kubernetes-ready health endpoints

#### 2. Database Layer (`app/models.py`)
**Core Entities:**
- `User`: User management with tenant isolation
- `Chatbot`: Chatbot configuration with entity-based triggers
- `ChatbotQuestion`: Structured Q&A flow definitions
- `ChatbotKnowledgebase`: Document associations
- `ChatbotConversation`: Conversation state and history
- `Document`: Knowledge base document metadata
- `TenantIndexMapping`: Elasticsearch master index strategy
- `ConversationTokenUsage`: OpenAI token consumption tracking
- `ChatbotCreditUsage`: Billing and credit management

#### 3. Service Layer Architecture

##### ChatbotService (`app/services/chatbot_service.py`)
**Primary Responsibilities:**
- Chatbot CRUD operations with entity-based routing
- Conversation flow management and state transitions
- Question-answer validation and processing
- Knowledge base integration and search
- Credit usage tracking and billing
- File upload validation (10MB max, 5 files limit)
- RabbitMQ usage data publishing

**Key Features:**
- Entity-based chatbot triggers (NEW_ENTITY/EXISTING_ENTITY)
- Multi-phase conversation flow (Q&A → Verification → Knowledge Search)
- AI-enhanced welcome messages
- Smart conversation termination
- Off-topic detection and redirection

##### ElasticsearchService (`app/services/elasticsearch_service.py`)
**Core Functionality:**
- Master index strategy with tenant aliases
- Document chunking and vector embedding generation
- Semantic search with relevance scoring
- Multi-tenant data isolation
- Token-based text chunking (400 tokens with 50 token overlap)
- Hybrid search capabilities

**Master Index Strategy:**
- Distributes tenants across multiple master indices
- Creates tenant-specific aliases for data isolation
- Enables efficient scaling and management
- Supports up to 200 tenants per master index

##### OpenAIService (`app/services/openai_service.py`)
**AI Integration:**
- Text embedding generation using text-embedding-3-small
- Chat completion using o4-mini model
- Token usage tracking for billing
- Rate limiting and error handling
- Contextual response generation
- Conversation enhancement features

##### RabbitMQManager (`app/services/rabbitmq_manager.py`)
**Asynchronous Processing:**
- Event-driven architecture
- Usage data collection and publishing
- Health monitoring and auto-recovery
- Scheduler integration
- Message routing and delivery

#### 4. Router Layer (`app/routers/chatbot.py`)
**API Endpoints:**
- Chatbot management (CRUD operations)
- Conversation handling with entity-based routing
- Knowledge base document upload
- Credit usage reporting and analytics
- Question management and configuration

## Complete System Flow

### 1. Application Startup Flow
```
1. Load environment variables and configuration
2. Initialize database connections and create tables
3. Start RabbitMQ services and event listeners
4. Initialize Elasticsearch connections
5. Setup Prometheus metrics collection
6. Configure middleware stack (auth, logging, CORS)
7. Mount API routers and health endpoints
8. Application ready to serve requests
```

### 2. Authentication Flow
```
Client Request → JWT Token Validation → Extract Claims (tenant_id, user_id) 
→ Permission Validation → Create Auth Context → Process Request
```

### 3. Document Upload and Indexing Flow
```
1. User uploads PDF document via API
2. Validate file type, size (10MB max), and count (5 files max)
3. Store document in AWS S3 with tenant-specific folder structure
4. Extract text content from PDF
5. Chunk text into 400-token segments with 50-token overlap
6. Generate embeddings for each chunk using OpenAI
7. Store chunks and embeddings in Elasticsearch master index
8. Save document metadata in PostgreSQL
9. Return success response with document ID
```

### 4. Chatbot Creation Flow
```
1. Validate chatbot configuration and connected account details
2. Create chatbot record with entity type and trigger settings
3. Configure structured questions for Q&A flow
4. Associate knowledge base documents
5. Set up entity-based routing rules
6. Initialize conversation templates
7. Return chatbot configuration
```

### 5. Conversation Flow (Multi-Phase)

#### Phase 1: Structured Q&A
```
1. User initiates conversation with entity context
2. System identifies appropriate chatbot based on entity type and trigger
3. Generate AI-enhanced welcome message
4. Begin structured Q&A flow
5. For each user response:
   - Validate answer relevance
   - Handle off-topic responses with redirection
   - Store valid answers
   - Progress to next question
6. Complete Q&A phase when all questions answered
```

#### Phase 2: Information Verification
```
1. Generate summary of collected information
2. Present summary to user for confirmation
3. If confirmed: proceed to knowledge search phase
4. If corrections needed:
   - Ask which information to correct
   - Allow specific field updates
   - Re-verify updated information
```

#### Phase 3: Knowledge Search
```
1. User asks questions about knowledge base
2. Generate embedding for user question
3. Perform vector search in Elasticsearch
4. Retrieve relevant document chunks
5. Generate contextual response using OpenAI
6. Track token usage and credit consumption
7. Provide answer with follow-up prompt
8. Continue until user ends conversation
```

### 6. Credit and Billing Flow
```
1. Track every Q&A interaction
2. Calculate credits: 2 credits (with KB) or 1 credit (without KB)
3. Store detailed usage records with timestamps
4. Provide real-time usage analytics
5. Generate billing reports with filtering capabilities
6. Support usage summaries and statistics
```

### 7. Monitoring and Observability Flow
```
1. Generate unique request IDs for tracing
2. Log all requests/responses in structured JSON format
3. Collect Prometheus metrics (latency, errors, usage)
4. Monitor Elasticsearch query performance
5. Track OpenAI API usage and costs
6. Health checks for all system components
7. RabbitMQ message processing monitoring
```

## Data Flow Architecture

### Multi-Tenant Data Isolation
- **Database Level**: tenant_id column in all tables with proper indexing
- **Elasticsearch Level**: Master index strategy with tenant aliases
- **S3 Storage**: Tenant-specific folder structure
- **Redis Caching**: Tenant-prefixed keys for conversation state

### Master Index Strategy
```
Master Indices: kb-master-1, kb-master-2, kb-master-3, ...
Tenant Aliases: tenant-123-kb, tenant-456-kb, tenant-789-kb, ...
Mapping: TenantIndexMapping table tracks tenant → master index assignments
Benefits: Efficient scaling, simplified management, cost optimization
```

## Key Features and Capabilities

### 1. Entity-Based Chatbot Routing
- Chatbots configured with specific entity types (LEAD, CONTACT, etc.)
- Trigger-based routing (NEW_ENTITY vs EXISTING_ENTITY)
- Connected account integration for external system linking

### 2. AI-Enhanced Interactions
- Welcome messages enhanced with OpenAI for human-like greetings
- Contextual response generation considering conversation history
- Smart conversation termination with appropriate farewell messages

### 3. Robust File Handling
- PDF document processing with text extraction
- File validation (type, size, count limits)
- S3 storage with organized folder structure
- Metadata tracking in PostgreSQL

### 4. Comprehensive Monitoring
- Request tracing with unique IDs
- Structured JSON logging
- Prometheus metrics for performance monitoring
- Health checks for all system components

### 5. Scalable Architecture
- Microservices-based design
- Asynchronous processing with RabbitMQ
- Horizontal scaling capabilities
- Multi-tenant isolation and security

## Environment Configuration

### Required Environment Variables
```
# Database
POSTGRES_HOST=postgres
POSTGRES_DB=sdwhatsapp
POSTGRES_USER=sdwhatsapp
POSTGRES_PASSWORD=sdwhatsapp

# Elasticsearch
ELASTICSEARCH_URL=http://elasticsearch-vector-master:9200

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Redis
REDIS_URL=redis://redis:6379/0

# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your-bucket-name

# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/

# Application
LOG_LEVEL=INFO
```

## Deployment and Operations

### Docker Compose Setup
- Multi-container deployment with service dependencies
- Volume mounts for persistent data
- Network configuration for service communication
- Environment variable management

### Health Monitoring
- `/health` endpoint for Kubernetes probes
- Component-specific health checks
- Graceful startup and shutdown procedures
- Auto-recovery mechanisms for RabbitMQ

## API Endpoint Reference

### Chatbot Management Endpoints
- `GET /v1/chatbot/` - List all chatbots for authenticated tenant
- `POST /v1/chatbot/` - Create new chatbot with entity configuration
- `GET /v1/chatbot/{chatbot_id}` - Retrieve specific chatbot details
- `PUT /v1/chatbot/{chatbot_id}` - Update chatbot configuration
- `DELETE /v1/chatbot/{chatbot_id}` - Delete chatbot and associated data

### Conversation Management Endpoints
- `POST /v1/chatbot/conversations` - Start conversation with entity context
- `POST /v1/chatbot/conversations/{conversation_id}` - Continue conversation
- `POST /v1/chatbot/conversations/{conversation_id}/verify` - Verify collected information

### Knowledge Base Management Endpoints
- `POST /v1/chatbot/{chatbot_id}/knowledgebase` - Upload documents to chatbot KB
- `GET /v1/chatbot/{chatbot_id}/knowledgebase` - List chatbot documents

### Question Management Endpoints
- `POST /v1/chatbot/{chatbot_id}/questions` - Add questions to chatbot
- `PUT /v1/chatbot/{chatbot_id}/questions/{question_id}` - Update specific question
- `DELETE /v1/chatbot/{chatbot_id}/questions/{question_id}` - Remove question

### Billing and Analytics Endpoints
- `GET /v1/chatbot/credit-usage` - Detailed credit usage with date filtering
- `GET /v1/chatbot/credit-summary` - Usage statistics and summaries

### System Endpoints
- `GET /health` - Application health check
- `GET /metrics` - Prometheus metrics
- `GET /v2/api-docs` - OpenAPI specification

## Error Handling and Recovery

### Database Connection Management
- Connection pooling with automatic retry
- Transaction rollback on errors
- Graceful degradation for non-critical operations

### Elasticsearch Resilience
- Automatic index creation and mapping
- Fallback strategies for search failures
- Index health monitoring and recovery

### OpenAI API Integration
- Rate limiting compliance (tier 3 plan with 200 tenants)
- Retry logic with exponential backoff
- Fallback responses for API failures
- Token usage optimization

### RabbitMQ Reliability
- Message persistence and acknowledgments
- Dead letter queue handling
- Connection recovery and health monitoring
- Event replay capabilities

## Security Considerations

### Authentication and Authorization
- JWT token validation with proper claims extraction
- Tenant-based data isolation at all layers
- Permission-based access control
- Request rate limiting and throttling

### Data Protection
- Encrypted data transmission (HTTPS)
- Secure S3 bucket configuration
- Database connection encryption
- Sensitive data masking in logs

### Input Validation
- File upload restrictions (type, size, count)
- SQL injection prevention
- XSS protection in API responses
- Request payload validation

## Performance Optimization

### Caching Strategy
- Redis for conversation state management
- Elasticsearch query result caching
- Application-level response caching
- CDN integration for static assets

### Database Optimization
- Proper indexing on tenant_id and frequently queried fields
- Query optimization and connection pooling
- Read replica support for analytics queries
- Partitioning strategies for large tables

### Elasticsearch Performance
- Master index strategy for efficient scaling
- Optimized mapping and analyzer configuration
- Query performance monitoring
- Index lifecycle management

## Testing Strategy

### Unit Testing
- Service layer testing with mocked dependencies
- Database model validation
- API endpoint testing
- Utility function verification

### Integration Testing
- End-to-end conversation flow testing
- Database migration verification
- Elasticsearch integration testing
- OpenAI API integration testing

### Performance Testing
- Load testing for concurrent conversations
- Elasticsearch query performance testing
- Memory and CPU usage profiling
- Database connection pool testing

## Migration and Deployment

### Database Migrations
- Alembic-based schema versioning with proper revision management
- Migration for removing permissions column from users table
- Backward compatibility considerations with rollback support
- Data migration scripts with verification procedures
- Proper revision chain management (latest: d4e5f6g7h8i9)

### Zero-Downtime Deployment
- Blue-green deployment strategy
- Health check integration
- Graceful shutdown procedures
- Database migration coordination

### Monitoring and Alerting
- Application performance monitoring
- Error rate and latency alerting
- Resource utilization monitoring
- Business metric tracking

## Detailed Sequence Diagrams

### Complete Conversation Flow Sequence

```mermaid
sequenceDiagram
    participant User
    participant API as FastAPI Gateway
    participant Auth as Auth Middleware
    participant ChatSvc as Chatbot Service
    participant ES as Elasticsearch
    participant OpenAI
    participant DB as PostgreSQL
    participant Redis
    participant RabbitMQ
    participant S3 as AWS S3
    participant Metrics as Prometheus

    Note over User,Metrics: Document Upload Phase
    User->>API: POST /chatbot/{id}/knowledgebase (PDF file)
    API->>Auth: Validate JWT token
    Auth->>API: Extract tenant_id, user_id
    API->>Metrics: Increment upload counter
    API->>ChatSvc: Process document upload
    ChatSvc->>S3: Store PDF file
    S3->>ChatSvc: Return S3 key
    ChatSvc->>ChatSvc: Extract text from PDF
    ChatSvc->>OpenAI: Generate embeddings for chunks
    OpenAI->>ChatSvc: Return embedding vectors
    ChatSvc->>ES: Index document chunks
    ES->>ChatSvc: Confirm indexing
    ChatSvc->>DB: Store document metadata
    DB->>ChatSvc: Return document ID
    ChatSvc->>API: Upload success response
    API->>User: Document uploaded successfully
    API->>Metrics: Record upload latency

    Note over User,Metrics: Conversation Initialization
    User->>API: POST /chatbot/conversations
    API->>Auth: Validate JWT token
    Auth->>API: Extract auth context
    API->>ChatSvc: Start conversation with entity context
    ChatSvc->>DB: Find chatbot by entity type + trigger
    DB->>ChatSvc: Return chatbot configuration
    ChatSvc->>DB: Create conversation record
    DB->>ChatSvc: Return conversation_id
    ChatSvc->>Redis: Initialize conversation state
    ChatSvc->>OpenAI: Enhance welcome message
    OpenAI->>ChatSvc: Return enhanced greeting
    ChatSvc->>API: Welcome message + first question
    API->>User: Display conversation start

    Note over User,Metrics: Structured Q&A Phase
    loop For each question in chatbot
        User->>API: POST /conversations/{id} (answer)
        API->>Auth: Validate request
        Auth->>API: Authorize access
        API->>ChatSvc: Process user answer
        ChatSvc->>Redis: Get conversation state
        Redis->>ChatSvc: Return current state
        ChatSvc->>ChatSvc: Validate answer relevance

        alt Answer is off-topic
            ChatSvc->>API: Redirect to current question
            API->>User: "Please answer the current question"
        else Answer is valid
            ChatSvc->>DB: Store answer
            ChatSvc->>Redis: Update conversation state
            ChatSvc->>DB: Log interaction for billing

            alt More questions remaining
                ChatSvc->>API: Next question
                API->>User: Display next question
            else All questions completed
                ChatSvc->>OpenAI: Generate information summary
                OpenAI->>ChatSvc: Return summary
                ChatSvc->>API: Summary + verification request
                API->>User: "Is this information correct?"
            end
        end
    end

    Note over User,Metrics: Information Verification
    User->>API: POST /conversations/{id}/verify
    API->>ChatSvc: Process verification response
    ChatSvc->>Redis: Get collected information

    alt Information confirmed correct
        ChatSvc->>Redis: Mark verification complete
        ChatSvc->>API: Enter knowledge search phase
        API->>User: "What would you like to know?"
    else Information needs correction
        ChatSvc->>API: Ask which field to correct
        API->>User: "Which information needs correction?"
        User->>API: Specify correction
        API->>ChatSvc: Process correction
        ChatSvc->>DB: Update information
        ChatSvc->>Redis: Update state
        ChatSvc->>API: Re-verify information
        API->>User: "Is this correct now?"
    end

    Note over User,Metrics: Knowledge Search Phase
    loop Knowledge-based Q&A
        User->>API: Ask knowledge question
        API->>Auth: Validate request
        Auth->>API: Authorize access
        API->>Metrics: Increment query counter
        API->>ChatSvc: Process knowledge question
        ChatSvc->>Redis: Get conversation context
        ChatSvc->>OpenAI: Generate question embedding
        OpenAI->>ChatSvc: Return embedding vector
        ChatSvc->>ES: Vector similarity search
        ES->>ChatSvc: Return relevant chunks
        ChatSvc->>OpenAI: Generate contextual response
        OpenAI->>ChatSvc: Return AI response + token usage
        ChatSvc->>DB: Log credit usage (2 credits with KB)
        ChatSvc->>Redis: Update conversation state
        ChatSvc->>RabbitMQ: Publish usage metrics
        ChatSvc->>API: Return answer
        API->>User: Display answer + "Anything else?"
        API->>Metrics: Record query latency
    end

    Note over User,Metrics: Conversation Termination
    User->>API: End conversation signal
    API->>ChatSvc: Process end request
    ChatSvc->>OpenAI: Generate farewell message
    OpenAI->>ChatSvc: Return personalized goodbye
    ChatSvc->>DB: Mark conversation complete
    ChatSvc->>Redis: Clear conversation state
    ChatSvc->>RabbitMQ: Publish completion metrics
    ChatSvc->>API: Farewell response
    API->>User: Display goodbye message
    API->>Metrics: Record conversation metrics
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Authentication Failures
**Symptoms:** 401 Unauthorized responses
**Causes:**
- Invalid JWT token format
- Expired tokens
- Missing Authorization header
- Incorrect token claims

**Solutions:**
- Verify JWT token structure and expiration
- Check Authorization header format: `Bearer <token>`
- Validate token claims include required fields (tenantId, userId)
- Review authentication middleware logs

#### 2. Elasticsearch Connection Issues
**Symptoms:** Search failures, document indexing errors
**Causes:**
- Elasticsearch service unavailable
- Index mapping conflicts
- Insufficient cluster resources
- Network connectivity issues

**Solutions:**
- Check Elasticsearch cluster health: `GET /_cluster/health`
- Verify index mappings and aliases
- Monitor cluster resource usage
- Review Elasticsearch service logs

#### 3. OpenAI API Failures
**Symptoms:** Empty responses, timeout errors, rate limiting
**Causes:**
- Invalid API key
- Rate limit exceeded
- Model unavailable
- Network timeouts

**Solutions:**
- Verify OPENAI_API_KEY environment variable
- Implement exponential backoff for retries
- Monitor API usage against tier limits
- Check OpenAI service status

#### 4. Database Connection Problems
**Symptoms:** Connection timeouts, transaction failures
**Causes:**
- Database service unavailable
- Connection pool exhaustion
- Long-running transactions
- Network issues

**Solutions:**
- Check PostgreSQL service health
- Monitor connection pool metrics
- Optimize query performance
- Review database logs for errors

#### 5. RabbitMQ Message Processing Issues
**Symptoms:** Messages not processed, queue buildup
**Causes:**
- Consumer failures
- Message serialization errors
- Queue configuration issues
- Network connectivity problems

**Solutions:**
- Check consumer health and error logs
- Verify message format and serialization
- Monitor queue depths and processing rates
- Review RabbitMQ management interface

### Performance Optimization Tips

#### 1. Database Performance
- Add indexes on frequently queried columns (tenant_id, created_at)
- Use connection pooling with appropriate pool sizes
- Implement read replicas for analytics queries
- Regular VACUUM and ANALYZE operations

#### 2. Elasticsearch Optimization
- Use appropriate shard and replica counts
- Optimize mapping for your data types
- Implement index lifecycle management
- Monitor query performance and optimize slow queries

#### 3. Redis Caching
- Set appropriate TTL values for cached data
- Monitor memory usage and eviction policies
- Use Redis clustering for high availability
- Implement cache warming strategies

#### 4. Application Performance
- Use async/await for I/O operations
- Implement request/response compression
- Optimize JSON serialization/deserialization
- Monitor memory usage and garbage collection

This comprehensive flow explanation provides a complete understanding of the SD WhatsApp Chatbot system architecture, data flow, operational characteristics, production considerations, and troubleshooting guidance.
