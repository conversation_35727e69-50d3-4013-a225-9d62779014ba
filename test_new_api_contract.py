#!/usr/bin/env python3
"""
Test script for the new API contract changes:
1. Test new conversation start API with entityDetails and connectedAccount
2. Test file upload with 202 response and background processing
3. Test max 2 files limit
4. Verify response formats match the new contract
"""

import pytest
import json
import asyncio
import tempfile
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import patch, MagicMock
from app.main import app
from app.models import Cha<PERSON>bot, ChatbotQuestion, User
from app.database import get_db
from sqlalchemy.orm import Session

# Test client
client = TestClient(app)

# Mock JWT token for testing
MOCK_JWT_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mock_signature"

def mock_auth_context():
    """Mock authentication context"""
    mock_context = MagicMock()
    mock_context.user_id = "7"
    mock_context.tenant_id = 7
    return mock_context

def create_test_chatbot(db: Session):
    """Create a test chatbot for testing"""
    chatbot = Chatbot(
        id="test-chatbot-123",
        tenant_id=7,
        name="Test Chatbot",
        type="AI",
        description="Test chatbot for API contract testing",
        status="ACTIVE",
        welcome_message="Welcome to our test chatbot!",
        entity_type="LEAD",
        connected_account_id=123,
        created_by="7"
    )
    db.add(chatbot)
    
    # Add test questions
    question1 = ChatbotQuestion(
        id="q1",
        chatbot_id="test-chatbot-123",
        tenant_id=7,
        question="What is your name?",
        field_id=1,
        display_name="Name",
        entity_type="LEAD",
        name="name",
        standard=True
    )
    question2 = ChatbotQuestion(
        id="q2",
        chatbot_id="test-chatbot-123",
        tenant_id=7,
        question="What is your email?",
        field_id=2,
        display_name="Email",
        entity_type="LEAD",
        name="email",
        standard=True
    )
    db.add(question1)
    db.add(question2)
    db.commit()
    
    return chatbot

@patch('app.routers.chatbot.get_auth_context')
def test_new_conversation_api_contract(mock_get_auth):
    """Test the new conversation start API contract"""
    # Mock authentication
    mock_get_auth.return_value = mock_auth_context()
    
    # Mock database and services
    with patch('app.routers.chatbot.get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock chatbot service methods
        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service
            
            # Mock chatbot and questions
            mock_chatbot = MagicMock()
            mock_chatbot.id = "test-chatbot-123"
            mock_chatbot.name = "Test Chatbot"
            mock_chatbot.welcome_message = "Welcome to our test chatbot!"
            
            mock_questions = [
                MagicMock(id="q1", question="What is your name?"),
                MagicMock(id="q2", question="What is your email?")
            ]
            
            mock_service.find_chatbot_by_entity_and_account.return_value = mock_chatbot
            mock_service.get_chatbot_questions_for_conversation.return_value = mock_questions
            
            # Mock Redis and Elasticsearch services
            with patch('app.routers.chatbot.RedisService') as mock_redis, \
                 patch('app.routers.chatbot.ElasticsearchService') as mock_es:
                
                mock_es_instance = MagicMock()
                mock_es.return_value = mock_es_instance
                mock_es_instance.select_next_question.return_value = (
                    {"id": "q1", "question": "What is your name?"}, 10, 5, "o4-mini"
                )
                
                # Test the new API contract
                request_data = {
                    "message": "Hello, I need help",
                    "entityDetails": [
                        {
                            "id": 456,
                            "entityType": "LEAD"
                        }
                    ],
                    "connectedAccount": {
                        "id": 123,
                        "name": "Test Account"
                    }
                }
                
                # Mock request state
                with patch('fastapi.Request') as mock_request:
                    mock_request_instance = MagicMock()
                    mock_request_instance.state.auth_context = mock_auth_context()
                    
                    response = client.post(
                        "/v1/chatbot/conversations",
                        json=request_data,
                        headers={"Authorization": MOCK_JWT_TOKEN}
                    )
                
                # Verify response format matches new contract
                assert response.status_code == 200
                response_data = response.json()
                
                # Check new response format
                assert "chatbotConversationId" in response_data
                assert "message" in response_data
                assert isinstance(response_data["chatbotConversationId"], str)
                assert isinstance(response_data["message"], str)
                
                # Verify old fields are not present
                assert "conversation_id" not in response_data
                assert "answer" not in response_data
                assert "nextQuestion" not in response_data
                
                print("✓ New conversation API contract test passed")

def test_file_upload_synchronous_processing():
    """Test file upload with synchronous processing (blocking)"""

    # Create test PDF content
    test_pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF"

    # Mock authentication
    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()

        # Mock chatbot service
        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service

            # Mock successful upload response
            mock_service.upload_multiple_knowledgebase_files.return_value = {
                "message": "Processed 2 files successfully, 0 failed",
                "successful_uploads": [
                    {"filename": "test1.pdf", "status": "success", "document_id": "doc-1"},
                    {"filename": "test2.pdf", "status": "success", "document_id": "doc-2"}
                ],
                "failed_uploads": [],
                "total_files": 2
            }

            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f1, \
                 tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f2:

                f1.write(test_pdf_content)
                f2.write(test_pdf_content)
                f1.flush()
                f2.flush()

                # Test upload with 2 files (should succeed)
                with open(f1.name, 'rb') as file1, open(f2.name, 'rb') as file2:
                    response = client.post(
                        "/v1/chatbot/test-chatbot-123/knowledgebase",
                        files=[
                            ("files", ("test1.pdf", file1, "application/pdf")),
                            ("files", ("test2.pdf", file2, "application/pdf"))
                        ],
                        headers={"Authorization": MOCK_JWT_TOKEN}
                    )

                # Verify 200 response (synchronous processing)
                assert response.status_code == 200
                response_data = response.json()

                # Check response format
                assert "message" in response_data
                assert "successful_uploads" in response_data
                assert "failed_uploads" in response_data
                assert "total_files" in response_data
                assert response_data["total_files"] == 2
                assert len(response_data["successful_uploads"]) == 2

                print("✓ File upload synchronous processing test passed")

def test_file_upload_limit():
    """Test file upload limit (max 2 files)"""
    
    # Create test PDF content
    test_pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF"
    
    # Mock authentication
    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()
        
        # Mock chatbot service to raise exception for too many files
        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service

            from fastapi import HTTPException
            mock_service.upload_multiple_knowledgebase_files.side_effect = HTTPException(
                status_code=400,
                detail="Maximum 2 files allowed per upload. You uploaded 3 files."
            )
            
            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f1, \
                 tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f2, \
                 tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f3:
                
                f1.write(test_pdf_content)
                f2.write(test_pdf_content)
                f3.write(test_pdf_content)
                f1.flush()
                f2.flush()
                f3.flush()
                
                # Test upload with 3 files (should fail)
                with open(f1.name, 'rb') as file1, open(f2.name, 'rb') as file2, open(f3.name, 'rb') as file3:
                    response = client.post(
                        "/v1/chatbot/test-chatbot-123/knowledgebase",
                        files=[
                            ("files", ("test1.pdf", file1, "application/pdf")),
                            ("files", ("test2.pdf", file2, "application/pdf")),
                            ("files", ("test3.pdf", file3, "application/pdf"))
                        ],
                        headers={"Authorization": MOCK_JWT_TOKEN}
                    )
                
                # Verify 400 response for too many files
                assert response.status_code == 400
                response_data = response.json()
                assert "Maximum 2 files allowed" in response_data["detail"]
                
                print("✓ File upload limit test passed")

def run_all_tests():
    """Run all tests"""
    print("Running API contract tests...")
    
    try:
        test_new_conversation_api_contract()
        test_file_upload_synchronous_processing()
        test_file_upload_limit()

        print("\n✓ All API contract tests passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
