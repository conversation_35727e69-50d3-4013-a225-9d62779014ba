#!/usr/bin/env python3
"""
Dependency checker and installer for the chatbot project
"""

import subprocess
import sys
import os

def check_dependency(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Install a package using pip"""
    try:
        print(f"📦 Installing {package_name}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Successfully installed {package_name}")
            return True
        else:
            print(f"❌ Failed to install {package_name}: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing {package_name}: {e}")
        return False

def main():
    """Main dependency checker"""
    print("🔍 Checking dependencies for chatbot project...")
    print("=" * 50)
    
    # Required dependencies for the project
    dependencies = [
        ("sqlalchemy", "sqlalchemy"),
        ("psycopg2-binary", "psycopg2"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("redis", "redis"),
        ("openai", "openai"),
        ("elasticsearch", "elasticsearch"),
        ("boto3", "boto3"),
        ("PyPDF2", "PyPDF2"),
        ("python-multipart", "multipart"),
        ("prometheus-client", "prometheus_client"),
        ("aio-pika", "aio_pika")
    ]
    
    # Optional dependencies
    optional_dependencies = [
        ("pytest", "pytest"),
        ("requests", "requests")
    ]
    
    missing_required = []
    missing_optional = []
    
    # Check required dependencies
    print("\n📋 Checking required dependencies:")
    for package_name, import_name in dependencies:
        if check_dependency(package_name, import_name):
            print(f"  ✅ {package_name}")
        else:
            print(f"  ❌ {package_name} (missing)")
            missing_required.append(package_name)
    
    # Check optional dependencies
    print("\n📋 Checking optional dependencies:")
    for package_name, import_name in optional_dependencies:
        if check_dependency(package_name, import_name):
            print(f"  ✅ {package_name}")
        else:
            print(f"  ⚠️  {package_name} (optional, missing)")
            missing_optional.append(package_name)
    
    # Install missing dependencies
    if missing_required:
        print(f"\n🔧 Installing {len(missing_required)} missing required dependencies...")
        failed_installs = []
        
        for package in missing_required:
            if not install_package(package):
                failed_installs.append(package)
        
        if failed_installs:
            print(f"\n❌ Failed to install: {', '.join(failed_installs)}")
            print("Please install them manually:")
            for package in failed_installs:
                print(f"  pip install {package}")
            return False
        else:
            print("\n✅ All required dependencies installed successfully!")
    
    if missing_optional:
        print(f"\n📝 Optional dependencies missing: {', '.join(missing_optional)}")
        print("You can install them with:")
        for package in missing_optional:
            print(f"  pip install {package}")
    
    # Check if we can import the app
    print("\n🧪 Testing app imports...")
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from app.database import get_database_url
        print("  ✅ app.database import successful")
        
        from app.models import Chatbot, ChatbotCreditUsage
        print("  ✅ app.models import successful")
        
        from app.services.chatbot_service import ChatbotService
        print("  ✅ app.services import successful")
        
        print("\n🎉 All imports successful! Project is ready to run.")
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        print("\nPlease check your environment configuration.")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 50)
        print("🚀 Next steps:")
        print("1. Run migration: python3 migrations/add_trigger_and_credit_usage.py")
        print("2. Run tests: python3 test_comprehensive_chatbot_changes.py")
        print("3. Start the application: uvicorn app.main:app --reload")
        sys.exit(0)
    else:
        print("\n" + "=" * 50)
        print("❌ Please resolve the dependency issues before proceeding.")
        sys.exit(1)
