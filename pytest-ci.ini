[tool:pytest]
# Pytest configuration file for CI/CD environments
# This configuration excludes tests that require external dependencies

# Test discovery patterns
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test directories
testpaths = test

# Output options for CI/CD
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --asyncio-mode=auto
    --ignore=test/test_comprehensive_chatbot_changes.py
    --ignore=test/test_new_chatbot_structure.py
    --ignore=test/test_file_upload_validation.py
    --ignore=test/test_multiple_file_upload.py
    --ignore=test/test_migration.py
    --ignore=test/test_llm_question_selection.py
    -m "not integration and not slow"

# Markers for categorizing tests
markers =
    unit: Unit tests that don't require external dependencies
    integration: Integration tests that require external services
    slow: Tests that take a long time to run
    api: API endpoint tests
    database: Tests that require database connection
    elasticsearch: Tests that require Elasticsearch
    rabbitmq: Tests that require RabbitMQ
    migration: Database migration tests
    validation: Input validation tests
    service: Service layer tests
    crud: CRUD operation tests
    auth: Authentication and authorization tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds) - shorter for CI
timeout = 120

# Ignore certain warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*
    ignore::ImportWarning
