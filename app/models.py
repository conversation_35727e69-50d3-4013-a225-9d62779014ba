from sqlalchemy import Column, String, Integer, BigInteger, DateTime, ForeignKey, Boolean, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator

Base = declarative_base()

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    email = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Document(Base):
    __tablename__ = "documents"

    id = Column(String, primary_key=True)
    tenant_id = Column(BigInteger, nullable=False)
    document_name = Column(String, nullable=False)
    document_type = Column(String, nullable=False)
    es_index = Column(String)
    es_document_id = Column(String)
    s3_key = Column(String)  # Add S3 key field
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String)

class Chatbot(Base):
    __tablename__ = "chatbots"

    id = Column(String, primary_key=True, index=True)
    tenant_id = Column(BigInteger, index=True)
    name = Column(String)
    type = Column(String, default="AI")  # 'AI' or 'RULE'
    description = Column(String, nullable=True)
    status = Column(String, default="DRAFT")  # 'DRAFT', 'ACTIVE', 'INACTIVE'
    welcome_message = Column(String, nullable=True)
    thank_you_message = Column(String, nullable=True)
    # Connected account fields
    connected_account_display_name = Column(String, nullable=True)
    connected_account_id = Column(Integer, nullable=True)
    # Trigger field for entity creation flow
    trigger = Column(String, nullable=True)  # 'NEW_ENTITY' or 'EXISTING_ENTITY'
    # User tracking fields
    created_by = Column(String, nullable=True)  # User ID who created the chatbot
    updated_by = Column(String, nullable=True)  # User ID who last updated the chatbot
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ChatbotQuestion(Base):
    __tablename__ = "chatbot_questions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, nullable=False, index=True)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    question = Column(String(500), nullable=False)  # Increased length for longer questions
    position = Column(Integer, nullable=False)  # Position/order of the question
    lead_field_id = Column(Integer, nullable=True)  # Lead field ID (nullable)
    lead_field_display_name = Column(String, nullable=True)  # Lead field display name (nullable)
    is_lead_field_standard = Column(Boolean, nullable=True)  # Whether lead field is standard (nullable)
    contact_field_id = Column(Integer, nullable=True)  # Contact field ID (nullable)
    contact_field_display_name = Column(String, nullable=True)  # Contact field display name (nullable)
    is_contact_field_standard = Column(Boolean, nullable=True)  # Whether contact field is standard (nullable)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class ChatbotKnowledgebase(Base):
    __tablename__ = "chatbot_knowledgebases"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, nullable=False, index=True)
    document_id = Column(String, nullable=False, index=True)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class TenantIndexMapping(Base):
    """
    Tracks which master Elasticsearch index each tenant is assigned to.
    This enables the master index + alias strategy for multi-tenant data organization.
    """
    __tablename__ = "tenant_index_mappings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tenant_id = Column(BigInteger, nullable=False, unique=True, index=True)
    master_index_number = Column(Integer, nullable=False, index=True)
    alias_name = Column(String, nullable=False, unique=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class ChatbotConversation(Base):
    __tablename__ = "chatbot_conversations"

    id = Column(String, primary_key=True, index=True)
    chatbot_id = Column(String, ForeignKey("chatbots.id"))
    tenant_id = Column(BigInteger, index=True)
    user_id = Column(String, index=True)  # Add user_id field
    conversation_data = Column(Text)
    completed = Column(Boolean, default=False)
    # Entity details for this conversation
    entity_details = Column(JSON, nullable=True)  # Store entity IDs and types
    connected_account_id = Column(Integer, nullable=True, index=True)  # Connected account ID
    connected_account_name = Column(String, nullable=True)  # Connected account name
    entity_update_status = Column(JSON, nullable=True)  # Track entity update results
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with token usage
    token_usage = relationship(
        "ConversationTokenUsage", 
        back_populates="conversation",
        cascade="all, delete-orphan"
    )

class ConversationTokenUsage(Base):
    __tablename__ = "conversation_token_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("chatbot_conversations.id"), index=True)
    tenant_id = Column(BigInteger, index=True)
    input = Column(JSON)  # JSONB column containing user message and system prompt
    output = Column(JSON)  # JSONB column containing LLM-generated response
    input_tokens = Column(Integer, default=0)  # Tokens in input prompt sent to LLM
    output_tokens = Column(Integer, default=0)  # Tokens in response generated by LLM
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationship with conversation
    conversation = relationship("ChatbotConversation", back_populates="token_usage")

class ChatbotCreditUsage(Base):
    __tablename__ = "chatbot_credit_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), index=True)
    conversation_id = Column(String, ForeignKey("chatbot_conversations.id"), index=True)
    tenant_id = Column(BigInteger, index=True)
    question = Column(String, nullable=False)  # The question that was asked
    answer = Column(String, nullable=False)    # The answer that was provided
    credits_used = Column(Integer, default=0)  # Credits consumed (1 or 2)
    has_knowledgebase = Column(Boolean, default=False)  # Whether knowledgebase was available
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    chatbot = relationship("Chatbot")
    conversation = relationship("ChatbotConversation")

# JWT Token models
class TokenAction(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    read: bool = False
    write: bool = False
    update: bool = False
    delete: bool = False
    email: bool = False
    call: bool = False
    sms: bool = False
    task: bool = False
    note: bool = False
    meeting: bool = False
    document: bool = False
    readAll: bool = False
    updateAll: bool = False
    deleteAll: bool = False
    quotation: bool = False
    reshare: bool = False
    reassign: bool = False

class TokenPermission(BaseModel):
    model_config = ConfigDict(extra='ignore')

    id: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    limits: Optional[int] = None
    units: Optional[str] = None
    action: Optional[TokenAction] = None

class TokenSource(BaseModel):
    model_config = ConfigDict(extra='ignore')

    type: Optional[str] = None
    id: Optional[str] = None
    name: Optional[str] = None

class TokenMeta(BaseModel):
    model_config = ConfigDict(extra='ignore')

    rate_limit: Optional[int] = Field(default=None, alias="rate-limit")
    pid: Optional[int] = None

class TokenData(BaseModel):
    model_config = ConfigDict(extra='ignore')

    accessToken: str
    expiresIn: Optional[int] = None
    expiry: Optional[Union[int, str]] = None  # Allow both int and string timestamps
    tokenType: Optional[str] = "Bearer"
    refreshToken: Optional[str] = None
    permissions: Optional[List[TokenPermission]] = []
    userId: Union[str, int]  # Allow both string and int
    username: Optional[str] = None
    tenantId: Union[str, int]  # Allow both string and int
    source: Optional[TokenSource] = None
    meta: Optional[TokenMeta] = None

    @field_validator('expiry', mode='before')
    @classmethod
    def validate_expiry(cls, v):
        """Convert string timestamps to integers or keep as is"""
        if v is None:
            return v
        if isinstance(v, str):
            # If it's a timestamp string, try to parse it
            try:
                from datetime import datetime
                # Parse ISO format timestamp and convert to Unix timestamp
                dt = datetime.fromisoformat(v.replace('Z', '+00:00'))
                return int(dt.timestamp())
            except (ValueError, AttributeError):
                # If parsing fails, return the string as is
                return v
        return v

class JWTPayload(BaseModel):
    model_config = ConfigDict(extra='ignore')

    iss: Optional[str] = None
    data: Optional[TokenData] = None

    # Alternative flat structure support
    accessToken: Optional[str] = None
    userId: Optional[Union[str, int]] = None
    tenantId: Optional[Union[str, int]] = None
    permissions: Optional[List[TokenPermission]] = []
    username: Optional[str] = None
    refreshToken: Optional[str] = None
    source: Optional[TokenSource] = None
    meta: Optional[TokenMeta] = None

# Pydantic models for API requests/responses
class QuestionCreate(BaseModel):
    question: str
    position: int
    leadFieldId: Optional[int] = None
    leadFieldDisplayName: Optional[str] = None
    isLeadFieldStandard: Optional[bool] = None
    contactFieldId: Optional[int] = None
    contactFieldDisplayName: Optional[str] = None
    isContactFieldStandard: Optional[bool] = None

class QuestionUpdate(BaseModel):
    question: Optional[str] = None
    position: Optional[int] = None
    leadFieldId: Optional[int] = None
    leadFieldDisplayName: Optional[str] = None
    isLeadFieldStandard: Optional[bool] = None
    contactFieldId: Optional[int] = None
    contactFieldDisplayName: Optional[str] = None
    isContactFieldStandard: Optional[bool] = None

class ConnectedAccount(BaseModel):
    displayName: str
    accountId: int  # Removed entityType - not needed for account-based chatbots

class ChatbotCreate(BaseModel):
    name: str
    type: str  # 'AI' or 'RULE'
    description: str = None
    welcomeMessage: str = None
    thankYouMessage: str = None
    connectedAccount: ConnectedAccount = None
    trigger: str = None  # Keep trigger field - 'NEW_ENTITY' or 'EXISTING_ENTITY'

class ChatbotUpdate(BaseModel):
    name: str = None
    description: str = None
    welcomeMessage: str = None
    thankYouMessage: str = None
    connectedAccount: ConnectedAccount = None
    trigger: str = None  # Keep trigger field - 'NEW_ENTITY' or 'EXISTING_ENTITY'
    questions: List[QuestionCreate] = None
    knowledgebase_ids: List[str] = None

class ConversationMessage(BaseModel):
    message: str

class EntityDetail(BaseModel):
    id: int
    entityType: str

class ConnectedAccountDetail(BaseModel):
    id: int
    name: str

class ConversationRequest(BaseModel):
    message: str
    entityDetails: List[EntityDetail]
    connectedAccount: ConnectedAccountDetail

class ConversationResponse(BaseModel):
    chatbotConversationId: str
    message: str
    firstQuestion: Optional[str] = None  # Include first question in response

class CreditUsageResponse(BaseModel):
    id: str
    chatbot_id: str
    conversation_id: str
    question: str
    answer: str
    credits_used: int
    has_knowledgebase: bool
    timestamp: datetime
    message: str = None
    completed: bool = False
    verification_pending: bool = False
    answers: List[dict] = None
    is_knowledge_response: bool = False
    is_off_topic: bool = False
    ended: bool = False

# User-related Pydantic models
class UserResponse(BaseModel):
    id: str
    name: str

class ChatbotListResponse(BaseModel):
    id: str
    name: str
    type: str
    description: str = None
    status: str
    welcomeMessage: str = None
    thankYouMessage: str = None
    connectedAccountDisplayName: str = None
    connectedAccountId: int = None
    trigger: str = None  # Keep trigger field
    # Removed entityType - not needed for account-based chatbots
    createdBy: UserResponse = None
    updatedBy: UserResponse = None
    createdAt: datetime
    updatedAt: datetime
