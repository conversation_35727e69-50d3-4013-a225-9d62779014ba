import os
import logging
from elasticsearch import Elasticsearch
import json
from openai import OpenAI
from app.services.openai_service import OpenAIService
from app.services.elasticsearch_index_manager import ElasticsearchIndexManager
from dotenv import load_dotenv
import re
import tiktoken
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class ElasticsearchService:
    def __init__(self):
        # Load environment variables
        load_dotenv()

        # Get Elasticsearch URL from environment variables
        elasticsearch_url = "http://elasticsearch-vector-master:9200"
        self.es_client = Elasticsearch(elasticsearch_url)
        self.index_prefix = os.getenv("ELASTICSEARCH_INDEX_PREFIX", "kb-")  # Keep for backward compatibility

        # Initialize Elasticsearch Index Manager for new master index strategy
        self.index_manager = ElasticsearchIndexManager()

        # Initialize OpenAI service
        self.openai_service = OpenAIService()

        # Initialize OpenAI client directly for backward compatibility
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if openai_api_key:
            self.openai_client = OpenAI(api_key=openai_api_key)
        else:
            logger.warning("OPENAI_API_KEY not set in environment variables")
            self.openai_client = None
        
        # Initialize tokenizer for chunking
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")  # Default encoding for text-embedding models
        except Exception as e:
            logger.warning(f"Could not initialize tokenizer: {str(e)}. Will use character-based estimation.")
            self.tokenizer = None
        
        logger.info(f"Initialized Elasticsearch service with URL: {elasticsearch_url}")
    
    def count_tokens(self, text):
        """
        Count the number of tokens in a text string
        
        Args:
            text: The text to count tokens for
            
        Returns:
            Number of tokens
        """
        if self.tokenizer:
            try:
                return len(self.tokenizer.encode(text))
            except Exception as e:
                logger.warning(f"Error counting tokens with tokenizer: {str(e)}. Using character estimation.")
                # Fallback to character-based estimation (approx 4 chars per token)
                return len(text) // 4
        else:
            # Character-based estimation (approx 4 chars per token)
            return len(text) // 4
    
    def index_document(self, tenant_id: int, document_id, content, chatbot_id, chunk_size=400, overlap=50, db: Session = None):
        """
        Index a document in Elasticsearch with vector embeddings using master index strategy

        Args:
            tenant_id: The tenant ID (integer)
            document_id: The document ID
            content: The document content
            chatbot_id: The chatbot ID this document belongs to
            chunk_size: Maximum size of each chunk in tokens (default: 400)
            overlap: Number of tokens to overlap between chunks (default: 50)
            db: Database session for index management

        Returns:
            The Elasticsearch alias name (tenant-specific)
        """
        try:
            # Use new master index strategy if db session is provided
            if db is not None:
                # Get or create tenant alias using the index manager
                alias_name = self.index_manager.get_or_create_tenant_alias(str(tenant_id), db)
                index_name = alias_name
                logger.info(f"Using tenant alias: {alias_name}")
            else:
                # Fallback to old strategy for backward compatibility
                index_name = f"{self.index_prefix}{tenant_id}"

                # Check if index exists, create if not
                if not self.es_client.indices.exists(index=index_name):
                    # Create index with vector search settings
                    self.es_client.indices.create(
                        index=index_name,
                        body={
                            "mappings": {
                                "properties": {
                                    "content": {"type": "text"},
                                    "document_id": {"type": "keyword"},
                                    "chatbot_id": {"type": "keyword"},
                                    "chunk_id": {"type": "keyword"},
                                    "chunk_index": {"type": "integer"},
                                    "tenantId": {"type": "long"},  # Changed from keyword to long for integer tenant IDs
                                    "tenant_id": {"type": "long"},  # Also support snake_case version as long
                                    "embedding": {
                                        "type": "dense_vector",
                                        "dims": 1536,
                                        "index": True,
                                        "similarity": "cosine"
                                    }
                                }
                            }
                        }
                    )
                    logger.info(f"Created Elasticsearch index: {index_name}")
            
            # Clean the content before chunking
            clean_content = self.clean_text_for_embedding(content)
            logger.info(f"Cleaned content length: {len(clean_content)} chars (original: {len(content)} chars)")
            
            # Split the document into chunks using token-based chunking with overlap
            chunks = self.chunk_document(clean_content, max_tokens=chunk_size, overlap_tokens=overlap)
            
            # If chunking produced no chunks, use the whole content as a single chunk
            if not chunks:
                logger.warning("Chunking produced no chunks. Using entire content as a single chunk.")
                chunks = [clean_content]
            
            logger.info(f"Document split into {len(chunks)} chunks with {overlap} tokens overlap")
            
            # Index each chunk with its own embedding
            for i, chunk in enumerate(chunks):
                # Generate a unique chunk ID
                chunk_id = f"{document_id}_{i}"

                # Generate embedding for the chunk
                embedding = self.openai_service.generate_embedding(chunk)

                # Index chunk with embedding (include tenant_id for filtering)
                self.es_client.index(
                    index=index_name,
                    id=chunk_id,
                    body={
                        "content": chunk,
                        "document_id": document_id,
                        "chatbot_id": chatbot_id,
                        "chunk_id": chunk_id,
                        "chunk_index": i,
                        "tenantId": tenant_id,  # Add tenantId for multi-tenant filtering (integer)
                        "tenant_id": tenant_id,  # Also add snake_case version (integer)
                        "embedding": embedding
                    }
                )
                
                logger.info(f"Indexed chunk {i+1}/{len(chunks)} for document {document_id}")
            
            logger.info(f"Indexed document with {len(chunks)} chunks in Elasticsearch: {document_id}")
            return index_name
        except Exception as e:
            logger.error(f"Error indexing document in Elasticsearch: {str(e)}")
            raise



    def is_conversation_ending(self, user_message):
        """
        Determine if a user message indicates they want to end the conversation

        Args:
            user_message: The user's message

        Returns:
            Tuple of (boolean indicating if the user wants to end the conversation, input_tokens, output_tokens, model)
        """
        try:
            return self.openai_service.is_conversation_ending(user_message)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.is_conversation_ending: {str(e)}")
            return False, 0, 0, "unknown"

    def generate_chat_response(self, messages, max_tokens=30000000, temperature=None, retry_count=0):
        """
        Generate a chat response using OpenAI via the OpenAIService
        
        Args:
            messages: List of message objects with role and content
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for response generation
            retry_count: Number of retries attempted (for internal use)
        
        Returns:
            Tuple of (generated response text, input token count, output token count, model)
        """
        try:
            # Use the OpenAIService to generate the response
            return self.openai_service.generate_chat_response(messages, max_tokens, temperature, retry_count)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.generate_chat_response: {str(e)}")
            return "I couldn't understand your question. Could you please rephrase it?", 0, 0, "unknown"



    def semantic_search(self, tenant_id: int, query, chatbot_id=None, limit=3, db: Session = None):
        """
        Perform semantic search on the Elasticsearch index using master index strategy

        Args:
            tenant_id: The tenant ID
            query: The search query
            chatbot_id: Optional chatbot ID to filter by
            limit: Maximum number of results to return
            db: Database session for index management

        Returns:
            List of search results
        """
        try:
            # Use new master index strategy if db session is provided
            if db is not None:
                # Get tenant alias using the index manager
                alias_name = self.index_manager.get_tenant_alias(str(tenant_id))

                # Check if alias exists, if not try to create it
                if not self.es_client.indices.exists_alias(name=alias_name):
                    logger.warning(f"Tenant alias {alias_name} does not exist, attempting to create...")
                    alias_name = self.index_manager.get_or_create_tenant_alias(str(tenant_id), db)

                index_name = alias_name
                print(f"------Search into tenant alias: {index_name}")
            else:
                # Fallback to old strategy for backward compatibility
                index_name = f"{self.index_prefix}{tenant_id}"
                print(f"------Search into document index {index_name}")

            # Generate embedding for the query
            embedding = self.openai_service.generate_embedding(query)

            # Build the query with tenant filtering for master index strategy
            query_filters = []

            # Always filter by tenantId when using master index strategy
            if db is not None:
                query_filters.append({"term": {"tenantId": tenant_id}})

            # Add chatbot_id filter if provided
            if chatbot_id:
                query_filters.append({"term": {"chatbot_id": chatbot_id}})
                print(f"------Filtering search by chatbot ID: {chatbot_id}")

            # Build the query
            query_body = {
                "size": limit * 2,  # Get more results to account for deduplication
                "query": {
                    "script_score": {
                        "query": {
                            "bool": {
                                "must": {"match_all": {}},
                                "filter": query_filters if query_filters else []
                            }
                        },
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                            "params": {"query_vector": embedding}
                        }
                    }
                },
                "_source": ["content", "document_id", "chatbot_id", "chunk_id", "tenant_id"]
            }
            
            # Perform vector search
            response = self.es_client.search(
                index=index_name,
                body=query_body
            )
            
            # Process results and deduplicate by document_id
            results = []
            seen_docs = set()
            print(f"-------------Search response is {response}")
            
            for hit in response["hits"]["hits"]:
                doc_id = hit["_source"].get("document_id")
                
                # Only include the highest scoring chunk from each document
                if doc_id not in seen_docs:
                    seen_docs.add(doc_id)
                    results.append({
                        "document_id": doc_id,
                        "content": hit["_source"].get("content"),
                        "score": hit["_score"],
                        "chunk_id": hit["_source"].get("chunk_id")
                    })
                    
                    # Stop once we have enough unique documents
                    if len(results) >= limit:
                        break
            
            logger.info(f"Semantic search results: {len(results)}", extra={
                "tenant_id": tenant_id,
                "query": query,
                "chatbot_id_filter": chatbot_id is not None,
                "chatbot_id": chatbot_id,
                "result_count": len(results)
            })
            print(f"----------Lets return result as {results}")
            return results
        except Exception as e:
            logger.error(f"Error in semantic search: {str(e)}")
            return []



    def generate_embedding(self, text):
        """
        Generate an embedding for the given text using OpenAI
        
        Args:
            text: The text to generate an embedding for
        
        Returns:
            The embedding vector
        """
        try:
            return self.openai_service.generate_embedding(text)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.generate_embedding: {str(e)}")
            raise

    def select_next_question(self, conversation_history, remaining_questions, answered_questions):
        """
        Select the next question to ask based on conversation history and remaining questions

        Args:
            conversation_history: List of conversation messages
            remaining_questions: List of questions that haven't been asked yet
            answered_questions: List of questions that have been answered

        Returns:
            Tuple of (selected_question_dict, input_tokens, output_tokens, model) or (None, 0, 0, "unknown") if no more questions
        """
        try:
            return self.openai_service.select_next_question(conversation_history, remaining_questions, answered_questions)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.select_next_question: {str(e)}")
            # Fallback to first remaining question if available
            if remaining_questions:
                return remaining_questions[0], 0, 0, "unknown"
            return None, 0, 0, "unknown"

    def detect_conversation_termination(self, user_message, conversation_context=""):
        """
        Detect if the user wants to end the conversation

        Args:
            user_message: The user's latest message
            conversation_context: Recent conversation context for better understanding

        Returns:
            Tuple of (wants_to_end: bool, input_tokens: int, output_tokens: int, model: str)
        """
        try:
            return self.openai_service.detect_conversation_termination(user_message, conversation_context)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.detect_conversation_termination: {str(e)}")
            # Conservative fallback - assume they don't want to end unless very obvious
            obvious_endings = ["bye", "goodbye", "thanks that's all", "no thanks", "that's all"]
            wants_to_end = any(ending in user_message.lower() for ending in obvious_endings)
            return wants_to_end, 0, 0, "unknown"

    def generate_farewell_message(self, user_message, conversation_summary=""):
        """
        Generate a polite farewell message when user wants to end the conversation

        Args:
            user_message: The user's termination message
            conversation_summary: Summary of what was discussed

        Returns:
            Tuple of (farewell_message: str, input_tokens: int, output_tokens: int, model: str)
        """
        try:
            return self.openai_service.generate_farewell_message(user_message, conversation_summary)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.generate_farewell_message: {str(e)}")
            return "Thank you for your time! Have a wonderful day and feel free to reach out anytime you need assistance.", 0, 0, "unknown"

    def generate_transition_message(self, answers):
        """
        Generate a transition message after the last question is answered

        Args:
            answers: List of question-answer pairs collected during the conversation

        Returns:
            A transition message thanking the user and asking if they have any specific queries
        """
        try:
            return self.openai_service.generate_transition_message(answers)
        except Exception as e:
            logger.error(f"Error in ElasticsearchService.generate_transition_message: {str(e)}")
            return "Thank you so much for taking the time to provide all that information! I really appreciate your cooperation. Is there anything else I can help you with today? Feel free to ask me any questions you might have!"

    def search_knowledgebase_and_generate_response(self, chatbot_id, tenant_id, user_message, conversation_history, db: Session = None):
        """
        Search the knowledgebase and generate a response based on the search results

        Args:
            chatbot_id: The chatbot ID to search within
            tenant_id: The tenant ID
            user_message: The user's message/query
            conversation_history: The conversation history for context
            db: Database session for index management

        Returns:
            Tuple of (ai_response: str, input_tokens: int, output_tokens: int, model: str)
        """
        try:
            # Perform semantic search with chatbot ID filtering using master index strategy
            search_results = self.semantic_search(
                tenant_id=tenant_id,
                query=user_message,
                chatbot_id=chatbot_id,
                limit=3,
                db=db
            )

            if not search_results:
                # No relevant results found
                return "I couldn't find a clear answer to your question based on the information I have. Could you please rephrase your question or provide more details?", 0, 0, "unknown"

            # Sort results by score and merge context
            search_results.sort(key=lambda x: x["score"], reverse=True)
            merged_context = "".join([result['content'] for result in search_results])
            merged_context = re.sub(r"\s+", " ", merged_context).strip()

            # Prepare prompt for response generation
            prompt = [
                {
                    "role": "system",
                    "content": f"""
                    You are a helpful and knowledgeable assistant for answering user questions based only on the provided context,
                    and reply in short summary with 5 bullet points
                    """
                },
                {
                    "role": "user",
                    "content": f"""
                        Context:{merged_context}
                        Question:{user_message}
                        """
                }
            ]

            # Generate response using the context
            ai_response, input_tokens, output_tokens, model = self.generate_chat_response(prompt, max_tokens=600)

            # Check if the response indicates the AI couldn't understand the question
            if "couldn't understand your question" in ai_response or "please rephrase" in ai_response.lower():
                ai_response = "I couldn't find a clear answer to your question based on the information I have. Could you please rephrase your question or provide more details?"

            return ai_response, input_tokens, output_tokens, model

        except Exception as e:
            logger.error(f"Error in search_knowledgebase_and_generate_response: {str(e)}")
            return "I encountered an error while searching for information. Please try asking your question again.", 0, 0, "unknown"

    def delete_document(self, tenant_id, document_id):
        """
        Delete a document from Elasticsearch
        
        Args:
            tenant_id: The tenant ID
            document_id: The document ID
        
        Returns:
            Boolean indicating if the document was deleted
        """
        try:
            # Create index name with tenant ID
            index_name = f"{self.index_prefix}{tenant_id}"
            
            # Check if index exists
            if not self.es_client.indices.exists(index=index_name):
                logger.warning(f"Index {index_name} does not exist")
                return False
            
            # Delete document
            response = self.es_client.delete(
                index=index_name,
                id=document_id,
                ignore=[404]  # Ignore if document doesn't exist
            )
            
            # Check if document was deleted
            if response.get("result") == "deleted":
                logger.info(f"Deleted document from Elasticsearch: {document_id}")
                return True
            else:
                logger.warning(f"Document not found in Elasticsearch: {document_id}")
                return False
        except Exception as e:
            logger.error(f"Error deleting document from Elasticsearch: {str(e)}")
            return False

    def clean_text_for_embedding(self, text):
        """
        Clean and normalize text before generating embeddings
        
        Args:
            text: The raw text extracted from a document
        
        Returns:
            Cleaned and normalized text
        """
        if not text:
            return ""
        
        try:
            # Replace multiple newlines with a single newline
            cleaned_text = re.sub(r'\n{2,}', '\n', text)
            
            # Replace multiple spaces with a single space
            cleaned_text = re.sub(r'\s{2,}', ' ', cleaned_text)
            
            # Remove page numbers (common in PDFs)
            cleaned_text = re.sub(r'\b\d+\s*\|\s*Page\b', '', cleaned_text)
            cleaned_text = re.sub(r'\bPage\s*\d+\s*of\s*\d+\b', '', cleaned_text)
            
            # Remove header/footer markers
            cleaned_text = re.sub(r'^\s*header\s*:.*$', '', cleaned_text, flags=re.MULTILINE | re.IGNORECASE)
            cleaned_text = re.sub(r'^\s*footer\s*:.*$', '', cleaned_text, flags=re.MULTILINE | re.IGNORECASE)
            
            # Remove common PDF artifacts
            cleaned_text = re.sub(r'Form\s+\d+', '', cleaned_text)
            
            # Remove URLs (optional, depending on your use case)
            cleaned_text = re.sub(r'https?://\S+', '', cleaned_text)
            
            # Remove email addresses (optional, depending on your use case)
            cleaned_text = re.sub(r'\S+@\S+\.\S+', '', cleaned_text)
            
            cleaned_text = re.sub(r'[•●▪▶–]', '', cleaned_text)

            # Normalize whitespace
            cleaned_text = cleaned_text.strip()
            
            logger.info(f"Cleaned text for embedding: reduced from {len(text)} to {len(cleaned_text)} characters")
            return cleaned_text
        except Exception as e:
            logger.error(f"Error cleaning text for embedding: {str(e)}")
            # Return original text if cleaning fails
            return text

    def chunk_document(self, text, max_tokens=40000000, overlap_tokens=50):
        """
        Split document text into chunks based on token count and sentence boundaries
        with specified overlap between chunks. Ensures the last chunk covers the end
        of the document properly.
        
        Args:
            text: The document text to chunk
            max_tokens: Maximum number of tokens per chunk
            overlap_tokens: Number of tokens to overlap between chunks
            
        Returns:
            List of text chunks
        """
        if not text:
            return []
        
        try:
            logger.info(f"Chunking document: length={len(text)}, max_tokens={max_tokens}, overlap_tokens={overlap_tokens}")
            
            # Split text into sentences
            sentences = re.split(r'(?<=[.!?])\s+', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if not sentences:
                return [text] if text else []
            
            logger.info(f"Document split into {len(sentences)} sentences")
            
            # Calculate token count for each sentence
            sentence_tokens = [self.count_tokens(s) for s in sentences]
            
            # Handle special case: if document is small enough to fit in one chunk
            total_tokens = sum(sentence_tokens)
            if total_tokens <= max_tokens:
                return [text]
            
            # Create chunks based on token count with overlap
            chunks = []
            
            # First, create chunks from the beginning of the document
            current_chunk = []
            current_tokens = 0
            
            for i, sentence in enumerate(sentences):
                tokens = sentence_tokens[i]
                
                # If adding this sentence would exceed max_tokens, finalize the chunk
                if current_tokens + tokens > max_tokens and current_chunk:
                    chunks.append(" ".join(current_chunk))
                    
                    # Start a new chunk with overlap
                    # Find sentences to keep for overlap
                    overlap_sentences = []
                    overlap_token_count = 0
                    
                    for s in reversed(current_chunk):
                        s_tokens = self.count_tokens(s)
                        if overlap_token_count + s_tokens <= overlap_tokens:
                            overlap_sentences.insert(0, s)
                            overlap_token_count += s_tokens
                        else:
                            break
                    
                    current_chunk = overlap_sentences
                    current_tokens = overlap_token_count
                
                # Handle sentences that are too long by themselves
                if tokens > max_tokens:
                    # If we have a current chunk, finalize it
                    if current_chunk:
                        chunks.append(" ".join(current_chunk))
                        current_chunk = []
                        current_tokens = 0
                    
                    # Split the long sentence into smaller parts
                    logger.warning(f"Long sentence found ({tokens} tokens). Splitting further.")
                    
                    # Try to split by punctuation first
                    sub_sentences = re.split(r'(?<=[,;:])\s+', sentence)
                    sub_chunks = []
                    
                    for sub in sub_sentences:
                        sub_tokens = self.count_tokens(sub)
                        
                        if sub_tokens <= max_tokens:
                            sub_chunks.append(sub)
                        else:
                            # If still too long, split by character chunks
                            for j in range(0, len(sub), max_tokens * 4):  # Approx 4 chars per token
                                sub_chunk = sub[j:j + max_tokens * 4]
                                if sub_chunk:
                                    sub_chunks.append(sub_chunk)
                    
                    # Add the sub-chunks with overlap
                    for j, sub_chunk in enumerate(sub_chunks):
                        if j > 0:
                            # Calculate overlap with previous sub-chunk
                            prev_sub = sub_chunks[j-1]
                            overlap_chars = min(len(prev_sub), overlap_tokens * 4)
                            if overlap_chars > 0:
                                sub_chunk = prev_sub[-overlap_chars:] + " " + sub_chunk
                    
                    chunks.append(sub_chunk)
                    
                    # Continue with an empty chunk
                    continue
                
                # Add the sentence to the current chunk
                current_chunk.append(sentence)
                current_tokens += tokens
            
            # Add the final chunk if it has content
            if current_chunk:
                chunks.append(" ".join(current_chunk))
            
            # Now, ensure the last chunk covers the end of the document properly
            # Create a special last chunk that starts from (end - max_tokens + overlap_tokens)
            
            # First, determine how many tokens we need from the end
            end_tokens = min(max_tokens, total_tokens)
            
            # Find the sentences that make up the last chunk
            last_chunk_sentences = []
            last_chunk_tokens = 0
            
            for s, tokens in zip(reversed(sentences), reversed(sentence_tokens)):
                if last_chunk_tokens + tokens <= end_tokens:
                    last_chunk_sentences.insert(0, s)
                    last_chunk_tokens += tokens
                else:
                    # If we can't add the whole sentence, we might need to split it
                    remaining_tokens = end_tokens - last_chunk_tokens
                    if remaining_tokens > overlap_tokens:
                        # Try to include part of this sentence to reach our target
                        # This is approximate since we can't easily split sentences by tokens
                        chars_per_token = len(s) / tokens
                        approx_chars = int(remaining_tokens * chars_per_token)
                        
                        if approx_chars > 10:  # Only if we can get a meaningful fragment
                            fragment = s[-approx_chars:]
                            last_chunk_sentences.insert(0, "..." + fragment)
                    
                    break
            
            # Create the last chunk
            if last_chunk_sentences:
                last_chunk = " ".join(last_chunk_sentences)
                
                # Check if this last chunk is significantly different from the existing last chunk
                if chunks and self.count_tokens(last_chunk) > 0:
                    existing_last = chunks[-1]
                    # Calculate similarity (simple approach: check if they share a significant portion)
                    if len(last_chunk) > len(existing_last) * 0.8:
                        # If they're very similar, replace the existing last chunk
                        chunks[-1] = last_chunk
                        logger.info("Replaced last chunk with end-focused chunk")
                    else:
                        # If they're different enough, add as a new chunk
                        chunks.append(last_chunk)
                        logger.info("Added special end-focused chunk")
            
            # Log chunking results
            if chunks:
                chunk_sizes = [len(chunk) for chunk in chunks]
                chunk_tokens = [self.count_tokens(chunk) for chunk in chunks]
                logger.info(f"Document chunked into {len(chunks)} chunks with overlap of {overlap_tokens} tokens")
                logger.info(f"Chunk sizes (chars): min={min(chunk_sizes)}, max={max(chunk_sizes)}, avg={sum(chunk_sizes)/len(chunks):.0f}")
                logger.info(f"Chunk sizes (tokens): min={min(chunk_tokens)}, max={max(chunk_tokens)}, avg={sum(chunk_tokens)/len(chunks):.0f}")
                logger.info(f"Last chunk token count: {self.count_tokens(chunks[-1])}")
            
            return chunks
        except Exception as e:
            logger.error(f"Error chunking document: {str(e)}", exc_info=True)
            # Return the whole text as a single chunk if chunking fails
            return [text]
