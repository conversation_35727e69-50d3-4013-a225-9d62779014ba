"""
Message Event Listener Service

This service handles incoming user messages from the ex.message exchange
and processes them through the conversation flow.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from aio_pika.abc import AbstractIncomingMessage


from app.database import get_db
from app.services.rabbitmq_service import rabbitmq_service
from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.redis_service import RedisService
from app.services.elasticsearch_service import ElasticsearchService
from app.services.charge_calculator import charge_calculator
from app.services.chatbot_service import ChatbotService
from app.models import ChatbotConversation
from app.utils.conversation_state_utils import (
    migrate_conversation_state,
    update_conversation_in_db,
    store_conversation_turn,
    track_credit_usage
)

logger = logging.getLogger(__name__)


class MessageEventListener:
    """
    Listener for incoming user messages in the WhatsApp chatbot system
    """
    
    def __init__(self):
        self.queue_name = "q.message.chatbot.user.response.chatbot"
        self.routing_key = "message.chatbot.user.response"
        self.exchange_name = "ex.message"
        self.is_running = False
    
    async def start(self):
        """
        Start the message event listener
        """
        if self.is_running:
            logger.warning("Message event listener is already running")
            return
        
        try:
            logger.info("Starting message event listener...")
            
            # Connect to RabbitMQ
            connected = await rabbitmq_service.connect()
            if not connected:
                raise RuntimeError("Failed to connect to RabbitMQ")
            
            # Setup the message listener
            await rabbitmq_service.setup_message_listener()

            # Setup publishers for sending conversation responses
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()

            # Register our event handler with the RabbitMQ service
            rabbitmq_service.register_event_handler(
                self.routing_key,
                self.handle_user_message_event
            )

            # Start consuming messages
            await rabbitmq_service.start_consuming(self.queue_name)

            self.is_running = True
            logger.info("Message event listener started successfully")

        except Exception as e:
            logger.error(f"Failed to start message event listener: {str(e)}")
            self.is_running = False
            raise
    
    async def stop(self):
        """
        Stop the message event listener
        """
        if not self.is_running:
            logger.warning("Message event listener is not running")
            return
        
        try:
            logger.info("Stopping message event listener...")
            
            # Stop consuming from the queue
            if self.queue_name in rabbitmq_service.is_consuming:
                rabbitmq_service.is_consuming[self.queue_name] = False
            
            self.is_running = False
            logger.info("Message event listener stopped successfully")

        except Exception as e:
            logger.error(f"Failed to stop message event listener: {str(e)}")
            raise
    
    async def handle_user_message_event(self, payload: Dict[str, Any], message: AbstractIncomingMessage):
        """
        Handle incoming user message events
        
        Args:
            payload: Event payload containing user message and conversation ID
            message: RabbitMQ message object
        """
        try:
            logger.info("🎯 HANDLING USER MESSAGE EVENT")
            logger.info(f"🎯 EVENT HANDLER - Routing Key: {message.routing_key}")
            logger.info(f"🎯 EVENT HANDLER - Exchange: {message.exchange}")

            # Log payload details
            logger.info(f"🎯 EVENT PAYLOAD - Full Payload: {payload}")

            # Extract message data
            user_message = payload.get("message", "")
            conversation_id = payload.get("chatbotConversationId")

            logger.info(f"🎯 EXTRACTED DATA - User Message: '{user_message}'")
            logger.info(f"🎯 EXTRACTED DATA - Conversation ID: {conversation_id}")

            if not user_message:
                logger.error("❌ VALIDATION ERROR - No message content in payload")
                return

            # Handle new conversation (conversation_id is None)
            if not conversation_id:
                logger.info("ℹ️ NEW CONVERSATION - Message for new conversation should be handled by start conversation API")
                return

            # Process existing conversation
            logger.info(f"🔄 PROCESSING CONVERSATION - ID: {conversation_id}")
            await self._process_conversation_message(conversation_id, user_message)

            logger.info("✅ USER MESSAGE EVENT PROCESSED SUCCESSFULLY")

        except Exception as e:
            logger.error(f"Error processing user message event: {str(e)}", exc_info=True)
            raise
    
    async def _process_conversation_message(self, conversation_id: str, user_message: str):
        """
        Process a message for an existing conversation
        
        Args:
            conversation_id: UUID of the conversation
            user_message: Message from the user
        """
        try:
            # Get conversation state from Redis
            redis_service = RedisService()
            state = redis_service.get_conversation_state(conversation_id)

            if not state:
                logger.error(f"Conversation {conversation_id} not found or expired")
                return

            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)
            
            try:
                # Get conversation from database
                conversation = db.query(ChatbotConversation).filter(
                    ChatbotConversation.id == conversation_id
                ).first()
                
                if not conversation:
                    logger.error(f"Conversation {conversation_id} not found in database")
                    return
                
                # Process the conversation using existing logic
                await self._continue_conversation_logic(
                    db, conversation, state, user_message, conversation_id
                )
                
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error processing conversation message: {str(e)}")
            raise
    
    async def _continue_conversation_logic(
        self,
        db: Session,
        conversation: ChatbotConversation,
        state: Dict[str, Any],
        user_message: str,
        conversation_id: str
    ):
        """
        Continue conversation logic (extracted from existing continue_conversation endpoint)

        Args:
            db: Database session
            conversation: ChatbotConversation object
            state: Conversation state from Redis
            user_message: User's message
            conversation_id: Conversation UUID
        """
        try:
            # Migrate old state format to new format for backward compatibility
            state = migrate_conversation_state(state)

            # Update conversation history
            state["history"].append({"role": "user", "content": user_message})

            # Initialize ElasticsearchService
            es_service = ElasticsearchService()

            # Get tenant_id for token usage tracking
            tenant_id = state.get("tenant_id", "unknown")

            # Check if conversation is already completed (in knowledge search phase)
            if state.get("completed", False):
                await self._handle_knowledge_phase(
                    db, state, user_message, conversation_id, tenant_id, es_service
                )
                return

            # Process regular question-answer flow
            await self._handle_question_phase(
                db, state, user_message, conversation_id, tenant_id, es_service
            )

        except Exception as e:
            logger.error(f"Error in continue conversation logic: {str(e)}")
            raise

    async def _handle_knowledge_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        user_message: str,
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle conversation in knowledge search phase (after all questions answered)
        """
        try:
            logger.info("Processing message in knowledge phase")

            # Check if user wants to end the conversation
            conversation_context = ""
            if state.get("history"):
                recent_messages = state["history"][-4:]
                conversation_context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])

            wants_to_end, termination_input_tokens, termination_output_tokens, termination_model = es_service.detect_conversation_termination(
                user_message,
                conversation_context
            )

            if wants_to_end:
                # Generate farewell message
                conversation_summary = ""
                if state.get("answers"):
                    conversation_summary = f"We collected information about: {', '.join([ans.get('question', '') for ans in state['answers']])}"

                farewell, input_tokens, output_tokens, model = es_service.generate_farewell_message(
                    user_message,
                    conversation_summary
                )

                # Store conversation turn
                store_conversation_turn(
                    db, conversation_id, tenant_id,
                    llm_prompt=[{"role": "user", "content": user_message}],
                    llm_response=farewell,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens
                )

                # Update state
                state["history"].append({"role": "assistant", "content": farewell})
                state["ended"] = True
                redis_service = RedisService()
                redis_service.store_conversation_state(conversation_id, state)

                # Update conversation in database
                update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)

                # Publish completion event (user-driven completion)
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message=farewell,
                    charge=0
                )
                return

            # Try to answer from knowledgebase
            chatbot_service = ChatbotService()
            has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], tenant_id)

            if has_knowledgebase:
                # Search knowledgebase for answer
                ai_response, input_tokens, output_tokens, model = es_service.search_knowledgebase_and_generate_response(
                    state["chatbot_id"],
                    tenant_id,
                    user_message,
                    state["history"],
                    db
                )

                if ai_response and ai_response.strip():
                    # Add a follow-up question to encourage user to indicate when they're done
                    follow_up_options = [
                        "Is there anything else I can help you with?",
                        "Do you have any other questions?",
                        "What else would you like to know?",
                        "Is there anything else you'd like to ask about?"
                    ]

                    import random
                    follow_up = random.choice(follow_up_options)
                    complete_response = f"{ai_response}\n\n{follow_up}"

                    # Store conversation turn
                    prompt = [{"role": "user", "content": user_message}]
                    store_conversation_turn(
                        db, conversation_id, tenant_id,
                        llm_prompt=prompt,
                        llm_response=complete_response,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens
                    )

                    # Update state
                    state["history"].append({"role": "assistant", "content": complete_response})
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database
                    update_conversation_in_db(db, conversation_id, state)

                    # Publish knowledge response event
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=complete_response,
                        completed=False,  # Still in knowledge phase, not completed until user indicates they're done
                        charge=0  # No charge for knowledge responses
                    )
                    return

            # No answer found in knowledgebase
            base_response = "I don't have specific information about that."
            follow_up_options = [
                "Is there anything else I can help you with?",
                "Do you have any other questions?",
                "What else would you like to know?",
                "Is there anything else you'd like to ask about?"
            ]

            import random
            follow_up = random.choice(follow_up_options)
            ai_response = f"{base_response} {follow_up}"

            # Store conversation turn
            no_info_prompt = [{"role": "user", "content": user_message}]
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=no_info_prompt,
                llm_response=ai_response,
                input_tokens=0,
                output_tokens=0
            )

            # Update state
            state["history"].append({"role": "assistant", "content": ai_response})
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            # Publish no-info response event
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=ai_response,
                completed=False,  # Still in knowledge phase, not completed until user indicates they're done
                charge=0
            )

        except Exception as e:
            logger.error(f"Error handling knowledge phase: {str(e)}")
            raise

    async def _handle_question_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        user_message: str,
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle conversation in question-answer phase
        """
        try:
            logger.info("Processing message in question phase")

            # Store the user's answer for the current question
            if state.get("asked_questions"):
                current_question = state["asked_questions"][-1]  # Last asked question

                # Handle both lead and contact fields - create separate entries if both exist
                if "answers" not in state:
                    state["answers"] = []

                answers_to_add = []

                # Add lead field answer if exists
                if current_question.get("lead_field_id") and current_question.get("lead_field_display_name"):
                    answers_to_add.append({
                        "question_id": current_question["id"],
                        "question": current_question["question"],
                        "answer": user_message,
                        "field_id": current_question["lead_field_id"],
                        "field_name": current_question["lead_field_display_name"].lower().replace(" ", "_"),
                        "entity_type": "LEAD",
                        "is_standard_field": current_question.get("is_lead_field_standard", False)
                    })

                # Add contact field answer if exists
                if current_question.get("contact_field_id") and current_question.get("contact_field_display_name"):
                    answers_to_add.append({
                        "question_id": current_question["id"],
                        "question": current_question["question"],
                        "answer": user_message,
                        "field_id": current_question["contact_field_id"],
                        "field_name": current_question["contact_field_display_name"].lower().replace(" ", "_"),
                        "entity_type": "CONTACT",
                        "is_standard_field": current_question.get("is_contact_field_standard", False)
                    })

                # If no specific fields, create a generic answer
                if not answers_to_add:
                    answers_to_add.append({
                        "question_id": current_question["id"],
                        "question": current_question["question"],
                        "answer": user_message,
                        "field_id": None,
                        "field_name": None,
                        "entity_type": None
                    })

                # Add all answers to state
                for answer in answers_to_add:
                    state["answers"].append(answer)

                # Track credit usage for this question-answer interaction
                chatbot_service = ChatbotService()
                has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], tenant_id)

                track_credit_usage(
                    db=db,
                    chatbot_id=state["chatbot_id"],
                    conversation_id=conversation_id,
                    tenant_id=tenant_id,
                    question=current_question["question"],
                    answer=user_message,
                    has_knowledgebase=has_knowledgebase
                )

            # Check if we have more questions remaining
            if state.get("remaining_questions"):
                # Use LLM to select the next question
                next_question, input_tokens, output_tokens, model = es_service.select_next_question(
                    state["history"],
                    state["remaining_questions"],
                    state["answers"]
                )

                if next_question:
                    # Move the selected question from remaining to asked
                    state["remaining_questions"] = [q for q in state["remaining_questions"] if q["id"] != next_question["id"]]
                    state["asked_questions"].append(next_question)

                    # Create a prompt for OpenAI to generate a natural transition
                    prompt = [
                        {"role": "system", "content": "You are a helpful assistant collecting information from users. Based on the conversation history, acknowledge the user's response and ask the next question in a natural way."},
                        {"role": "user", "content": f"Conversation history: {json.dumps(state['answers'])}\n\nNext question to ask: {next_question['question']}\n\nGenerate a natural response that acknowledges what the user just said and then asks the next question."}
                    ]

                    ai_response, transition_input_tokens, transition_output_tokens, transition_model = es_service.generate_chat_response(prompt, max_tokens=150)

                    # Store conversation turn
                    store_conversation_turn(
                        db, conversation_id, tenant_id,
                        llm_prompt=prompt,
                        llm_response=ai_response,
                        input_tokens=transition_input_tokens,
                        output_tokens=transition_output_tokens
                    )

                    # Update state
                    state["history"].append({"role": "assistant", "content": ai_response})
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database
                    update_conversation_in_db(db, conversation_id, state)

                    # Calculate charge for the next question
                    charge = charge_calculator.calculate_question_charge(
                        next_question, is_predefined=True, is_llm_generated=False
                    )

                    # Publish next question event
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=ai_response,
                        completed=False,
                        charge=charge
                    )
                    return

            # All predefined questions answered - check if we need custom follow-up questions
            await self._handle_custom_questions_phase(
                db, state, conversation_id, tenant_id, es_service
            )

        except Exception as e:
            logger.error(f"Error handling question phase: {str(e)}")
            raise

    async def _handle_custom_questions_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle generation and asking of custom follow-up questions based on user responses
        """
        try:
            logger.info("Checking if custom follow-up questions are needed")

            # Check if we've already generated custom questions for this conversation
            if state.get("custom_questions_generated", False):
                # Custom questions already processed, proceed to completion
                await self._handle_completion_phase(
                    db, state, conversation_id, tenant_id, es_service
                )
                return

            # Generate custom follow-up questions based on user responses
            custom_questions = await self._generate_custom_questions(
                state.get("answers", []), es_service
            )

            if custom_questions:
                # Add custom questions to remaining questions
                state["remaining_questions"].extend(custom_questions)
                state["custom_questions_generated"] = True

                # Store updated state
                redis_service = RedisService()
                redis_service.store_conversation_state(conversation_id, state)

                # Select and ask the first custom question
                next_question, input_tokens, output_tokens, model = es_service.select_next_question(
                    state["history"],
                    state["remaining_questions"],
                    state["answers"]
                )

                if next_question:
                    # Move the selected question from remaining to asked
                    state["remaining_questions"] = [q for q in state["remaining_questions"] if q["id"] != next_question["id"]]
                    state["asked_questions"].append(next_question)

                    # Create a prompt for OpenAI to generate a natural transition
                    prompt = [
                        {"role": "system", "content": "You are a helpful assistant collecting information from users. Based on the conversation history, acknowledge the user's response and ask a follow-up question in a natural way."},
                        {"role": "user", "content": f"Conversation history: {json.dumps(state['answers'])}\n\nFollow-up question to ask: {next_question['question']}\n\nGenerate a natural response that acknowledges what the user has shared and then asks the follow-up question."}
                    ]

                    ai_response, transition_input_tokens, transition_output_tokens, transition_model = es_service.generate_chat_response(prompt, max_tokens=150)

                    # Store conversation turn
                    store_conversation_turn(
                        db, conversation_id, tenant_id,
                        llm_prompt=prompt,
                        llm_response=ai_response,
                        input_tokens=transition_input_tokens,
                        output_tokens=transition_output_tokens
                    )

                    # Update state
                    state["history"].append({"role": "assistant", "content": ai_response})
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database
                    update_conversation_in_db(db, conversation_id, state)

                    # Calculate charge for the custom question
                    charge = charge_calculator.calculate_question_charge(
                        next_question, is_predefined=False, is_llm_generated=True
                    )

                    # Publish next question event
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=ai_response,
                        completed=False,
                        charge=charge
                    )
                    return

            # No custom questions needed or generated, proceed to completion
            state["custom_questions_generated"] = True
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)

            await self._handle_completion_phase(
                db, state, conversation_id, tenant_id, es_service
            )

        except Exception as e:
            logger.error(f"Error handling custom questions phase: {str(e)}")
            # Fallback to completion phase
            await self._handle_completion_phase(
                db, state, conversation_id, tenant_id, es_service
            )

    async def _generate_custom_questions(
        self,
        answers: List[Dict[str, Any]],
        es_service: ElasticsearchService
    ) -> List[Dict[str, Any]]:
        """
        Generate custom follow-up questions based on user responses
        """
        try:
            if not answers:
                return []

            # Create context from answered questions
            answered_context = ""
            for qa in answers:
                answered_context += f"Q: {qa.get('question', '')}\nA: {qa.get('answer', '')}\n\n"

            # Create a prompt for generating follow-up questions
            prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful assistant that generates relevant follow-up questions based on user responses.

Your task is to:
1. Analyze the user's answers to identify areas that need clarification or could benefit from more detail
2. Generate 1-2 relevant follow-up questions that would help gather more useful information
3. Only generate questions if the answers suggest there's valuable follow-up information to collect
4. Focus on questions that would help better understand the user's needs or situation

Respond with a JSON array of questions in this format:
[
  {"question": "Follow-up question 1"},
  {"question": "Follow-up question 2"}
]

If no follow-up questions are needed, respond with an empty array: []"""
                },
                {
                    "role": "user",
                    "content": f"""Based on these answered questions, determine if any follow-up questions would be valuable:

{answered_context}

Generate relevant follow-up questions (maximum 2) or return empty array if none are needed."""
                }
            ]

            response_text, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=300)

            # Parse the JSON response
            import json
            import uuid
            try:
                questions_data = json.loads(response_text.strip())
                if not isinstance(questions_data, list):
                    return []

                custom_questions = []
                for i, q_data in enumerate(questions_data[:2]):  # Limit to 2 questions
                    if isinstance(q_data, dict) and "question" in q_data:
                        custom_questions.append({
                            "id": str(uuid.uuid4()),
                            "question": q_data["question"],
                            "is_llm_generated": True,
                            "is_predefined": False,
                            "field_name": None,
                            "entity_type": None
                        })

                logger.info(f"Generated {len(custom_questions)} custom follow-up questions")
                return custom_questions

            except json.JSONDecodeError:
                logger.warning(f"Could not parse custom questions response: {response_text}")
                return []

        except Exception as e:
            logger.error(f"Error generating custom questions: {str(e)}")
            return []

    async def _handle_completion_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle conversation completion phase
        """
        try:
            logger.info("Processing conversation completion")

            # Check if chatbot has knowledgebase to determine conversation flow
            chatbot_service = ChatbotService()
            has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], tenant_id)

            if has_knowledgebase:
                # Transition to knowledge search phase
                prompt = [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant. The user has completed answering all the required questions. Now transition them to a knowledge search phase where they can ask questions about the topic."
                    },
                    {
                        "role": "user",
                        "content": f"""The user has completed answering these questions:
                        {json.dumps([ans.get('question', '') for ans in state.get('answers', [])])}

                        Generate a natural transition message that:
                        1. Thanks them for providing the information
                        2. Lets them know they can now ask questions about the topic
                        3. Encourages them to ask anything they'd like to know"""
                    }
                ]

                transition_message, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=150)
                completed = True  # Mark as completed but not ended (knowledge phase)
            else:
                # No knowledgebase - end conversation with thank you message
                chatbot = db.query(ChatbotConversation).filter(
                    ChatbotConversation.id == conversation_id
                ).first()

                thank_you_message = "Thank you for providing all the information!"
                if chatbot and hasattr(chatbot, 'thank_you_message') and chatbot.thank_you_message:
                    thank_you_message = chatbot.thank_you_message

                prompt = [
                    {
                        "role": "user",
                        "content": f"""Please enhance this thank you message for the end of conversation:

                        Original message: "{thank_you_message}"

                        Context: The user has completed all questions and there's no knowledgebase for further assistance, so this ends the conversation."""
                    }
                ]

                transition_message, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=100)
                completed = True

            # Store conversation turn
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=prompt,
                llm_response=transition_message,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

            # Update state to indicate transition to knowledge phase (but not completed yet)
            if has_knowledgebase:
                state["completed"] = True  # Transition to knowledge phase
                state["questions_completed"] = True  # Mark questions as done
            else:
                state["completed"] = True  # End conversation immediately
                state["ended"] = True

            state["history"].append({"role": "assistant", "content": transition_message})
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database - do NOT mark as completed yet if has knowledgebase
            if has_knowledgebase:
                update_conversation_in_db(db, conversation_id, state, completed=False)  # Not completed yet - waiting for user to indicate they're done
            else:
                update_conversation_in_db(db, conversation_id, state, completed=True)  # Complete immediately

            # Publish transition event (NOT completion yet)
            if has_knowledgebase:
                # Transition to knowledge phase - do NOT mark as completed yet
                await conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=transition_message,
                    completed=False,  # NOT completed yet - will be completed when user indicates they're done
                    charge=0
                )
            else:
                # End conversation immediately if no knowledgebase
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message=transition_message,
                    charge=0
                )

        except Exception as e:
            logger.error(f"Error handling completion phase: {str(e)}")
            raise




# Global instance
message_event_listener = MessageEventListener()
