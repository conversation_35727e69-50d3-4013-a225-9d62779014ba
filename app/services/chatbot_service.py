import logging
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, UploadFile
from io import BytesIO
import PyPDF2
from app.database import get_db
from app.models import (
    Chatbot,
    Cha<PERSON>botQuest<PERSON>,
    ChatbotKnowledgebase,
    ChatbotConversation,
    Document,
    ChatbotCreate,
    ChatbotUpdate,
    QuestionCreate,
    QuestionUpdate
)
from app.services.rabbitmq_service import rabbitmq_service
from app.services.elasticsearch_service import ElasticsearchService
from app.services.s3_service import S3Service
from app.services.user_service import user_service

logger = logging.getLogger(__name__)

# File upload validation constants
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
MAX_FILE_COUNT = 2  # Maximum number of files per upload


class ChatbotService:
    """
    Service class for chatbot-related operations including usage collection
    """
    
    def __init__(self):
        """
        Initialize the ChatbotService
        """
        logger.info("Initialized ChatbotService")
    
    async def collect_and_publish_chatbot_usage(self):
        """
        Collect chatbot usage data and publish it to RabbitMQ
        
        This method:
        1. Queries the database for non-DRAFT chatbots grouped by tenant
        2. Formats the data for usage reporting
        3. Publishes the usage data to the RabbitMQ exchange
        """
        db = None
        try:
            logger.info("Starting chatbot usage collection...")
            
            db = next(get_db())
            
            # Get total non-Draft status chatbots by tenantId
            usage_data = (
                db.query(Chatbot.tenant_id, func.count(Chatbot.id))
                .filter(Chatbot.status != "DRAFT")
                .group_by(Chatbot.tenant_id)
                .all()
            )
            
            # Format the payload for usage reporting
            payload = [
                {
                    "tenantId": tenant_id,
                    "usageEntity": "CHATBOT",
                    "count": count
                }
                for tenant_id, count in usage_data
            ]
            
            # Publish the usage data if there's any data to publish
            if payload:
                await rabbitmq_service.publish_message(
                    "ex.usage",
                    "usage.collect.response",
                    payload
                )
                logger.info(f"Published chatbot usage data for {len(payload)} tenants: {payload}")
            else:
                logger.info("No chatbot usage data to publish.")
            
            logger.info("Chatbot usage collection completed successfully")
            
        except Exception as e:
            logger.error(f"Error collecting chatbot usage: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_count_by_tenant(self, tenant_id: int, include_draft: bool = False) -> int:
        """
        Get the count of chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID to get chatbot count for
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            int: Number of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            count = query.scalar()
            
            logger.info(f"Chatbot count for tenant {tenant_id}: {count} (include_draft: {include_draft})")
            return count
            
        except Exception as e:
            logger.error(f"Error getting chatbot count for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_all_tenant_chatbot_counts(self, include_draft: bool = False) -> List[Dict[str, Any]]:
        """
        Get chatbot counts for all tenants
        
        Args:
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            List[Dict]: List of dictionaries containing tenant_id and count
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            results = query.all()
            
            tenant_counts = [
                {
                    "tenant_id": tenant_id,
                    "count": count
                }
                for tenant_id, count in results
            ]
            
            logger.info(f"Retrieved chatbot counts for {len(tenant_counts)} tenants (include_draft: {include_draft})")
            return tenant_counts
            
        except Exception as e:
            logger.error(f"Error getting chatbot counts for all tenants: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_by_id(self, chatbot_id: str, tenant_id: int) -> Optional[Chatbot]:
        """
        Get a chatbot by ID and tenant ID
        
        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            
        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())
            
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if chatbot:
                logger.info(f"Found chatbot {chatbot_id} for tenant {tenant_id}")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")
            
            return chatbot
            
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbots_by_tenant(self, tenant_id: int, include_draft: bool = True) -> List[Chatbot]:
        """
        Get all chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots
            
        Returns:
            List[Chatbot]: List of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            chatbots = query.all()
            
            logger.info(f"Found {len(chatbots)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")
            return chatbots
            
        except Exception as e:
            logger.error(f"Error getting chatbots for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_statistics(self, tenant_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get comprehensive chatbot statistics
        
        Args:
            tenant_id: Optional tenant ID to filter statistics for a specific tenant
            
        Returns:
            Dict[str, Any]: Dictionary containing various chatbot statistics
        """
        db = None
        try:
            db = next(get_db())
            
            stats = {}
            
            if tenant_id:
                # Statistics for a specific tenant
                total_query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
                active_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "ACTIVE"
                )
                draft_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "DRAFT"
                )
                
                stats = {
                    "tenant_id": tenant_id,
                    "total_chatbots": total_query.scalar(),
                    "active_chatbots": active_query.scalar(),
                    "draft_chatbots": draft_query.scalar()
                }
            else:
                # Global statistics
                total_chatbots = db.query(func.count(Chatbot.id)).scalar()
                active_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "ACTIVE").scalar()
                draft_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "DRAFT").scalar()
                inactive_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "INACTIVE").scalar()
                
                # Count by tenant
                tenant_counts = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id).all()
                
                stats = {
                    "total_chatbots": total_chatbots,
                    "active_chatbots": active_chatbots,
                    "draft_chatbots": draft_chatbots,
                    "inactive_chatbots": inactive_chatbots,
                    "total_tenants": len(tenant_counts),
                    "tenant_breakdown": [
                        {"tenant_id": tenant_id, "count": count}
                        for tenant_id, count in tenant_counts
                    ]
                }
            
            logger.info(f"Generated chatbot statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting chatbot statistics: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    # CRUD Operations

    async def create_chatbot(self, chatbot_data: ChatbotCreate, tenant_id: int, user_id: str, token: str) -> Dict[str, Any]:
        """
        Create a new chatbot

        Args:
            chatbot_data: ChatbotCreate model with chatbot information
            tenant_id: The tenant ID
            user_id: The user ID creating the chatbot
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Created chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Validate type value
            chatbot_type = chatbot_data.type.upper()
            if chatbot_type not in ["AI", "RULE"]:
                raise HTTPException(status_code=400, detail="Chatbot type must be either 'AI' or 'RULE'")

            # Generate unique chatbot ID
            chatbot_id = str(uuid.uuid4())

            # Extract connected account information (account-based approach)
            connected_account_display_name = None
            connected_account_id = None

            if chatbot_data.connectedAccount:
                connected_account_display_name = chatbot_data.connectedAccount.displayName
                connected_account_id = chatbot_data.connectedAccount.accountId

            # Validate that connected account is provided for account-based chatbots
            if not connected_account_id:
                raise HTTPException(status_code=400, detail="Connected account ID is required for chatbot creation")

            # Validate trigger value if provided
            trigger = None
            if chatbot_data.trigger:
                if chatbot_data.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
                    raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")
                trigger = chatbot_data.trigger

            # Create chatbot with account-based approach (no entity type but keep trigger)
            chatbot = Chatbot(
                id=chatbot_id,
                tenant_id=tenant_id,
                name=chatbot_data.name,
                type=chatbot_type,
                description=chatbot_data.description or "",
                welcome_message=chatbot_data.welcomeMessage,
                thank_you_message=chatbot_data.thankYouMessage,
                connected_account_display_name=connected_account_display_name,
                connected_account_id=connected_account_id,
                trigger=trigger,  # Keep trigger field for chatbot behavior
                created_by=user_id,  # Track who created the chatbot
                updated_by=user_id,  # Initially same as created_by
                status="DRAFT"  # Initial status is DRAFT until fully configured
            )

            # Add chatbot to database
            db.add(chatbot)
            db.commit()
            db.refresh(chatbot)

            logger.info(f"Created chatbot {chatbot_id} for tenant {tenant_id}")

            # Publish chatbot created event (which includes DRAFT status)
            if chatbot.connected_account_id and chatbot.connected_account_display_name:
                try:
                    from app.services.chatbot_event_publisher import ChatbotEventPublisher
                    event_publisher = ChatbotEventPublisher()

                    # Ensure exchange exists
                    await event_publisher.ensure_exchange_exists()

                    # Publish the status updated event for DRAFT status
                    await event_publisher.publish_status_updated_event(
                        status="DRAFT",
                        connected_account_id=chatbot.connected_account_id,
                        connected_account_name=chatbot.connected_account_display_name,
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id
                    )
                except Exception as e:
                    logger.error(f"Failed to publish chatbot created event for chatbot {chatbot_id}: {str(e)}")
                    # Don't fail the creation if event publishing fails

            # Build connected account response (account-based approach)
            connected_account = None
            if (chatbot.connected_account_display_name or chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                    # Removed entityType - not needed for account-based chatbots
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,  # Keep trigger field in response
                "status": chatbot.status,
                "created_at": chatbot.created_at
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error creating chatbot: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def update_chatbot(self, chatbot_id: str, chatbot_data: ChatbotUpdate, tenant_id: int, user_id: str, token: str) -> Dict[str, Any]:
        """
        Update an existing chatbot

        Args:
            chatbot_id: The chatbot ID to update
            chatbot_data: ChatbotUpdate model with updated information
            tenant_id: The tenant ID
            user_id: The user ID updating the chatbot
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Updated chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Validate knowledgebase IDs if provided
            if chatbot_data.knowledgebase_ids is not None:
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb = db.query(Document).filter(
                        Document.id == kb_id,
                        Document.tenant_id == tenant_id
                    ).first()
                    if not kb:
                        raise HTTPException(status_code=404, detail=f"Knowledgebase with ID {kb_id} not found")

            # Update chatbot fields if provided
            if chatbot_data.name is not None:
                chatbot.name = chatbot_data.name

            if chatbot_data.description is not None:
                chatbot.description = chatbot_data.description

            if chatbot_data.welcomeMessage is not None:
                chatbot.welcome_message = chatbot_data.welcomeMessage

            if chatbot_data.thankYouMessage is not None:
                chatbot.thank_you_message = chatbot_data.thankYouMessage

            if chatbot_data.connectedAccount is not None:
                chatbot.connected_account_display_name = chatbot_data.connectedAccount.displayName
                chatbot.connected_account_id = chatbot_data.connectedAccount.accountId
                # No longer setting entity_type - not needed for account-based chatbots

            if chatbot_data.trigger is not None:
                if chatbot_data.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
                    raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")
                chatbot.trigger = chatbot_data.trigger

            # Update questions if provided
            if chatbot_data.questions is not None:
                # Delete existing questions
                db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).delete()

                # Add new questions
                for q_data in chatbot_data.questions:
                    # Debug logging to see what values we're receiving
                    logger.info(f"Creating question in chatbot creation with data: question='{q_data.question}', "
                               f"leadFieldId={q_data.leadFieldId}, leadFieldDisplayName='{q_data.leadFieldDisplayName}', "
                               f"isLeadFieldStandard={q_data.isLeadFieldStandard} (type: {type(q_data.isLeadFieldStandard)}), "
                               f"contactFieldId={q_data.contactFieldId}, contactFieldDisplayName='{q_data.contactFieldDisplayName}', "
                               f"isContactFieldStandard={q_data.isContactFieldStandard} (type: {type(q_data.isContactFieldStandard)})")

                    # Set default values for standard field indicators if None
                    lead_standard = q_data.isLeadFieldStandard
                    if lead_standard is None and q_data.leadFieldId is not None:
                        lead_standard = False  # Default to False if not specified

                    contact_standard = q_data.isContactFieldStandard
                    if contact_standard is None and q_data.contactFieldId is not None:
                        contact_standard = False  # Default to False if not specified

                    question = ChatbotQuestion(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id,
                        question=q_data.question,
                        position=q_data.position,
                        lead_field_id=q_data.leadFieldId,
                        lead_field_display_name=q_data.leadFieldDisplayName,
                        is_lead_field_standard=lead_standard,
                        contact_field_id=q_data.contactFieldId,
                        contact_field_display_name=q_data.contactFieldDisplayName,
                        is_contact_field_standard=contact_standard
                    )
                    db.add(question)

            # Update knowledgebase associations if provided
            if chatbot_data.knowledgebase_ids is not None:
                # Delete existing associations
                db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot_id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).delete()

                # Add new associations
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=kb_id,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

            # Update the updated_by field to track who made the changes
            chatbot.updated_by = user_id

            # Commit changes
            db.commit()
            db.refresh(chatbot)

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Updated chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response (account-based approach)
            connected_account = None
            if (chatbot.connected_account_display_name or chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                    # Removed entityType - not needed for account-based chatbots
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,  # Keep trigger field in response
                "questions": [{
                    "id": q.id,
                    "question": q.question,
                    "fieldId": q.field_id,
                    "displayName": q.display_name,
                    "entityType": q.entity_type,
                    "name": q.name,
                    "standard": q.standard
                } for q in questions],
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "updated_at": chatbot.updated_at
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_chatbot(self, chatbot_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Delete a chatbot and all its associated data

        Args:
            chatbot_id: The chatbot ID to delete
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Delete knowledgebase associations
            db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).delete()

            # Delete chatbot
            db.delete(chatbot)
            db.commit()

            logger.info(f"Deleted chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Chatbot deleted successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_with_details(self, chatbot_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Get a chatbot with all its details including questions and knowledgebase associations

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Chatbot details
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Retrieved chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response
            connected_account = None
            if (chatbot.connected_account_display_name or
                chatbot.entity_type or
                chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "entityType": chatbot.entity_type,
                    "accountId": chatbot.connected_account_id
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "status": chatbot.status,
                "questions": [{
                    "id": q.id,
                    "question": q.question,
                    "position": q.position,
                    "leadFieldId": q.lead_field_id,
                    "leadFieldDisplayName": q.lead_field_display_name,
                    "isLeadFieldStandard": q.is_lead_field_standard,
                    "contactFieldId": q.contact_field_id,
                    "contactFieldDisplayName": q.contact_field_display_name,
                    "isContactFieldStandard": q.is_contact_field_standard
                } for q in sorted(questions, key=lambda x: x.position)],
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "created_at": chatbot.created_at,
                "updated_at": chatbot.updated_at
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_question(self, chatbot_id: str, question_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Delete a specific question from a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to delete
            tenant_id: The tenant ID (integer)

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Delete question
            db.delete(question)
            db.commit()

            logger.info(f"Deleted question {question_id} from chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Question deleted successfully",
                "question_id": question_id,
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting question: {str(e)}")
        finally:
            if db:
                db.close()

    def configure_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: int, user_id: str, token: str) -> Dict[str, Any]:
        """
        Configure questions for a chatbot (replace all existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects
            tenant_id: The tenant ID
            user_id: The user ID configuring the questions
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Configuration result with questions
        """
        db = None
        try:
            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete existing questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Add new questions
            created_questions = []
            for q_data in questions:
                # Debug logging to see what values we're receiving
                logger.info(f"Creating question with data: question='{q_data.question}', "
                           f"leadFieldId={q_data.leadFieldId}, leadFieldDisplayName='{q_data.leadFieldDisplayName}', "
                           f"isLeadFieldStandard={q_data.isLeadFieldStandard} (type: {type(q_data.isLeadFieldStandard)}), "
                           f"contactFieldId={q_data.contactFieldId}, contactFieldDisplayName='{q_data.contactFieldDisplayName}', "
                           f"isContactFieldStandard={q_data.isContactFieldStandard} (type: {type(q_data.isContactFieldStandard)})")

                # Set default values for standard field indicators if None
                lead_standard = q_data.isLeadFieldStandard
                if lead_standard is None and q_data.leadFieldId is not None:
                    lead_standard = False  # Default to False if not specified

                contact_standard = q_data.isContactFieldStandard
                if contact_standard is None and q_data.contactFieldId is not None:
                    contact_standard = False  # Default to False if not specified

                question = ChatbotQuestion(
                    id=str(uuid.uuid4()),
                    chatbot_id=chatbot_id,
                    tenant_id=tenant_id,
                    question=q_data.question,
                    position=q_data.position,
                    lead_field_id=q_data.leadFieldId,
                    lead_field_display_name=q_data.leadFieldDisplayName,
                    is_lead_field_standard=lead_standard,
                    contact_field_id=q_data.contactFieldId,
                    contact_field_display_name=q_data.contactFieldDisplayName,
                    is_contact_field_standard=contact_standard
                )
                db.add(question)
                created_questions.append(question)

            # Update chatbot status to ACTIVE if it was DRAFT
            if chatbot.status == "DRAFT":
                chatbot.status = "ACTIVE"

            db.commit()

            # Refresh to get the created questions with their IDs
            for question in created_questions:
                db.refresh(question)

            logger.info(f"Configured {len(questions)} questions for chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Questions configured successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "questions": [{
                    "id": q.id,
                    "question": q.question,
                    "position": q.position,
                    "leadFieldId": q.lead_field_id,
                    "leadFieldDisplayName": q.lead_field_display_name,
                    "isLeadFieldStandard": q.is_lead_field_standard,
                    "contactFieldId": q.contact_field_id,
                    "contactFieldDisplayName": q.contact_field_display_name,
                    "isContactFieldStandard": q.is_contact_field_standard
                } for q in created_questions],
                "status": chatbot.status
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error configuring questions for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error configuring questions: {str(e)}")
        finally:
            if db:
                db.close()

    async def update_chatbot_status(
        self,
        chatbot_id: str,
        new_status: str,
        tenant_id: int,
        user_id: str,
        token: str
    ) -> Dict[str, Any]:
        """
        Update chatbot status and publish event

        Args:
            chatbot_id: The chatbot ID
            new_status: The new status (DRAFT, ACTIVE, INACTIVE)
            tenant_id: The tenant ID
            user_id: The user ID updating the status
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Updated chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Store old status for comparison
            old_status = chatbot.status

            # Update the status
            chatbot.status = new_status
            chatbot.updated_by = user_id

            # Commit the changes
            db.commit()
            db.refresh(chatbot)

            logger.info(f"Updated chatbot {chatbot_id} status from {old_status} to {new_status}")

            # Publish status updated event if connected account exists
            if chatbot.connected_account_id and chatbot.connected_account_display_name:
                try:
                    from app.services.chatbot_event_publisher import ChatbotEventPublisher
                    event_publisher = ChatbotEventPublisher()

                    # Ensure exchange exists
                    await event_publisher.ensure_exchange_exists()

                    # Publish the status updated event
                    await event_publisher.publish_status_updated_event(
                        status=new_status,
                        connected_account_id=chatbot.connected_account_id,
                        connected_account_name=chatbot.connected_account_display_name,
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id
                    )
                except Exception as e:
                    logger.error(f"Failed to publish status updated event for chatbot {chatbot_id}: {str(e)}")
                    # Don't fail the status update if event publishing fails

            # Build response
            connected_account = None
            if (chatbot.connected_account_display_name or chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                }

            return {
                "id": chatbot.id,
                "name": chatbot.name,
                "status": chatbot.status,
                "previousStatus": old_status,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "updated_at": chatbot.updated_at,
                "updated_by": user_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating chatbot status {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating chatbot status: {str(e)}")
        finally:
            if db:
                db.close()

    def list_chatbots(self, tenant_id: int, include_draft: bool = True) -> List[Dict[str, Any]]:
        """
        List all chatbots for a tenant

        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots

        Returns:
            List[Dict[str, Any]]: List of chatbots with basic information
        """
        db = None
        try:
            db = next(get_db())

            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)

            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")

            chatbots = query.all()

            # Get all unique user IDs for batch fetching
            user_ids = set()
            for chatbot in chatbots:
                if chatbot.created_by:
                    user_ids.add(chatbot.created_by)
                if chatbot.updated_by:
                    user_ids.add(chatbot.updated_by)

            # Fetch all users in one query
            users_dict = user_service.get_users_by_ids(list(user_ids), db) if user_ids else {}

            result = []
            for chatbot in chatbots:
                # Get question count for each chatbot
                question_count = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot.id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).count()

                # Get knowledgebase count for each chatbot
                kb_count = db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot.id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).count()

                # Get user information for createdBy and updatedBy
                created_by_user = users_dict.get(chatbot.created_by) if chatbot.created_by else None
                updated_by_user = users_dict.get(chatbot.updated_by) if chatbot.updated_by else None

                result.append({
                    "id": chatbot.id,
                    "name": chatbot.name,
                    "type": chatbot.type,
                    "description": chatbot.description,
                    "status": chatbot.status,
                    "welcomeMessage": chatbot.welcome_message,
                    "thankYouMessage": chatbot.thank_you_message,
                    "connectedAccountDisplayName": chatbot.connected_account_display_name,
                    "connectedAccountId": chatbot.connected_account_id,
                    "trigger": chatbot.trigger,  # Keep trigger field
                    # Removed entityType - not needed for account-based chatbots
                    "createdBy": {"id": created_by_user.id, "name": created_by_user.name} if created_by_user else None,
                    "updatedBy": {"id": updated_by_user.id, "name": updated_by_user.name} if updated_by_user else None,
                    "createdAt": chatbot.created_at,
                    "updatedAt": chatbot.updated_at,
                    "questionCount": question_count,
                    "knowledgebaseCount": kb_count
                })

            logger.info(f"Listed {len(result)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")

            return result

        except Exception as e:
            logger.error(f"Error listing chatbots for tenant {tenant_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error listing chatbots: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_for_conversation(self, chatbot_id: str, tenant_id: int) -> Optional[Chatbot]:
        """
        Get a chatbot for conversation purposes (used by conversation endpoints)

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if chatbot:
                logger.info(f"Retrieved chatbot {chatbot_id} for conversation")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for conversation: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def get_chatbot_questions_for_conversation(self, chatbot_id: str, tenant_id: int) -> List[ChatbotQuestion]:
        """
        Get chatbot questions for conversation purposes

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            List[ChatbotQuestion]: List of questions for the chatbot
        """
        db = None
        try:
            db = next(get_db())

            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).order_by(ChatbotQuestion.position).all()

            logger.info(f"Retrieved {len(questions)} questions for chatbot {chatbot_id}")

            return questions

        except Exception as e:
            logger.error(f"Error getting questions for chatbot {chatbot_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def find_chatbot_by_entity_and_trigger(self, entity_type: str, connected_account_id: int, trigger: str, tenant_id: int) -> Optional[Chatbot]:
        """
        Find chatbot by entity type, connected account ID, and trigger

        Args:
            entity_type: The entity type (e.g., 'LEAD')
            connected_account_id: The connected account ID
            trigger: The trigger ('NEW_ENTITY' or 'EXISTING_ENTITY')
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot model object or None if not found
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.entity_type == entity_type,
                Chatbot.connected_account_id == connected_account_id,
                Chatbot.trigger == trigger,
                Chatbot.tenant_id == tenant_id,
                Chatbot.status == "ACTIVE"
            ).first()

            if chatbot:
                logger.info(f"Found chatbot {chatbot.id} for entity_type={entity_type}, account_id={connected_account_id}, trigger={trigger}")
            else:
                logger.warning(f"No chatbot found for entity_type={entity_type}, account_id={connected_account_id}, trigger={trigger}, tenant={tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error finding chatbot by entity and trigger: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def find_chatbot_by_account(self, connected_account_id: int, tenant_id: int) -> Optional[Chatbot]:
        """
        Find chatbot by connected account ID only (new approach: one chatbot per account)

        Args:
            connected_account_id: The connected account ID
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot model object or None if not found
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.connected_account_id == connected_account_id,
                Chatbot.tenant_id == tenant_id,
                Chatbot.status == "ACTIVE"
            ).first()

            if chatbot:
                logger.info(f"Found chatbot {chatbot.id} for account_id={connected_account_id}")
            else:
                logger.warning(f"No chatbot found for account_id={connected_account_id}, tenant={tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error finding chatbot by account: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    async def update_entities_after_conversation(
        self,
        entity_details: List[Dict[str, Any]],
        collected_answers: List[Dict[str, Any]],
        tenant_id: int,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Update all entities with collected answers after conversation completion

        Args:
            entity_details: List of entities with id and entityType
            collected_answers: List of question-answer pairs collected during conversation
            tenant_id: The tenant ID
            user_id: The user ID for audit trail

        Returns:
            Dict with update results for each entity
        """
        update_results = {
            "successful_updates": [],
            "failed_updates": [],
            "total_entities": len(entity_details)
        }

        try:
            logger.info(f"Updating {len(entity_details)} entities with {len(collected_answers)} answers")

            for entity in entity_details:
                entity_id = entity.get("id")
                entity_type = entity.get("entityType")

                try:
                    # Filter answers that apply to this specific entity type
                    entity_update_data = {}
                    applicable_answers = []

                    for answer in collected_answers:
                        answer_entity_type = answer.get("entity_type")
                        field_name = answer.get("field_name")
                        field_value = answer.get("answer")

                        # Include answers that either:
                        # 1. Are specifically for this entity type, OR
                        # 2. Are generic (no entity_type specified) and have a field_name
                        if field_name and field_value and (
                            not answer_entity_type or
                            answer_entity_type == entity_type
                        ):
                            entity_update_data[field_name] = field_value
                            applicable_answers.append({
                                "question": answer.get("question"),
                                "field_name": field_name,
                                "value": field_value
                            })

                    if not entity_update_data:
                        logger.info(f"No applicable answers found for {entity_type} {entity_id}")
                        update_results["successful_updates"].append({
                            "entity_id": entity_id,
                            "entity_type": entity_type,
                            "updated_fields": [],
                            "status": "success",
                            "message": "No applicable fields to update"
                        })
                        continue

                    # TODO: Replace with actual entity update API call
                    # Example: await entity_service.update_entity(entity_id, entity_type, entity_update_data)

                    logger.info(f"Would update {entity_type} {entity_id} with data: {entity_update_data}")

                    update_results["successful_updates"].append({
                        "entity_id": entity_id,
                        "entity_type": entity_type,
                        "updated_fields": list(entity_update_data.keys()),
                        "field_updates": applicable_answers,
                        "status": "success"
                    })

                except Exception as entity_error:
                    logger.error(f"Failed to update {entity_type} {entity_id}: {str(entity_error)}")
                    update_results["failed_updates"].append({
                        "entity_id": entity_id,
                        "entity_type": entity_type,
                        "error": str(entity_error),
                        "status": "failed"
                    })

            logger.info(f"Entity updates completed: {len(update_results['successful_updates'])} successful, {len(update_results['failed_updates'])} failed")
            return update_results

        except Exception as e:
            logger.error(f"Error updating entities after conversation: {str(e)}")
            raise

    def get_conversations_by_entity(self, entity_id: int, entity_type: str, tenant_id: int) -> List[Dict[str, Any]]:
        """
        Get all conversations that involved a specific entity

        Args:
            entity_id: The entity ID to search for
            entity_type: The entity type to search for
            tenant_id: The tenant ID

        Returns:
            List of conversation records that involved this entity
        """
        db = None
        try:
            db = next(get_db())

            # Query conversations where entity_details contains the specified entity
            conversations = db.query(ChatbotConversation).filter(
                ChatbotConversation.tenant_id == tenant_id,
                ChatbotConversation.entity_details.isnot(None)
            ).all()

            # Filter conversations that contain the specific entity
            matching_conversations = []
            for conv in conversations:
                if conv.entity_details:
                    for entity in conv.entity_details:
                        if (entity.get("id") == entity_id and
                            entity.get("entityType") == entity_type):
                            matching_conversations.append({
                                "conversation_id": conv.id,
                                "chatbot_id": conv.chatbot_id,
                                "completed": conv.completed,
                                "entity_details": conv.entity_details,
                                "connected_account_id": conv.connected_account_id,
                                "connected_account_name": conv.connected_account_name,
                                "entity_update_status": conv.entity_update_status,
                                "created_at": conv.created_at.isoformat() if conv.created_at else None,
                                "updated_at": conv.updated_at.isoformat() if conv.updated_at else None
                            })
                            break

            logger.info(f"Found {len(matching_conversations)} conversations for {entity_type} {entity_id}")
            return matching_conversations

        except Exception as e:
            logger.error(f"Error getting conversations by entity: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def get_conversations_by_account(self, connected_account_id: int, tenant_id: int) -> List[Dict[str, Any]]:
        """
        Get all conversations for a specific connected account

        Args:
            connected_account_id: The connected account ID
            tenant_id: The tenant ID

        Returns:
            List of conversation records for this account
        """
        db = None
        try:
            db = next(get_db())

            conversations = db.query(ChatbotConversation).filter(
                ChatbotConversation.connected_account_id == connected_account_id,
                ChatbotConversation.tenant_id == tenant_id
            ).order_by(ChatbotConversation.created_at.desc()).all()

            result = []
            for conv in conversations:
                result.append({
                    "conversation_id": conv.id,
                    "chatbot_id": conv.chatbot_id,
                    "completed": conv.completed,
                    "entity_details": conv.entity_details,
                    "connected_account_id": conv.connected_account_id,
                    "connected_account_name": conv.connected_account_name,
                    "entity_update_status": conv.entity_update_status,
                    "created_at": conv.created_at.isoformat() if conv.created_at else None,
                    "updated_at": conv.updated_at.isoformat() if conv.updated_at else None
                })

            logger.info(f"Found {len(result)} conversations for account {connected_account_id}")
            return result

        except Exception as e:
            logger.error(f"Error getting conversations by account: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def has_knowledgebase(self, chatbot_id: str, tenant_id: int) -> bool:
        """
        Check if a chatbot has any knowledgebase documents

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            bool: True if chatbot has knowledgebase, False otherwise
        """
        db = None
        try:
            db = next(get_db())

            kb_count = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).count()

            logger.info(f"Chatbot {chatbot_id} has {kb_count} knowledgebase documents")
            return kb_count > 0

        except Exception as e:
            logger.error(f"Error checking knowledgebase for chatbot {chatbot_id}: {str(e)}")
            return False
        finally:
            if db:
                db.close()

    def update_all_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: int) -> Dict[str, Any]:
        """
        Update all questions for a chatbot (replaces existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Update result with questions
        """
        # This is the same as configure_chatbot_questions
        return self.configure_chatbot_questions(chatbot_id, questions, tenant_id)

    def update_chatbot_question(self, chatbot_id: str, question_id: str, question_data: QuestionUpdate, tenant_id: int) -> Dict[str, Any]:
        """
        Update a specific question for a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to update
            question_data: QuestionUpdate object with new data
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Updated question information
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Update question fields if provided
            if question_data.question is not None:
                question.question = question_data.question
            if question_data.position is not None:
                question.position = question_data.position
            if question_data.leadFieldId is not None:
                question.lead_field_id = question_data.leadFieldId
            if question_data.leadFieldDisplayName is not None:
                question.lead_field_display_name = question_data.leadFieldDisplayName
            if question_data.isLeadFieldStandard is not None:
                question.is_lead_field_standard = question_data.isLeadFieldStandard
            if question_data.contactFieldId is not None:
                question.contact_field_id = question_data.contactFieldId
            if question_data.contactFieldDisplayName is not None:
                question.contact_field_display_name = question_data.contactFieldDisplayName
            if question_data.isContactFieldStandard is not None:
                question.is_contact_field_standard = question_data.isContactFieldStandard

            db.commit()
            db.refresh(question)

            logger.info(f"Updated question {question_id} for chatbot {chatbot_id}")

            return {
                "message": "Question updated successfully",
                "question": {
                    "id": question.id,
                    "question": question.question,
                    "position": question.position,
                    "leadFieldId": question.lead_field_id,
                    "leadFieldDisplayName": question.lead_field_display_name,
                    "isLeadFieldStandard": question.is_lead_field_standard,
                    "contactFieldId": question.contact_field_id,
                    "contactFieldDisplayName": question.contact_field_display_name,
                    "isContactFieldStandard": question.is_contact_field_standard
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating question: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_knowledgebase(self, chatbot_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Get all knowledgebase documents for a chatbot

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: List of knowledgebase documents
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Get knowledgebase associations
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            # Get document details
            documents = []
            for kb in kb_assocs:
                document = db.query(Document).filter(
                    Document.id == kb.document_id,
                    Document.tenant_id == tenant_id
                ).first()
                if document:
                    documents.append({
                        "id": document.id,
                        "document_name": document.document_name,
                        "document_type": document.document_type,
                        "created_at": document.created_at,
                        "created_by": document.created_by
                    })

            logger.info(f"Retrieved {len(documents)} knowledgebase documents for chatbot {chatbot_id}")

            return {
                "chatbot_id": chatbot_id,
                "documents": documents,
                "total_count": len(documents)
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting knowledgebase for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting knowledgebase: {str(e)}")
        finally:
            if db:
                db.close()

    async def upload_multiple_knowledgebase_files(
        self,
        chatbot_id: str,
        files: List[UploadFile],
        tenant_id: int,
        user_id: str,
        token: str
    ) -> Dict[str, Any]:
        """
        Upload multiple files to chatbot knowledgebase

        Args:
            chatbot_id: The chatbot ID
            files: List of UploadFile objects
            tenant_id: The tenant ID
            user_id: The user ID for audit trail
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Upload results with success and failure details
        """
        db = None
        successful_uploads = []
        failed_uploads = []

        try:
            # Validate file count
            if len(files) > MAX_FILE_COUNT:
                raise HTTPException(
                    status_code=400,
                    detail=f"Maximum {MAX_FILE_COUNT} files allowed per upload. You uploaded {len(files)} files."
                )

            # Validate file sizes
            for file in files:
                if file.size and file.size > MAX_FILE_SIZE:
                    raise HTTPException(
                        status_code=400,
                        detail=f"File '{file.filename}' exceeds maximum size of 10MB. File size: {file.size / (1024*1024):.2f}MB"
                    )

            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Initialize services
            s3_service = S3Service()
            es_service = ElasticsearchService()

            # Process each file
            for file in files:
                file_result = {
                    "filename": file.filename,
                    "status": "processing"
                }

                try:
                    # Validate file type
                    if not file.filename.lower().endswith('.pdf'):
                        file_result.update({
                            "status": "failed",
                            "error": "Only PDF files are supported"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Read file content
                    file_data = await file.read()

                    if not file_data:
                        file_result.update({
                            "status": "failed",
                            "error": "File is empty"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Secondary file size check (in case file.size wasn't available)
                    if len(file_data) > MAX_FILE_SIZE:
                        file_result.update({
                            "status": "failed",
                            "error": f"File exceeds maximum size of 10MB. File size: {len(file_data) / (1024*1024):.2f}MB"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Extract text from PDF
                    pdf_content = await self._extract_pdf_text(file_data, file.filename)

                    # Generate unique document ID
                    document_id = str(uuid.uuid4())

                    # Upload file to S3
                    file_data_io = BytesIO(file_data)
                    s3_key = s3_service.upload_file(
                        file_data=file_data_io,
                        tenant_id=tenant_id,
                        chatbot_id=chatbot_id,
                        filename=file.filename
                    )

                    # Clean and index content in Elasticsearch using new master index strategy
                    cleaned_content = es_service.clean_text_for_embedding(pdf_content)
                    es_index = es_service.index_document(
                        tenant_id,
                        document_id,
                        cleaned_content,
                        chatbot_id,  # chatbot_id
                        300,  # chunk_size
                        30,   # overlap
                        db    # database session for master index strategy
                    )

                    # Store document metadata in PostgreSQL
                    document = Document(
                        id=document_id,
                        tenant_id=tenant_id,
                        document_name=file.filename,
                        document_type="pdf",
                        es_index=es_index,
                        es_document_id=document_id,
                        s3_key=s3_key,
                        created_by=user_id
                    )
                    db.add(document)

                    # Create association between chatbot and document
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=document_id,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

                    # Update file result with success
                    file_result.update({
                        "status": "success",
                        "document_id": document_id,
                        "s3_key": s3_key,
                        "es_index": es_index
                    })
                    successful_uploads.append(file_result)

                    logger.info(f"Successfully processed file {file.filename} for chatbot {chatbot_id}")

                except Exception as e:
                    logger.error(f"Error processing file {file.filename}: {str(e)}")
                    file_result.update({
                        "status": "failed",
                        "error": str(e)
                    })
                    failed_uploads.append(file_result)
                    continue

            # Update chatbot status if it was in DRAFT and has questions
            if chatbot.status == "DRAFT" and successful_uploads:
                question_count = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).count()

                if question_count > 0:
                    chatbot.status = "ACTIVE"
                    logger.info(f"Updated chatbot {chatbot_id} status to ACTIVE")

            # Commit all successful changes
            if successful_uploads:
                db.commit()
                logger.info(f"Committed {len(successful_uploads)} successful uploads for chatbot {chatbot_id}")

            return {
                "message": f"Processed {len(files)} files",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "total_files": len(files),
                "successful_uploads": len(successful_uploads),
                "failed_uploads": len(failed_uploads),
                "results": {
                    "successful": successful_uploads,
                    "failed": failed_uploads
                },
                "chatbot_status": chatbot.status
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error in multiple file upload for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing files: {str(e)}")
        finally:
            if db:
                db.close()

    def remove_knowledgebase_document(self, chatbot_id: str, document_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Remove a specific document from chatbot knowledgebase

        Args:
            chatbot_id: The chatbot ID
            document_id: The document ID to remove
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Removal result
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Check if document exists and is associated with this chatbot
            kb_assoc = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.document_id == document_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).first()

            if not kb_assoc:
                raise HTTPException(status_code=404, detail="Document not found in chatbot knowledgebase")

            # Get document details for cleanup
            document = db.query(Document).filter(
                Document.id == document_id,
                Document.tenant_id == tenant_id
            ).first()

            if not document:
                raise HTTPException(status_code=404, detail="Document not found")

            # Remove the association
            db.delete(kb_assoc)

            # Check if this document is used by other chatbots
            other_associations = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.document_id == document_id,
                ChatbotKnowledgebase.tenant_id == tenant_id,
                ChatbotKnowledgebase.chatbot_id != chatbot_id
            ).count()

            # If no other chatbots use this document, remove it completely
            if other_associations == 0:
                # TODO: Remove from Elasticsearch
                # TODO: Remove from S3
                # For now, just remove from database
                db.delete(document)
                logger.info(f"Removed document {document_id} completely as it's not used by other chatbots")
            else:
                logger.info(f"Document {document_id} is still used by {other_associations} other chatbots")

            db.commit()

            logger.info(f"Removed document {document_id} from chatbot {chatbot_id} knowledgebase")

            return {
                "message": "Document removed from knowledgebase successfully",
                "chatbot_id": chatbot_id,
                "document_id": document_id,
                "document_name": document.document_name,
                "completely_removed": other_associations == 0
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error removing document {document_id} from chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error removing document: {str(e)}")
        finally:
            if db:
                db.close()

    async def _extract_pdf_text(self, file_data: bytes, filename: str) -> str:
        """
        Extract text content from PDF file data

        Args:
            file_data: PDF file bytes
            filename: Original filename for error reporting

        Returns:
            str: Extracted text content
        """
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(file_data))
            pdf_content = ""

            # Extract text from each page
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                pdf_content += page.extract_text() + "\n\n"

            if not pdf_content.strip():
                raise ValueError("Could not extract text from PDF")

            return pdf_content

        except Exception as e:
            logger.error(f"Error extracting text from PDF {filename}: {str(e)}")
            raise ValueError(f"Error processing PDF {filename}: {str(e)}")
