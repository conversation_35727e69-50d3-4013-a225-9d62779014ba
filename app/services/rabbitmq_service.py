import asyncio
import json
import logging
import os
import random
import time
from typing import Optional, Callable, Dict, Any, List
from aio_pika import connect_robust, Connection, Channel, Exchange, Queue, Message
from aio_pika.abc import AbstractIncomingMessage
import aio_pika

logger = logging.getLogger(__name__)


class RabbitMQService:
    """
    RabbitMQ service for handling message queue operations
    """
    
    def __init__(self):
        self.connection: Optional[Connection] = None
        self.channel: Optional[Channel] = None
        self.exchanges: Dict[str, Exchange] = {}
        self.queues: Dict[str, Queue] = {}
        self.event_handlers: Dict[str, Callable] = {}
        self.consumer_tags: Dict[str, str] = {}
        self.is_consuming: Dict[str, bool] = {}
        self.last_heartbeat: Dict[str, float] = {}
        self.recovery_tasks: List[asyncio.Task] = []
        self.health_check_task: Optional[asyncio.Task] = None
        self.keepalive_task: Optional[asyncio.Task] = None
        self.is_shutting_down = False

        # RabbitMQ connection settings from environment
        self.rabbitmq_url = "amqp://sling_sales:test@rabbitmq:5672/"
        self.connection_timeout = int(os.getenv("RABBITMQ_CONNECTION_TIMEOUT", "30"))
        self.heartbeat = int(os.getenv("RABBITMQ_HEARTBEAT", "300"))  # 5 minutes
        self.consumer_timeout = int(os.getenv("RABBITMQ_CONSUMER_TIMEOUT", "0"))  # 0 = no timeout
        self.health_check_interval = int(os.getenv("RABBITMQ_HEALTH_CHECK_INTERVAL", "60"))  # 1 minute
        
    async def connect(self) -> bool:
        """
        Establish connection to RabbitMQ server
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to RabbitMQ at {self.rabbitmq_url}")

            self.connection = await connect_robust(
                self.rabbitmq_url,
                timeout=self.connection_timeout,
                heartbeat=self.heartbeat,
                client_properties={
                    "connection_name": f"{os.getenv('SERVICE_NAME', 'whatsapp-chatbot')}-{int(time.time())}"
                }
            )
            
            self.channel = await self.connection.channel()
            
            # Set QoS to process one message at a time
            await self.channel.set_qos(prefetch_count=1)

            # Start health check task
            if not self.health_check_task or self.health_check_task.done():
                self.health_check_task = asyncio.create_task(self._health_check_loop())

            # Start keepalive task
            if not self.keepalive_task or self.keepalive_task.done():
                self.keepalive_task = asyncio.create_task(self._keepalive_loop())

            logger.info("Successfully connected to RabbitMQ")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ: {str(e)}")
            return False
    
    async def disconnect(self):
        """
        Close RabbitMQ connection and cleanup
        """
        try:
            self.is_shutting_down = True

            # Cancel health check task
            if self.health_check_task and not self.health_check_task.done():
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass

            # Cancel keepalive task
            if self.keepalive_task and not self.keepalive_task.done():
                self.keepalive_task.cancel()
                try:
                    await self.keepalive_task
                except asyncio.CancelledError:
                    pass

            # Cancel recovery tasks
            for task in self.recovery_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # Cancel consumers using consumer tags
            for queue_name, consumer_tag in self.consumer_tags.items():
                try:
                    if consumer_tag and self.channel and not self.channel.is_closed:
                        await self.channel.basic_cancel(consumer_tag)
                        logger.info(f"Cancelled consumer for queue: {queue_name} (tag: {consumer_tag})")
                except Exception as e:
                    logger.error(f"Error cancelling consumer for {queue_name}: {str(e)}")

            # Close connection
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
                logger.info("RabbitMQ connection closed")

            # Clear state
            self.consumer_tags.clear()
            self.is_consuming.clear()
            self.last_heartbeat.clear()

        except Exception as e:
            logger.error(f"Error closing RabbitMQ connection: {str(e)}")

    async def _ensure_connection(self) -> bool:
        """
        Ensure RabbitMQ connection and channel are available and healthy

        Returns:
            bool: True if connection is healthy, False otherwise
        """
        try:
            # Check if connection and channel exist and are open
            if (self.connection and not self.connection.is_closed and
                self.channel and not self.channel.is_closed):
                return True

            logger.info("Connection or channel not available, attempting to reconnect...")

            # Clear existing state
            await self._clear_connection_state()

            # Attempt to reconnect
            return await self.connect()

        except Exception as e:
            logger.error(f"Error ensuring connection: {str(e)}")
            return False

    async def _clear_connection_state(self):
        """
        Clear connection state without closing (for when connection is already broken)
        """
        try:
            # Clear references without attempting to close (connection may already be broken)
            self.connection = None
            self.channel = None
            self.exchanges.clear()
            self.queues.clear()

            # Cancel background tasks
            if self.health_check_task and not self.health_check_task.done():
                self.health_check_task.cancel()

            if self.keepalive_task and not self.keepalive_task.done():
                self.keepalive_task.cancel()

        except Exception as e:
            logger.error(f"Error clearing connection state: {str(e)}")

    async def declare_exchange(self, exchange_name: str, exchange_type: str = "topic") -> Exchange:
        """
        Declare an exchange
        
        Args:
            exchange_name: Name of the exchange
            exchange_type: Type of exchange (topic, direct, fanout, headers)
            
        Returns:
            Exchange: The declared exchange
        """
        if not self.channel:
            raise RuntimeError("RabbitMQ channel not available. Call connect() first.")
        
        if exchange_name not in self.exchanges:
            exchange = await self.channel.declare_exchange(
                exchange_name,
                type=aio_pika.ExchangeType(exchange_type),
                durable=True
            )
            self.exchanges[exchange_name] = exchange
            logger.info(f"Declared exchange: {exchange_name} (type: {exchange_type})")
        
        return self.exchanges[exchange_name]
    
    async def declare_queue(self, queue_name: str, durable: bool = True) -> Queue:
        """
        Declare a queue
        
        Args:
            queue_name: Name of the queue
            durable: Whether the queue should survive server restarts
            
        Returns:
            Queue: The declared queue
        """
        if not self.channel:
            raise RuntimeError("RabbitMQ channel not available. Call connect() first.")
        
        if queue_name not in self.queues:
            queue = await self.channel.declare_queue(
                queue_name,
                durable=durable
            )
            self.queues[queue_name] = queue
            logger.info(f"Declared queue: {queue_name} (durable: {durable})")
        
        return self.queues[queue_name]
    
    async def bind_queue_to_exchange(
        self, 
        queue_name: str, 
        exchange_name: str, 
        routing_key: str
    ):
        """
        Bind a queue to an exchange with a routing key
        
        Args:
            queue_name: Name of the queue
            exchange_name: Name of the exchange
            routing_key: Routing key for binding
        """
        if queue_name not in self.queues:
            raise ValueError(f"Queue {queue_name} not declared")
        
        if exchange_name not in self.exchanges:
            raise ValueError(f"Exchange {exchange_name} not declared")
        
        queue = self.queues[queue_name]
        exchange = self.exchanges[exchange_name]
        
        await queue.bind(exchange, routing_key)
        logger.info(f"Bound queue {queue_name} to exchange {exchange_name} with routing key: {routing_key}")
    
    def register_event_handler(self, event_name: str, handler: Callable):
        """
        Register an event handler for a specific event
        
        Args:
            event_name: Name of the event to handle
            handler: Async function to handle the event
        """
        self.event_handlers[event_name] = handler
        logger.info(f"Registered event handler for: {event_name}")
    
    async def message_handler(self, message: AbstractIncomingMessage):
        """
        Generic message handler that routes messages to specific event handlers

        Args:
            message: Incoming RabbitMQ message
        """
        try:
            # Update heartbeat for all consuming queues
            current_time = time.time()
            for queue_name in self.is_consuming:
                if self.is_consuming[queue_name]:
                    self.last_heartbeat[queue_name] = current_time

            # Get routing key (event name)
            routing_key = message.routing_key

            # Enhanced logging for message reception
            logger.info(f"📥 RECEIVED MESSAGE - Routing Key: {routing_key}")
            logger.info(f"📥 RECEIVED MESSAGE - Exchange: {message.exchange}")
            logger.info(f"📥 RECEIVED MESSAGE - Payload Size: {len(message.body)} bytes")
            logger.info(f"📥 RECEIVED MESSAGE - Content Type: {message.content_type}")

            # Parse message body
            try:
                if message.body:
                    body = json.loads(message.body.decode())
                else:
                    body = {}
            except json.JSONDecodeError:
                logger.error(f"❌ JSON DECODE ERROR - Failed to parse message body: {message.body}")
                body = {}

            # Log payload with size-based truncation for readability
            if len(str(body)) > 1000:
                logger.info(f"📥 RECEIVED MESSAGE - Payload (truncated): {str(body)[:1000]}...")
                logger.debug(f"📥 RECEIVED MESSAGE - Full Payload: {body}")
            else:
                logger.info(f"📥 RECEIVED MESSAGE - Payload: {body}")

            # Route to specific handler
            if routing_key in self.event_handlers:
                handler = self.event_handlers[routing_key]
                logger.info(f"🔄 PROCESSING MESSAGE - Handler found for routing key: {routing_key}")

                # Track processing time
                start_time = time.time()

                await handler(body, message)

                processing_time = time.time() - start_time
                logger.info(f"✅ MESSAGE PROCESSED - Routing Key: {routing_key}, Processing Time: {processing_time:.3f}s")
            else:
                logger.warning(f"⚠️ NO HANDLER - No handler registered for routing key: {routing_key}")
                logger.warning(f"⚠️ NO HANDLER - Available handlers: {list(self.event_handlers.keys())}")

            # Acknowledge the message
            await message.ack()
            logger.debug(f"✅ MESSAGE ACKNOWLEDGED - Routing Key: {routing_key}")

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}", exc_info=True)
            # Reject the message and don't requeue it
            await message.reject(requeue=False)
    
    async def start_consuming(self, queue_name: str):
        """
        Start consuming messages from a queue with robust error handling

        Args:
            queue_name: Name of the queue to consume from
        """
        if queue_name not in self.queues:
            raise ValueError(f"Queue {queue_name} not declared")

        # Ensure we have a valid connection and channel
        if not await self._ensure_connection():
            raise RuntimeError("Failed to establish RabbitMQ connection")

        queue = self.queues[queue_name]

        try:
            # Check if already consuming
            if self.is_consuming.get(queue_name, False):
                logger.warning(f"Already consuming from queue {queue_name}")
                return

            # Create consumer with no timeout and exclusive=False
            consumer_tag = await queue.consume(
                self.message_handler,
                no_ack=False,
                exclusive=False,
                timeout=self.consumer_timeout if self.consumer_timeout > 0 else None
            )

            # Store consumer tag
            self.consumer_tags[queue_name] = consumer_tag
            self.is_consuming[queue_name] = True
            self.last_heartbeat[queue_name] = time.time()

            logger.info(f"Started consuming messages from queue: {queue_name} (consumer_tag: {consumer_tag})")

            # Set up consumer monitoring
            asyncio.create_task(self._monitor_consumer(queue_name))

        except Exception as e:
            logger.error(f"Failed to start consuming from queue {queue_name}: {str(e)}")
            self.is_consuming[queue_name] = False
            raise
    
    async def setup_scheduler_listener(self):
        """
        Set up listener for scheduler events on ex.scheduler exchange
        """
        try:
            # Declare the ex.scheduler exchange
            await self.declare_exchange("ex.scheduler", "topic")
            
            # Declare a queue for our service
            service_name = os.getenv("SERVICE_NAME", "whatsapp-chatbot")
            queue_name = f"{service_name}.scheduler.events"
            await self.declare_queue(queue_name, durable=True)
            
            # Bind queue to exchange for scheduler.collect.usage events
            await self.bind_queue_to_exchange(
                queue_name, 
                "ex.scheduler", 
                "scheduler.collect.usage"
            )
            
            # Register event handler for scheduler.collect.usage
            self.register_event_handler("scheduler.collect.usage", self.handle_collect_usage_event)
            
            # Start consuming messages
            await self.start_consuming(queue_name)
            
            logger.info("Scheduler event listener setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup scheduler listener: {str(e)}")
            raise
    
    async def handle_collect_usage_event(self, payload: Dict[str, Any], message: AbstractIncomingMessage):
        """
        Handle the scheduler.collect.usage event
        
        Args:
            payload: Event payload (empty {} for this event)
            message: RabbitMQ message object
        """
        try:
            logger.info("Received scheduler.collect.usage event")
            logger.debug(f"Event payload: {payload}")
            
            # Since the payload is empty {}, we just acknowledge receipt
            # In a real implementation, you might trigger usage collection here
            
            # Example: You could trigger usage collection, cleanup, or reporting
            # await self.collect_usage_data()
            # await self.cleanup_old_data()
            # await self.generate_usage_reports()
            
            logger.info("Successfully processed scheduler.collect.usage event")
            
        except Exception as e:
            logger.error(f"Error handling scheduler.collect.usage event: {str(e)}")
            raise

    async def _monitor_consumer(self, queue_name: str):
        """
        Monitor a specific consumer and restart if needed

        Args:
            queue_name: Name of the queue to monitor
        """
        while not self.is_shutting_down and self.is_consuming.get(queue_name, False):
            try:
                await asyncio.sleep(30)  # Check every 30 seconds

                # Check if consumer is still active
                consumer_tag = self.consumer_tags.get(queue_name)
                if not consumer_tag or not self.is_consuming.get(queue_name, False):
                    logger.warning(f"Consumer for queue {queue_name} is not active, attempting recovery")
                    try:
                        await self._recover_consumer(queue_name)
                    except Exception as recovery_error:
                        logger.error(f"Failed to recover consumer for {queue_name}: {recovery_error}")
                        # Wait longer before next attempt
                        await asyncio.sleep(60)
                else:
                    # Update heartbeat
                    self.last_heartbeat[queue_name] = time.time()
                    logger.debug(f"Consumer for queue {queue_name} is healthy (tag: {consumer_tag})")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring consumer for queue {queue_name}: {str(e)}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _recover_consumer(self, queue_name: str):
        """
        Recover a failed consumer

        Args:
            queue_name: Name of the queue to recover consumer for
        """
        try:
            logger.info(f"Attempting to recover consumer for queue: {queue_name}")

            # Mark as not consuming
            self.is_consuming[queue_name] = False

            # Remove old consumer tag
            if queue_name in self.consumer_tags:
                del self.consumer_tags[queue_name]

            # Wait a bit before recovery
            await asyncio.sleep(5)

            # Ensure connection is healthy using the robust method
            if not await self._ensure_connection():
                raise RuntimeError("Failed to establish RabbitMQ connection for recovery")

            # Re-declare the queue to ensure it exists
            if queue_name in self.queues:
                await self.declare_queue(queue_name, durable=True)

            # Restart consuming
            await self.start_consuming(queue_name)

            logger.info(f"Successfully recovered consumer for queue: {queue_name}")

        except Exception as e:
            logger.error(f"Failed to recover consumer for queue {queue_name}: {str(e)}")
            # Schedule retry
            recovery_task = asyncio.create_task(self._schedule_recovery_retry(queue_name))
            self.recovery_tasks.append(recovery_task)

    async def _schedule_recovery_retry(self, queue_name: str):
        """
        Schedule a retry for consumer recovery

        Args:
            queue_name: Name of the queue to retry recovery for
        """
        try:
            await asyncio.sleep(60)  # Wait 1 minute before retry
            if not self.is_shutting_down and not self.is_consuming.get(queue_name, False):
                logger.info(f"Retrying consumer recovery for queue: {queue_name}")
                await self._recover_consumer(queue_name)
        except Exception as e:
            logger.error(f"Error in recovery retry for queue {queue_name}: {str(e)}")

    async def _health_check_loop(self):
        """
        Periodic health check for all consumers
        """
        while not self.is_shutting_down:
            try:
                await asyncio.sleep(self.health_check_interval)

                if self.is_shutting_down:
                    break

                logger.debug("Performing consumer health check")

                for queue_name in list(self.is_consuming.keys()):
                    if self.is_consuming.get(queue_name, False):
                        consumer_tag = self.consumer_tags.get(queue_name)
                        last_heartbeat = self.last_heartbeat.get(queue_name, 0)

                        # Check if consumer tag exists
                        if not consumer_tag:
                            logger.warning(f"Health check: Consumer tag for {queue_name} is missing")
                            await self._recover_consumer(queue_name)
                            continue

                        # Check heartbeat age (if older than 10 minutes, consider it stale)
                        heartbeat_age = time.time() - last_heartbeat
                        if heartbeat_age > 600:  # 10 minutes
                            logger.warning(f"Health check: Consumer for {queue_name} heartbeat is stale ({heartbeat_age:.1f}s)")
                            await self._recover_consumer(queue_name)
                            continue

                        logger.debug(f"Health check: Consumer for {queue_name} is healthy (tag: {consumer_tag})")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {str(e)}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _keepalive_loop(self):
        """
        Send periodic keepalive signals to maintain connection
        """
        keepalive_interval = self.heartbeat // 3  # Send keepalive every 1/3 of heartbeat interval

        while not self.is_shutting_down:
            try:
                await asyncio.sleep(keepalive_interval)

                if self.is_shutting_down:
                    break

                # Check connection health
                if self.connection and not self.connection.is_closed:
                    try:
                        # Send a lightweight operation to keep connection alive
                        if self.channel and not self.channel.is_closed:
                            # Create a unique queue name to avoid conflicts in clustered environments
                            hostname = os.environ.get('HOSTNAME', 'unknown')
                            pid = os.getpid()
                            timestamp = int(time.time() * 1000000)  # Use microseconds for better uniqueness
                            random_suffix = random.randint(1000, 9999)
                            temp_queue_name = f"keepalive-{hostname}-{pid}-{timestamp}-{random_suffix}"

                            # Declare a temporary queue to test connection
                            temp_queue = await self.channel.declare_queue(
                                temp_queue_name,
                                durable=False,
                                auto_delete=True,
                                exclusive=True
                            )

                            # Small delay to ensure queue is properly created before deletion
                            await asyncio.sleep(0.1)

                            # Clean up the temporary queue
                            try:
                                await temp_queue.delete()
                            except Exception as delete_error:
                                logger.debug(f"Error deleting keepalive queue (may have auto-deleted): {delete_error}")

                            logger.debug("Keepalive signal sent successfully")
                        else:
                            logger.warning("Channel is closed, attempting to reconnect")
                            await self.connect()
                    except Exception as e:
                        logger.warning(f"Keepalive failed, attempting reconnection: {str(e)}")
                        await self.connect()
                else:
                    logger.warning("Connection is closed, attempting to reconnect")
                    await self.connect()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in keepalive loop: {str(e)}")
                await asyncio.sleep(60)  # Wait longer on error

    def get_consumer_status(self) -> Dict[str, Any]:
        """
        Get status information for all consumers

        Returns:
            Dict containing consumer status information
        """
        status = {
            "connection_status": "connected" if self.connection and not self.connection.is_closed else "disconnected",
            "channel_status": "open" if self.channel and not self.channel.is_closed else "closed",
            "consumers": {},
            "health_check_running": self.health_check_task and not self.health_check_task.done(),
            "keepalive_running": self.keepalive_task and not self.keepalive_task.done(),
            "is_shutting_down": self.is_shutting_down
        }

        current_time = time.time()
        for queue_name in self.is_consuming:
            consumer_tag = self.consumer_tags.get(queue_name)
            last_heartbeat = self.last_heartbeat.get(queue_name, 0)
            heartbeat_age = current_time - last_heartbeat

            status["consumers"][queue_name] = {
                "is_consuming": self.is_consuming.get(queue_name, False),
                "consumer_tag_exists": consumer_tag is not None,
                "consumer_tag": consumer_tag or "unknown",
                "last_heartbeat": last_heartbeat,
                "heartbeat_age_seconds": heartbeat_age,
                "status": "healthy" if (
                    self.is_consuming.get(queue_name, False) and
                    consumer_tag and
                    heartbeat_age < 600
                ) else "unhealthy"
            }

        return status

    async def force_consumer_recovery(self, queue_name: str = None):
        """
        Force recovery of consumers

        Args:
            queue_name: Specific queue to recover, or None for all queues
        """
        if queue_name:
            if queue_name in self.is_consuming:
                logger.info(f"Forcing recovery for consumer: {queue_name}")
                await self._recover_consumer(queue_name)
            else:
                logger.warning(f"Queue {queue_name} is not being consumed")
        else:
            logger.info("Forcing recovery for all consumers")
            for queue_name in list(self.is_consuming.keys()):
                await self._recover_consumer(queue_name)

    async def publish_message(
        self,
        exchange: str,
        routing_key: str,
        message: Any,
        durable: bool = True
    ):
        """
        Publish a message to an exchange with automatic reconnection

        Args:
            exchange: Exchange name
            routing_key: Routing key for the message
            message: Message payload (will be JSON serialized)
            durable: Whether the message should be persistent
        """
        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                # Check and ensure connection is available
                if not await self._ensure_connection():
                    raise RuntimeError("Failed to establish RabbitMQ connection")

                # Ensure exchange exists
                await self.declare_exchange(exchange)

                # Serialize message to JSON
                if isinstance(message, (dict, list)):
                    message_body = json.dumps(message)
                else:
                    message_body = str(message)

                # Enhanced logging for message publishing
                logger.info(f"📤 PUBLISHING MESSAGE - Exchange: {exchange}")
                logger.info(f"📤 PUBLISHING MESSAGE - Routing Key: {routing_key}")
                logger.info(f"📤 PUBLISHING MESSAGE - Payload Size: {len(message_body)} bytes")
                logger.info(f"📤 PUBLISHING MESSAGE - Durable: {durable}")

                # Log payload with size-based truncation for readability
                if len(message_body) > 1000:
                    logger.info(f"📤 PUBLISHING MESSAGE - Payload (truncated): {message_body[:1000]}...")
                    logger.debug(f"📤 PUBLISHING MESSAGE - Full Payload: {message_body}")
                else:
                    logger.info(f"📤 PUBLISHING MESSAGE - Payload: {message_body}")

                # Create message
                msg = Message(
                    message_body.encode(),
                    delivery_mode=2 if durable else 1,  # 2 = persistent, 1 = transient
                    content_type="application/json"
                )

                # Publish message
                publish_start_time = time.time()
                await self.exchanges[exchange].publish(msg, routing_key=routing_key)
                publish_time = time.time() - publish_start_time

                logger.info(f"✅ MESSAGE PUBLISHED - Exchange: {exchange}, Routing Key: {routing_key}, Publish Time: {publish_time:.3f}s")
                return  # Success, exit retry loop

            except Exception as e:
                logger.warning(f"Publish attempt {attempt + 1}/{max_retries} failed: {str(e)}")

                if attempt < max_retries - 1:
                    # Clear connection state to force reconnection
                    await self._clear_connection_state()
                    await asyncio.sleep(retry_delay * (attempt + 1))
                else:
                    logger.error(f"Failed to publish message after {max_retries} attempts: {str(e)}")
                    raise

    async def publish_event(self, event_name: str, payload: Any):
        """
        Publish an event to the default events exchange

        Args:
            event_name: Name of the event (used as routing key)
            payload: Event payload
        """
        try:
            # Use a default events exchange
            exchange_name = "ex.events"

            logger.info(f"🚀 PUBLISHING EVENT - Event Name: {event_name}")
            logger.info(f"🚀 PUBLISHING EVENT - Target Exchange: {exchange_name}")

            await self.publish_message(exchange_name, event_name, payload)

            logger.info(f"✅ EVENT PUBLISHED SUCCESSFULLY - Event: {event_name}")

        except Exception as e:
            logger.error(f"❌ EVENT PUBLISH FAILED - Event: {event_name}, Error: {str(e)}")
            raise

    async def setup_usage_publisher(self):
        """
        Set up publisher for usage events
        """
        try:
            # Declare the ex.usage exchange for publishing usage data
            await self.declare_exchange("ex.usage", "topic")
            logger.info("Usage publisher setup completed")

        except Exception as e:
            logger.error(f"Failed to setup usage publisher: {str(e)}")
            raise

    async def setup_events_publisher(self):
        """
        Set up publisher for general events
        """
        try:
            # Declare the ex.events exchange for publishing events
            await self.declare_exchange("ex.events", "topic")
            logger.info("Events publisher setup completed")

        except Exception as e:
            logger.error(f"Failed to setup events publisher: {str(e)}")
            raise

    async def setup_whatsapp_chatbot_publisher(self):
        """
        Set up publisher for WhatsApp chatbot conversation events
        """
        try:
            # Declare the ex.whatsappChatbot exchange for publishing conversation responses
            await self.declare_exchange("ex.whatsappChatbot", "topic")
            logger.info("WhatsApp chatbot publisher setup completed")

        except Exception as e:
            logger.error(f"Failed to setup WhatsApp chatbot publisher: {str(e)}")
            raise

    async def setup_message_listener(self):
        """
        Set up listener for incoming user messages from ex.message exchange
        """
        try:
            # Declare the ex.message exchange
            await self.declare_exchange("ex.message", "topic")

            # Declare the queue for chatbot user responses
            queue_name = "q.message.chatbot.user.response.chatbot"
            await self.declare_queue(queue_name, durable=True)

            # Bind queue to exchange for message.chatbot.user.response events
            await self.bind_queue_to_exchange(
                queue_name,
                "ex.message",
                "message.chatbot.user.response"
            )

            logger.info("Message listener setup completed")

        except Exception as e:
            logger.error(f"Failed to setup message listener: {str(e)}")
            raise

    async def setup_all_publishers(self):
        """
        Set up all required publishers
        """
        try:
            await self.setup_usage_publisher()
            await self.setup_events_publisher()
            await self.setup_whatsapp_chatbot_publisher()
            logger.info("All publishers setup completed")

        except Exception as e:
            logger.error(f"Failed to setup publishers: {str(e)}")
            raise

    async def is_healthy(self) -> bool:
        """
        Check if the RabbitMQ service is healthy

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            if not self.connection or self.connection.is_closed:
                return False

            if not self.channel or self.channel.is_closed:
                return False

            # Use a unique identifier with hostname, process ID, and microseconds to avoid conflicts
            hostname = os.environ.get('HOSTNAME', 'unknown')
            pid = os.getpid()
            timestamp = int(time.time() * 1000000)  # Use microseconds for better uniqueness
            random_suffix = random.randint(1000, 9999)

            # Create a truly unique queue name to avoid conflicts in clustered environments
            temp_queue_name = f"health-check-{hostname}-{pid}-{timestamp}-{random_suffix}"

            try:
                # Try to declare a temporary queue to verify channel is working
                temp_queue = await self.channel.declare_queue(
                    temp_queue_name,
                    durable=False,
                    auto_delete=True,
                    exclusive=True
                )

                # Delete immediately to avoid resource leaks
                await temp_queue.delete()
                return True

            except Exception as e:
                if "RESOURCE_LOCKED" in str(e):
                    # If queue is locked, it means another process has it
                    # This is still a valid health check (RabbitMQ is responding)
                    logger.info(f"Health check queue locked but RabbitMQ is responding: {str(e)}")
                    return True
                else:
                    # Other errors indicate a problem
                    raise

        except Exception as e:
            logger.warning(f"Health check failed: {str(e)}")
            return False


# Global RabbitMQ service instance
rabbitmq_service = RabbitMQService()
