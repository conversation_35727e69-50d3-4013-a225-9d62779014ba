"""
Conversation Event Publisher Service

This service handles publishing conversation events to the message queue system.
It publishes chatbot conversation responses to the ex.whatsappChatbot exchange.
"""

import json
import logging
from typing import Optional, Dict, Any
from app.services.rabbitmq_service import rabbitmq_service

logger = logging.getLogger(__name__)


class ConversationEventPublisher:
    """
    Publisher for conversation events in the WhatsApp chatbot system
    """
    
    def __init__(self):
        self.exchange_name = "ex.whatsappChatbot"
        self.routing_key = "chatbot.conversation.response"
    
    async def publish_conversation_response(
        self,
        chatbot_conversation_id: str,
        message: str,
        completed: bool = False,
        charge: int = 0
    ) -> bool:
        """
        Publish a conversation response event
        
        Args:
            chatbot_conversation_id: UUID of the conversation
            message: The response message from the chatbot
            completed: Whether the conversation is completed
            charge: Charge amount (1 for predefined questions, 2 for custom questions)
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare event payload
            event_payload = {
                "chatbotConversationId": chatbot_conversation_id,
                "message": message,
                "completed": completed,
                "charge": charge
            }
            
            # Publish the event
            success = await self._publish_event(
                routing_key=self.routing_key,
                payload=event_payload,
                event_type="conversation_response"
            )
            
            if success:
                logger.info(f"Successfully published conversation response event for conversation {chatbot_conversation_id}")
            else:
                logger.error(f"Failed to publish conversation response event for conversation {chatbot_conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error publishing conversation response event: {str(e)}")
            return False
    

    
    async def publish_conversation_completion(
        self,
        chatbot_conversation_id: str,
        completion_message: str,
        charge: int = 0
    ) -> bool:
        """
        Publish a conversation completion event
        
        Args:
            chatbot_conversation_id: UUID of the conversation
            completion_message: Final message to the user
            charge: Charge amount for the completion message
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            return await self.publish_conversation_response(
                chatbot_conversation_id=chatbot_conversation_id,
                message=completion_message,
                completed=True,
                charge=charge
            )
            
        except Exception as e:
            logger.error(f"Error publishing conversation completion event: {str(e)}")
            return False
    
    async def _publish_event(
        self,
        routing_key: str,
        payload: Dict[str, Any],
        event_type: str
    ) -> bool:
        """
        Internal method to publish events to RabbitMQ with retry logic

        Args:
            routing_key: Routing key for the message
            payload: Event payload
            event_type: Type of event for logging

        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            logger.info(f"💬 PUBLISHING CONVERSATION EVENT - Type: {event_type}")
            logger.info(f"💬 CONVERSATION EVENT - Routing Key: {routing_key}")
            logger.info(f"💬 CONVERSATION EVENT - Exchange: {self.exchange_name}")

            # Log payload details
            logger.info(f"💬 CONVERSATION EVENT - Payload Keys: {list(payload.keys()) if isinstance(payload, dict) else 'Not a dict'}")
            if isinstance(payload, dict):
                # Log important fields without exposing sensitive data
                safe_payload = {k: v for k, v in payload.items() if k not in ['token', 'password', 'secret']}
                logger.info(f"💬 CONVERSATION EVENT - Payload: {safe_payload}")
            else:
                logger.info(f"💬 CONVERSATION EVENT - Payload: {payload}")

            # Convert payload to JSON string
            message_body = json.dumps(payload, default=str)
            logger.info(f"💬 CONVERSATION EVENT - Serialized Size: {len(message_body)} bytes")

            # Publish the message (rabbitmq_service.publish_message now handles reconnection)
            await rabbitmq_service.publish_message(
                exchange=self.exchange_name,
                routing_key=routing_key,
                message=message_body,
                durable=True
            )

            logger.info(f"✅ CONVERSATION EVENT PUBLISHED - Type: {event_type}, Routing Key: {routing_key}")

            return True

        except Exception as e:
            logger.error(f"❌ CONVERSATION EVENT PUBLISH FAILED - Type: {event_type}, Error: {str(e)}")
            return False


# Global instance
conversation_event_publisher = ConversationEventPublisher()
