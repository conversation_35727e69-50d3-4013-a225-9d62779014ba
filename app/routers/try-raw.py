import openai
from openai import OpenAI

client = OpenAI(api_key='********************************************************************************************************************************************************************')

try:
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "system", "content": """You are an expert at detecting user intent. Your task is to determine if a user wants to end a conversation.\n\n                    Look for these termination indicators:\n                    - Explicit endings: "goodbye", "bye", "thanks, that\'s all", "no more questions", "I\'m done", "that\'s it"\n                    - Polite dismissals: "no thank you", "no thanks", "that\'s all I needed", "nothing else", "I\'m good"\n                    - Completion statements: "that\'s everything", "all set", "perfect", "got what I needed"\n                    - Gratitude with finality: "thank you for your help", "thanks for everything", "appreciate it"\n\n                    Do NOT consider these as termination:\n                    - New questions or requests for information\n                    - Requests for clarification\n                    - Follow-up questions\n                    - Expressions of confusion that need resolution\n\n                    Respond with ONLY "YES" if the user wants to end the conversation, or "NO" if they want to continue.\n                    Do not provide any explanation."""}, {"role": "user", "content": """'\n                                User\'s message: "no thanks"\n\n                                Does the user want to end the conversation? Respond with only YES or NO."""}],

        temperature=0.7,
        max_tokens=600,
    )
    print("RAW RESPONSE:")
    print(response.model_dump_json(indent=2))
    print("\nOUTPUT MESSAGE:")
    print(response.choices[0].message.content)
except Exception as e:
    print("Error:", str(e))
