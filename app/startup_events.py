"""
Startup Events for Event-Driven Conversation System

This module handles the initialization of the event-driven conversation system
including publishers, listeners, and RabbitMQ infrastructure.
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI

from app.services.rabbitmq_service import rabbitmq_service
from app.services.event_listeners import EventListenerManager

logger = logging.getLogger(__name__)


class EventDrivenStartup:
    """
    Manages startup and shutdown of event-driven conversation system
    """
    
    def __init__(self):
        self.event_listener_manager = EventListenerManager()
        self.is_initialized = False
    
    async def initialize(self):
        """
        Initialize the event-driven conversation system
        """
        if self.is_initialized:
            logger.warning("Event-driven system already initialized")
            return
        
        try:
            logger.info("Initializing event-driven conversation system...")
            
            # Step 1: Connect to RabbitMQ
            logger.info("Connecting to RabbitMQ...")
            connected = await rabbitmq_service.connect()
            if not connected:
                raise RuntimeError("Failed to connect to RabbitMQ")
            logger.info("✓ RabbitMQ connection established")
            
            # Step 2: Setup exchanges and queues
            logger.info("Setting up exchanges and queues...")
            await self._setup_infrastructure()
            logger.info("✓ Event infrastructure setup completed")
            
            # Step 3: Start event listeners
            logger.info("Starting event listeners...")
            await self.event_listener_manager.start_all()
            logger.info("✓ Event listeners started")
            
            self.is_initialized = True
            logger.info("🎉 Event-driven conversation system initialized successfully!")
            
        except Exception as e:
            logger.error(f"Failed to initialize event-driven system: {str(e)}")
            raise
    
    async def shutdown(self):
        """
        Shutdown the event-driven conversation system
        """
        if not self.is_initialized:
            logger.warning("Event-driven system not initialized")
            return
        
        try:
            logger.info("Shutting down event-driven conversation system...")
            
            # Step 1: Stop event listeners
            logger.info("Stopping event listeners...")
            await self.event_listener_manager.stop_all()
            logger.info("✓ Event listeners stopped")
            
            # Step 2: Disconnect from RabbitMQ
            logger.info("Disconnecting from RabbitMQ...")
            await rabbitmq_service.disconnect()
            logger.info("✓ RabbitMQ disconnected")
            
            self.is_initialized = False
            logger.info("✓ Event-driven conversation system shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
            raise
    
    async def _setup_infrastructure(self):
        """
        Setup RabbitMQ exchanges, queues, and bindings
        """
        try:
            # Setup publishers
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()
            await rabbitmq_service.setup_all_publishers()
            
            # Setup message listener infrastructure
            await rabbitmq_service.setup_message_listener()
            
            logger.info("Event infrastructure setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup event infrastructure: {str(e)}")
            raise
    
    async def health_check(self):
        """
        Perform health check on the event-driven system
        """
        try:
            # Check RabbitMQ connection
            if not rabbitmq_service.connection or rabbitmq_service.connection.is_closed:
                return {"status": "unhealthy", "reason": "RabbitMQ connection lost"}
            
            if not rabbitmq_service.channel or rabbitmq_service.channel.is_closed:
                return {"status": "unhealthy", "reason": "RabbitMQ channel lost"}
            
            # Check if listeners are running
            if not self.event_listener_manager.scheduler_listener.is_running:
                return {"status": "unhealthy", "reason": "Scheduler listener not running"}
            
            if not self.event_listener_manager.message_listener.is_running:
                return {"status": "unhealthy", "reason": "Message listener not running"}
            
            return {"status": "healthy", "message": "Event-driven system is operational"}
            
        except Exception as e:
            return {"status": "unhealthy", "reason": f"Health check failed: {str(e)}"}


# Global instance
event_driven_startup = EventDrivenStartup()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI lifespan context manager for event-driven system
    """
    # Startup
    try:
        await event_driven_startup.initialize()
        yield
    finally:
        # Shutdown
        await event_driven_startup.shutdown()


async def manual_startup():
    """
    Manual startup function for testing or standalone usage
    """
    await event_driven_startup.initialize()


async def manual_shutdown():
    """
    Manual shutdown function for testing or standalone usage
    """
    await event_driven_startup.shutdown()


def get_health_status():
    """
    Get current health status of the event-driven system
    """
    return asyncio.create_task(event_driven_startup.health_check())


# Health check endpoint helper
async def event_system_health():
    """
    Health check function for use in FastAPI health endpoints
    """
    return await event_driven_startup.health_check()


if __name__ == "__main__":
    """
    Standalone startup for testing
    """
    async def main():
        try:
            await manual_startup()
            logger.info("Event-driven system started. Press Ctrl+C to stop...")
            
            # Keep running until interrupted
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Shutdown requested...")
        finally:
            await manual_shutdown()
    
    asyncio.run(main())
