#!/usr/bin/env python3
"""
Test script for the Elasticsearch Master Index Strategy

This script demonstrates the new master index strategy by:
1. Creating sample tenant aliases
2. Indexing test documents
3. Performing searches
4. Showing statistics
"""

import os
import sys
import uuid
import logging

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.elasticsearch_service import ElasticsearchService
from app.services.elasticsearch_index_manager import ElasticsearchIndexManager
from app.database import get_db
from sqlalchemy.orm import Session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_master_index_strategy():
    """Test the master index strategy with sample data."""
    
    print("="*60)
    print("TESTING ELASTICSEARCH MASTER INDEX STRATEGY")
    print("="*60)
    
    # Initialize services
    es_service = ElasticsearchService()
    index_manager = ElasticsearchIndexManager()
    
    # Get database session
    db: Session = next(get_db())
    
    try:
        # Test data
        test_tenants = ["tenant001", "tenant002", "tenant003"]
        test_documents = [
            {
                "content": "This is a comprehensive guide about artificial intelligence and machine learning. AI has revolutionized many industries including healthcare, finance, and technology. Machine learning algorithms can process vast amounts of data to identify patterns and make predictions.",
                "chatbot_id": "chatbot_ai_guide"
            },
            {
                "content": "Customer service best practices include active listening, empathy, and quick problem resolution. Always greet customers warmly and ensure their concerns are addressed promptly. Follow up to ensure satisfaction.",
                "chatbot_id": "chatbot_customer_service"
            },
            {
                "content": "Python programming fundamentals cover variables, data types, functions, and object-oriented programming. Python is widely used for web development, data science, and automation tasks.",
                "chatbot_id": "chatbot_programming"
            }
        ]
        
        print("\n1. Creating tenant aliases and indexing documents...")
        print("-" * 40)
        
        # Index documents for each tenant
        for i, tenant_id in enumerate(test_tenants):
            print(f"\nProcessing tenant: {tenant_id}")
            
            for j, doc_data in enumerate(test_documents):
                document_id = f"doc_{tenant_id}_{j+1}"
                
                print(f"  Indexing document: {document_id}")
                
                # Index document using new strategy
                es_index = es_service.index_document(
                    tenant_id=tenant_id,
                    document_id=document_id,
                    content=doc_data["content"],
                    chatbot_id=doc_data["chatbot_id"],
                    chunk_size=200,
                    overlap=20,
                    db=db  # This enables the master index strategy
                )
                
                print(f"    -> Indexed to: {es_index}")
        
        print("\n2. Showing index statistics...")
        print("-" * 40)
        
        stats = index_manager.get_tenant_statistics(db)
        print(f"Total Master Indices: {stats['total_master_indices']}")
        print(f"Total Tenants: {stats['total_tenants']}")
        
        if stats['indices']:
            for index_name, details in stats['indices'].items():
                print(f"  {index_name}: {details['tenant_count']} tenants "
                      f"({details['utilization_percentage']:.1f}% utilized)")
        
        print("\n3. Testing semantic search...")
        print("-" * 40)
        
        # Test searches for each tenant
        test_queries = [
            "What is artificial intelligence?",
            "How to provide good customer service?",
            "Python programming basics"
        ]
        
        for tenant_id in test_tenants:
            print(f"\nSearching for tenant: {tenant_id}")
            
            for query in test_queries:
                print(f"  Query: '{query}'")
                
                # Perform search using new strategy
                results = es_service.semantic_search(
                    tenant_id=tenant_id,
                    query=query,
                    limit=2,
                    db=db  # This enables the master index strategy
                )
                
                if results:
                    for result in results:
                        print(f"    -> Score: {result['score']:.3f}, "
                              f"Doc: {result['document_id']}")
                        print(f"       Content: {result['content'][:100]}...")
                else:
                    print("    -> No results found")
        
        print("\n4. Testing chatbot-specific filtering...")
        print("-" * 40)
        
        # Test chatbot-specific search
        tenant_id = test_tenants[0]
        chatbot_id = "chatbot_ai_guide"
        query = "machine learning"
        
        print(f"Searching tenant '{tenant_id}' for chatbot '{chatbot_id}' with query '{query}'")
        
        results = es_service.semantic_search(
            tenant_id=tenant_id,
            query=query,
            chatbot_id=chatbot_id,
            limit=3,
            db=db
        )
        
        print(f"Found {len(results)} results:")
        for result in results:
            print(f"  -> Score: {result['score']:.3f}, Doc: {result['document_id']}")
            print(f"     Content: {result['content'][:150]}...")
        
        print("\n5. Verifying tenant isolation...")
        print("-" * 40)
        
        # Verify that tenant data is properly isolated
        tenant1_results = es_service.semantic_search(
            tenant_id=test_tenants[0],
            query="artificial intelligence",
            limit=10,
            db=db
        )
        
        tenant2_results = es_service.semantic_search(
            tenant_id=test_tenants[1],
            query="artificial intelligence",
            limit=10,
            db=db
        )
        
        print(f"Tenant {test_tenants[0]} results: {len(tenant1_results)}")
        print(f"Tenant {test_tenants[1]} results: {len(tenant2_results)}")
        
        # Verify no cross-tenant contamination
        tenant1_docs = {result['document_id'] for result in tenant1_results}
        tenant2_docs = {result['document_id'] for result in tenant2_results}
        
        if tenant1_docs.intersection(tenant2_docs):
            print("❌ ERROR: Cross-tenant data contamination detected!")
        else:
            print("✅ SUCCESS: Tenant isolation verified")
        
        print("\n" + "="*60)
        print("TEST COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        print("\nNext steps:")
        print("1. Run 'python app/scripts/elasticsearch_index_management.py stats' to see detailed statistics")
        print("2. Run 'python app/scripts/elasticsearch_index_management.py list-mappings' to see tenant mappings")
        print("3. Run 'python app/scripts/elasticsearch_index_management.py health' to check cluster health")
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    test_master_index_strategy()
