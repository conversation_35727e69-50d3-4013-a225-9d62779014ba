"""
Conversation State Utilities

This module contains utility functions for managing conversation state
and database operations to avoid circular imports.
"""

import json
import logging
import uuid
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from app.models import ChatbotConversation, ConversationTokenUsage, ChatbotCreditUsage

logger = logging.getLogger(__name__)


def migrate_conversation_state(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate old conversation state format to new format for backward compatibility
    
    Args:
        state: Conversation state dictionary
        
    Returns:
        Dict: Migrated conversation state
    """
    # Check if state is already in new format
    if "remaining_questions" in state and "asked_questions" in state:
        return state

    # Migrate from old format
    if "questions" in state and "current_question_index" in state:
        current_index = state.get("current_question_index", 0)
        questions = state.get("questions", [])
        
        # Split questions into asked and remaining
        asked_questions = questions[:current_index] if current_index > 0 else []
        remaining_questions = questions[current_index:]
        
        # Update state with new format
        state["asked_questions"] = asked_questions
        state["remaining_questions"] = remaining_questions
        
        # Keep old format for compatibility
        # Don't remove "questions" and "current_question_index" yet
        
        logger.info(f"Migrated conversation state: {len(asked_questions)} asked, {len(remaining_questions)} remaining")
    
    return state


def update_conversation_in_db(
    db: Session, 
    conversation_id: str, 
    state: Dict[str, Any], 
    completed: bool = False, 
    verification_pending: bool = False, 
    correction_pending: bool = False, 
    ended: bool = False
) -> bool:
    """
    Update or create conversation record in database
    
    Args:
        db: Database session
        conversation_id: Conversation UUID
        state: Conversation state dictionary
        completed: Whether conversation is completed
        verification_pending: Whether verification is pending
        correction_pending: Whether correction is pending
        ended: Whether conversation has ended
        
    Returns:
        bool: True if update successful, False otherwise
    """
    try:
        from sqlalchemy.sql import func
        
        # Check if conversation exists
        conversation = db.query(ChatbotConversation).filter(
            ChatbotConversation.id == conversation_id
        ).first()
        
        if conversation:
            # Update existing conversation
            conversation.conversation_data = json.dumps(state, default=str)
            conversation.completed = completed
            conversation.updated_at = func.now()
            
            # Update status fields if provided
            if verification_pending:
                conversation.verification_pending = True
            if correction_pending:
                conversation.correction_pending = True
            if ended:
                conversation.ended = True
                
            logger.info(f"Updated conversation {conversation_id} in database")
        else:
            # Create new conversation record if it doesn't exist
            # This shouldn't normally happen, but handle it gracefully
            conversation = ChatbotConversation(
                id=conversation_id,
                conversation_data=json.dumps(state, default=str),
                completed=completed,
                chatbot_id=state.get("chatbot_id"),
                tenant_id=state.get("tenant_id"),
                user_id=state.get("user_id"),
                entity_details=state.get("entity_details", []),
                connected_account_id=state.get("connected_account", {}).get("id"),
                connected_account_name=state.get("connected_account", {}).get("name")
            )
            db.add(conversation)
            logger.info(f"Created new conversation {conversation_id} in database")
        
        # Commit the changes
        db.commit()
        return True
        
    except Exception as e:
        logger.error(f"Error updating conversation {conversation_id} in database: {str(e)}")
        db.rollback()
        return False


def get_conversation_summary(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get a summary of the conversation state
    
    Args:
        state: Conversation state dictionary
        
    Returns:
        Dict: Conversation summary
    """
    try:
        summary = {
            "conversation_id": state.get("conversation_id"),
            "chatbot_id": state.get("chatbot_id"),
            "tenant_id": state.get("tenant_id"),
            "completed": state.get("completed", False),
            "ended": state.get("ended", False),
            "total_questions": len(state.get("all_questions", [])),
            "asked_questions": len(state.get("asked_questions", [])),
            "remaining_questions": len(state.get("remaining_questions", [])),
            "answers_collected": len(state.get("answers", [])),
            "entity_details": state.get("entity_details", []),
            "connected_account": state.get("connected_account", {})
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Error getting conversation summary: {str(e)}")
        return {}


def validate_conversation_state(state: Dict[str, Any]) -> bool:
    """
    Validate conversation state structure
    
    Args:
        state: Conversation state dictionary
        
    Returns:
        bool: True if state is valid, False otherwise
    """
    try:
        required_fields = ["chatbot_id", "tenant_id", "history"]
        
        for field in required_fields:
            if field not in state:
                logger.error(f"Missing required field in conversation state: {field}")
                return False
        
        # Validate history structure
        history = state.get("history", [])
        if not isinstance(history, list):
            logger.error("Conversation history must be a list")
            return False
        
        # Validate questions structure
        if "all_questions" in state and not isinstance(state["all_questions"], list):
            logger.error("all_questions must be a list")
            return False
        
        if "asked_questions" in state and not isinstance(state["asked_questions"], list):
            logger.error("asked_questions must be a list")
            return False
        
        if "remaining_questions" in state and not isinstance(state["remaining_questions"], list):
            logger.error("remaining_questions must be a list")
            return False
        
        # Validate answers structure
        if "answers" in state and not isinstance(state["answers"], list):
            logger.error("answers must be a list")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating conversation state: {str(e)}")
        return False


def clean_conversation_state(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Clean and normalize conversation state
    
    Args:
        state: Conversation state dictionary
        
    Returns:
        Dict: Cleaned conversation state
    """
    try:
        # Ensure required fields exist
        if "history" not in state:
            state["history"] = []
        
        if "answers" not in state:
            state["answers"] = []
        
        if "asked_questions" not in state:
            state["asked_questions"] = []
        
        if "remaining_questions" not in state:
            state["remaining_questions"] = []
        
        # Clean up any None values
        cleaned_state = {}
        for key, value in state.items():
            if value is not None:
                cleaned_state[key] = value
        
        return cleaned_state
        
    except Exception as e:
        logger.error(f"Error cleaning conversation state: {str(e)}")
        return state


def store_conversation_turn(db: Session, conversation_id: str, tenant_id: str,
                          llm_prompt: list, llm_response: str,
                          input_tokens: int, output_tokens: int):
    """
    Store a complete conversation turn with full LLM prompt and response

    Args:
        db: Database session
        conversation_id: The conversation ID
        tenant_id: The tenant ID
        llm_prompt: Complete prompt array sent to LLM (list of message objects)
        llm_response: Complete response received from LLM
        input_tokens: Total tokens in input prompt sent to LLM
        output_tokens: Tokens in response generated by LLM
    """
    try:
        # Store the complete LLM prompt as input
        input_data = {
            "prompt": llm_prompt
        }

        # Store the complete LLM response as output
        output_data = {
            "response": llm_response
        }

        conversation_turn = ConversationTokenUsage(
            id=str(uuid.uuid4()),
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            input=input_data,
            output=output_data,
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )

        db.add(conversation_turn)
        logger.info(f"Stored conversation turn with complete LLM interaction", extra={
            "conversation_id": conversation_id,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "prompt_messages": len(llm_prompt),
            "response_length": len(llm_response)
        })

    except Exception as e:
        logger.error(f"Error storing conversation turn: {str(e)}")


def track_credit_usage(
    db: Session,
    chatbot_id: str,
    conversation_id: str,
    tenant_id: str,
    question: str,
    answer: str,
    has_knowledgebase: bool
):
    """
    Track credit usage for a question-answer interaction

    Args:
        db: Database session
        chatbot_id: The chatbot ID
        conversation_id: The conversation ID
        tenant_id: The tenant ID
        question: The question that was asked
        answer: The answer that was provided
        has_knowledgebase: Whether knowledgebase was available for this chatbot
    """
    try:
        # Determine credits based on knowledgebase availability
        credits_used = 2 if has_knowledgebase else 1

        # Create credit usage record
        credit_usage = ChatbotCreditUsage(
            chatbot_id=chatbot_id,
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            question=question,
            answer=answer,
            credits_used=credits_used,
            has_knowledgebase=has_knowledgebase
        )

        db.add(credit_usage)
        logger.info(f"Tracked credit usage: {credits_used} credits", extra={
            "chatbot_id": chatbot_id,
            "conversation_id": conversation_id,
            "has_knowledgebase": has_knowledgebase,
            "credits_used": credits_used
        })

    except Exception as e:
        logger.error(f"Error tracking credit usage: {str(e)}")
