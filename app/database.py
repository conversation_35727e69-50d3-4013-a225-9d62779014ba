from sqlalchemy import create_engine, Column, String, Integer, Foreign<PERSON>ey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import logging

# Load environment variables from .env file
load_dotenv()

# Get logger
logger = logging.getLogger(__name__)

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sling_sales")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "test")
DB_HOST = os.getenv("POSTGRES_HOST", "postgresql-postgresql")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "whatsapp_chatbot")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def get_database_url():
    """Get the database URL for use in migrations and other contexts"""
    return DATABASE_URL

# Print the database URL (with password masked for security)
masked_url = f"postgresql://{DB_USER}:****@{DB_HOST}:{DB_PORT}/{DB_NAME}"
logger.info(f"Connecting to database", extra={"db_host": DB_HOST, "db_name": DB_NAME, "db_user": DB_USER})

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()