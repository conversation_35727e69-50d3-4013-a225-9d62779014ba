import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv(verbose=True)

# Check AWS credentials
aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
aws_region = os.getenv("AWS_REGION", "us-east-1")
bucket_name = os.getenv("AWS_S3_BUCKET_NAME")

print("AWS Environment Variables:")
print(f"AWS_ACCESS_KEY_ID present: {aws_access_key_id is not None}")
print(f"AWS_SECRET_ACCESS_KEY present: {aws_secret_access_key is not None}")
print(f"AWS_REGION: {aws_region}")
print(f"AWS_S3_BUCKET_NAME: {bucket_name}")

# Check if any required variables are missing
missing_vars = []
if not aws_access_key_id:
    missing_vars.append("AWS_ACCESS_KEY_ID")
if not aws_secret_access_key:
    missing_vars.append("AWS_SECRET_ACCESS_KEY")
if not bucket_name:
    missing_vars.append("AWS_S3_BUCKET_NAME")

if missing_vars:
    print(f"ERROR: Missing required environment variables: {', '.join(missing_vars)}")
else:
    print("All required AWS environment variables are set.")

# Print the .env file path
import pathlib
env_path = pathlib.Path('.') / '.env'
print(f".env file path: {env_path.absolute()}")
print(f".env file exists: {env_path.exists()}")

if env_path.exists():
    print("\nContents of .env file (without showing secret values):")
    with open(env_path, 'r') as f:
        for line in f:
            if line.strip() and not line.strip().startswith('#'):
                key = line.split('=')[0].strip()
                print(f"{key}: {'[HIDDEN]' if 'SECRET' in key or 'KEY' in key else line.split('=')[1].strip()}")