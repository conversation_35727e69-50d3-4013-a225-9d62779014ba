#!/usr/bin/env python3
"""
Test script for the new multi-entity approach:
1. One chatbot per connected account
2. Multiple entities can use the same chatbot
3. After conversation completion, update all entities with collected answers
"""

import pytest
import json
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from app.main import app
from app.models import Chatbot, ChatbotQuestion

# Test client
client = TestClient(app)

# Mock JWT token for testing
MOCK_JWT_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzZWxsIiwiZGF0YSI6eyJ1c2VySWQiOiI3IiwidGVuYW50SWQiOjd9fQ.mock_signature"

def mock_auth_context():
    """Mock authentication context"""
    mock_context = MagicMock()
    mock_context.user_id = "7"
    mock_context.tenant_id = 7
    return mock_context

def test_multi_entity_conversation_start():
    """Test starting conversation with multiple entities"""
    
    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()
        
        with patch('app.routers.chatbot.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__next__.return_value = mock_db
            
            with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
                mock_service = MagicMock()
                mock_service_class.return_value = mock_service
                
                # Mock chatbot (one per account)
                mock_chatbot = MagicMock()
                mock_chatbot.id = "account-chatbot-123"
                mock_chatbot.name = "Account Chatbot"
                mock_chatbot.welcome_message = "Welcome! I'll help you with multiple entities."
                
                # Mock questions that apply to different entity types
                mock_questions = [
                    MagicMock(
                        id="q1", 
                        question="What is the name?", 
                        name="name", 
                        entity_type="LEAD"
                    ),
                    MagicMock(
                        id="q2", 
                        question="What is the email?", 
                        name="email", 
                        entity_type="LEAD"
                    ),
                    MagicMock(
                        id="q3", 
                        question="What is the phone?", 
                        name="phone", 
                        entity_type="CONTACT"
                    )
                ]
                
                mock_service.find_chatbot_by_account.return_value = mock_chatbot
                mock_service.get_chatbot_questions_for_conversation.return_value = mock_questions
                
                with patch('app.routers.chatbot.RedisService') as mock_redis, \
                     patch('app.routers.chatbot.ElasticsearchService') as mock_es:
                    
                    mock_es_instance = MagicMock()
                    mock_es.return_value = mock_es_instance
                    mock_es_instance.select_next_question.return_value = (
                        {"id": "q1", "question": "What is the name?", "field_name": "name", "entity_type": "LEAD"}, 
                        10, 5, "o4-mini"
                    )
                    
                    # Test with multiple entities
                    request_data = {
                        "message": "Hello, I need help with multiple entities",
                        "entityDetails": [
                            {"id": 101, "entityType": "LEAD"},
                            {"id": 102, "entityType": "LEAD"},
                            {"id": 201, "entityType": "CONTACT"}
                        ],
                        "connectedAccount": {
                            "id": 123,
                            "name": "Sales Account"
                        }
                    }
                    
                    with patch('fastapi.Request') as mock_request:
                        mock_request_instance = MagicMock()
                        mock_request_instance.state.auth_context = mock_auth_context()
                        
                        response = client.post(
                            "/v1/chatbot/conversations",
                            json=request_data,
                            headers={"Authorization": MOCK_JWT_TOKEN}
                        )
                    
                    # Verify response
                    assert response.status_code == 200
                    response_data = response.json()
                    
                    assert "chatbotConversationId" in response_data
                    assert "message" in response_data
                    
                    # Verify chatbot was found by account only (not entity type)
                    mock_service.find_chatbot_by_account.assert_called_once_with(123, 7)
                    
                    print("✓ Multi-entity conversation start test passed")

def test_conversation_completion_with_entity_updates():
    """Test conversation completion and entity updates"""
    
    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()
        
        with patch('app.routers.chatbot.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__next__.return_value = mock_db
            
            # Mock conversation state with multiple entities and collected answers
            mock_conversation_state = {
                "chatbot_id": "account-chatbot-123",
                "tenant_id": 7,
                "user_id": "7",
                "entity_details": [
                    {"id": 101, "entityType": "LEAD"},
                    {"id": 102, "entityType": "LEAD"},
                    {"id": 201, "entityType": "CONTACT"}
                ],
                "connected_account": {"id": 123, "name": "Sales Account"},
                "answers": [
                    {
                        "question_id": "q1",
                        "question": "What is the name?",
                        "answer": "John Doe",
                        "field_name": "name",
                        "entity_type": "LEAD"
                    },
                    {
                        "question_id": "q2",
                        "question": "What is the email?",
                        "answer": "<EMAIL>",
                        "field_name": "email",
                        "entity_type": "LEAD"
                    },
                    {
                        "question_id": "q3",
                        "question": "What is the phone?",
                        "answer": "+**********",
                        "field_name": "phone",
                        "entity_type": "CONTACT"
                    }
                ]
            }
            
            with patch('app.routers.chatbot.RedisService') as mock_redis:
                mock_redis_instance = MagicMock()
                mock_redis.return_value = mock_redis_instance
                mock_redis_instance.get_conversation_state.return_value = mock_conversation_state
                
                with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
                    mock_service = MagicMock()
                    mock_service_class.return_value = mock_service
                    
                    # Mock entity update results
                    mock_update_results = {
                        "successful_updates": [
                            {
                                "entity_id": 101,
                                "entity_type": "LEAD",
                                "updated_fields": ["name", "email"],
                                "status": "success"
                            },
                            {
                                "entity_id": 102,
                                "entity_type": "LEAD",
                                "updated_fields": ["name", "email"],
                                "status": "success"
                            },
                            {
                                "entity_id": 201,
                                "entity_type": "CONTACT",
                                "updated_fields": ["phone"],
                                "status": "success"
                            }
                        ],
                        "failed_updates": [],
                        "total_entities": 3
                    }
                    
                    mock_service.update_entities_after_conversation.return_value = mock_update_results
                    
                    # Mock database conversation
                    mock_conversation = MagicMock()
                    mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
                    
                    with patch('fastapi.Request') as mock_request:
                        mock_request_instance = MagicMock()
                        mock_request_instance.state.auth_context = mock_auth_context()
                        
                        response = client.post(
                            "/v1/chatbot/conversations/test-conv-123/complete",
                            headers={"Authorization": MOCK_JWT_TOKEN}
                        )
                    
                    # Verify response
                    assert response.status_code == 200
                    response_data = response.json()
                    
                    assert response_data["message"] == "Conversation completed successfully"
                    assert response_data["total_entities"] == 3
                    assert len(response_data["entities_updated"]) == 3
                    assert len(response_data["entities_failed"]) == 0
                    
                    # Verify entity update was called with correct data
                    mock_service.update_entities_after_conversation.assert_called_once()
                    call_args = mock_service.update_entities_after_conversation.call_args
                    
                    # Check entity details
                    entity_details = call_args[0][0]
                    assert len(entity_details) == 3
                    assert {"id": 101, "entityType": "LEAD"} in entity_details
                    assert {"id": 102, "entityType": "LEAD"} in entity_details
                    assert {"id": 201, "entityType": "CONTACT"} in entity_details
                    
                    # Check collected answers
                    collected_answers = call_args[0][1]
                    assert len(collected_answers) == 3
                    
                    print("✓ Conversation completion with entity updates test passed")

def test_entity_type_specific_field_mapping():
    """Test that answers are correctly mapped to entity types"""
    
    from app.services.chatbot_service import ChatbotService
    
    chatbot_service = ChatbotService()
    
    # Mock entity details
    entity_details = [
        {"id": 101, "entityType": "LEAD"},
        {"id": 201, "entityType": "CONTACT"}
    ]
    
    # Mock collected answers with entity-specific fields
    collected_answers = [
        {
            "question": "What is the name?",
            "answer": "John Doe",
            "field_name": "name",
            "entity_type": "LEAD"  # Only for LEAD entities
        },
        {
            "question": "What is the phone?",
            "answer": "+**********",
            "field_name": "phone",
            "entity_type": "CONTACT"  # Only for CONTACT entities
        },
        {
            "question": "What is the company?",
            "answer": "Acme Corp",
            "field_name": "company",
            "entity_type": None  # Generic field for all entities
        }
    ]
    
    # Test the entity update logic (without actual API calls)
    with patch.object(chatbot_service, 'update_entities_after_conversation') as mock_update:
        # Mock the method to return our test logic
        def mock_update_logic(entities, answers, tenant_id, user_id):
            results = {"successful_updates": [], "failed_updates": [], "total_entities": len(entities)}
            
            for entity in entities:
                entity_id = entity["id"]
                entity_type = entity["entityType"]
                
                # Filter answers for this entity type
                applicable_fields = []
                for answer in answers:
                    answer_entity_type = answer.get("entity_type")
                    field_name = answer.get("field_name")
                    
                    if field_name and (not answer_entity_type or answer_entity_type == entity_type):
                        applicable_fields.append(field_name)
                
                results["successful_updates"].append({
                    "entity_id": entity_id,
                    "entity_type": entity_type,
                    "updated_fields": applicable_fields
                })
            
            return results
        
        mock_update.side_effect = mock_update_logic
        
        # Test the update
        result = chatbot_service.update_entities_after_conversation(
            entity_details, collected_answers, 7, "7"
        )
        
        # Verify results
        assert len(result["successful_updates"]) == 2
        
        # LEAD entity should get 'name' and 'company' fields
        lead_update = next(u for u in result["successful_updates"] if u["entity_type"] == "LEAD")
        assert "name" in lead_update["updated_fields"]
        assert "company" in lead_update["updated_fields"]
        assert "phone" not in lead_update["updated_fields"]
        
        # CONTACT entity should get 'phone' and 'company' fields
        contact_update = next(u for u in result["successful_updates"] if u["entity_type"] == "CONTACT")
        assert "phone" in contact_update["updated_fields"]
        assert "company" in contact_update["updated_fields"]
        assert "name" not in contact_update["updated_fields"]
        
        print("✓ Entity type specific field mapping test passed")

def test_entity_storage_in_conversation_table():
    """Test that entity details are stored in the conversation table"""

    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()

        with patch('app.routers.chatbot.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__next__.return_value = mock_db

            with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
                mock_service = MagicMock()
                mock_service_class.return_value = mock_service

                # Mock chatbot and questions
                mock_chatbot = MagicMock()
                mock_chatbot.id = "account-chatbot-123"
                mock_chatbot.name = "Account Chatbot"
                mock_chatbot.welcome_message = "Welcome!"

                mock_questions = [
                    MagicMock(id="q1", question="What is the name?", name="name", entity_type="LEAD")
                ]

                mock_service.find_chatbot_by_account.return_value = mock_chatbot
                mock_service.get_chatbot_questions_for_conversation.return_value = mock_questions

                with patch('app.routers.chatbot.RedisService') as mock_redis, \
                     patch('app.routers.chatbot.ElasticsearchService') as mock_es:

                    mock_es_instance = MagicMock()
                    mock_es.return_value = mock_es_instance
                    mock_es_instance.select_next_question.return_value = (
                        {"id": "q1", "question": "What is the name?", "field_name": "name", "entity_type": "LEAD"},
                        10, 5, "o4-mini"
                    )

                    # Test conversation creation with entity storage
                    request_data = {
                        "message": "Hello",
                        "entityDetails": [
                            {"id": 101, "entityType": "LEAD"},
                            {"id": 201, "entityType": "CONTACT"}
                        ],
                        "connectedAccount": {
                            "id": 123,
                            "name": "Sales Account"
                        }
                    }

                    with patch('fastapi.Request') as mock_request:
                        mock_request_instance = MagicMock()
                        mock_request_instance.state.auth_context = mock_auth_context()

                        response = client.post(
                            "/v1/chatbot/conversations",
                            json=request_data,
                            headers={"Authorization": MOCK_JWT_TOKEN}
                        )

                    # Verify response
                    assert response.status_code == 200

                    # Verify that ChatbotConversation was created with entity details
                    mock_db.add.assert_called_once()
                    conversation_obj = mock_db.add.call_args[0][0]

                    # Check that entity details are stored
                    assert hasattr(conversation_obj, 'entity_details')
                    assert conversation_obj.entity_details == [
                        {"id": 101, "entityType": "LEAD"},
                        {"id": 201, "entityType": "CONTACT"}
                    ]

                    # Check connected account details
                    assert conversation_obj.connected_account_id == 123
                    assert conversation_obj.connected_account_name == "Sales Account"

                    print("✓ Entity storage in conversation table test passed")

def test_conversation_queries_by_entity():
    """Test querying conversations by entity"""

    from app.services.chatbot_service import ChatbotService

    chatbot_service = ChatbotService()

    # Mock database query results
    with patch.object(chatbot_service, 'get_conversations_by_entity') as mock_get_by_entity:
        mock_conversations = [
            {
                "conversation_id": "conv-123",
                "chatbot_id": "chatbot-456",
                "completed": True,
                "entity_details": [
                    {"id": 101, "entityType": "LEAD"},
                    {"id": 201, "entityType": "CONTACT"}
                ],
                "connected_account_id": 123,
                "connected_account_name": "Sales Account",
                "entity_update_status": {
                    "successful_updates": [
                        {"entity_id": 101, "entity_type": "LEAD", "status": "success"}
                    ],
                    "failed_updates": [],
                    "total_entities": 2
                }
            }
        ]

        mock_get_by_entity.return_value = mock_conversations

        # Test getting conversations by entity
        result = chatbot_service.get_conversations_by_entity(101, "LEAD", 7)

        assert len(result) == 1
        assert result[0]["conversation_id"] == "conv-123"
        assert result[0]["entity_details"] == [
            {"id": 101, "entityType": "LEAD"},
            {"id": 201, "entityType": "CONTACT"}
        ]

        print("✓ Conversation queries by entity test passed")

def run_all_tests():
    """Run all multi-entity tests"""
    print("Running multi-entity approach tests...")
    
    try:
        test_multi_entity_conversation_start()
        test_conversation_completion_with_entity_updates()
        test_entity_type_specific_field_mapping()
        test_entity_storage_in_conversation_table()
        test_conversation_queries_by_entity()

        print("\n✓ All multi-entity approach tests passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
