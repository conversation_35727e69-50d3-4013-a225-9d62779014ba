---
description: Repository Information Overview
alwaysApply: true
---

# Knowledge Base Chatbot API Information

## Summary
A powerful, AI-driven chatbot platform that combines structured Q&A flows with knowledge base search capabilities to provide intelligent customer support. The system uses FastAPI, PostgreSQL, Elasticsearch, and OpenAI's models to deliver a comprehensive chatbot solution.

## Structure
- **app/**: Main application code with routers, services, and utilities
- **alembic/**: Database migration scripts and configuration
- **test/**: Comprehensive test suite with unit and integration tests
- **docs/**: Project documentation and implementation guides
- **deploy/**: Deployment configurations
- **scripts/**: Utility scripts for various operations

## Language & Runtime
**Language**: Python
**Version**: 3.9 (Dockerfile), 3.11+ (recommended in README)
**Build System**: pip
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- fastapi (>=0.115.0): Web framework
- uvicorn (>=0.27.0): ASGI server
- pydantic (>=2.5.0): Data validation
- sqlalchemy (>=2.0.0): ORM
- elasticsearch (8.12.0): Vector search
- openai (>=1.0.0): AI model integration
- alembic (>=1.13.0): Database migrations
- aio-pika (>=9.0.0): RabbitMQ client

**Development Dependencies**:
- pytest (>=7.0.0): Testing framework
- pytest-asyncio (>=0.21.0): Async testing support
- pytest-mock (>=3.10.0): Mocking support
- pytest-cov (>=4.0.0): Test coverage

## Build & Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run database migrations
alembic upgrade head

# Start the server
python start_server.py
```

## Docker
**Dockerfile**: Dockerfile (Python 3.9 base image)
**Docker Compose**: docker-compose.yml
**Services**:
- PostgreSQL (15-alpine)
- Elasticsearch (8.12.0)
- Kibana (8.12.0)
- Redis (7-alpine)
- RabbitMQ (3-management-alpine)
- S3Mock (for local development)

## Main Entry Points
**Application Entry**: app/main.py
**Server Startup**: start_server.py
**API Routers**: app/routers/chatbot.py

## Testing
**Framework**: pytest with multiple plugins
**Test Location**: test/ directory
**Configuration**: pytest.ini
**Markers**: unit, integration, api, database, elasticsearch, rabbitmq, etc.
**Run Command**:
```bash
# Run all tests
pytest

# Run specific test category
pytest -m unit
```

## Database
**Engine**: PostgreSQL
**ORM**: SQLAlchemy
**Migrations**: Alembic
**Models**: app/models.py

## Monitoring & Observability
**Metrics**: Prometheus (exposed at /metrics)
**Logging**: Structured JSON logging
**Health Check**: /health endpoint for Kubernetes probes