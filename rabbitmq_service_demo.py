#!/usr/bin/env python3
"""
RabbitMQ Services Demo Script
Demonstrates how to use the RabbitMQ services independently
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def demo_rabbitmq_services():
    """
    Demonstrate RabbitMQ services functionality
    """
    print("=" * 60)
    print("RabbitMQ Services Demo")
    print("=" * 60)
    
    try:
        # Import services (this will fail without aio_pika, but shows the structure)
        print("1. Importing RabbitMQ services...")
        
        try:
            from app.services.rabbitmq_manager import rabbitmq_manager
            from app.services.rabbitmq_service import rabbitmq_service
            from app.services.event_listeners import event_listener_manager
            print("   ✓ Services imported successfully")
        except ImportError as e:
            print(f"   ✗ Import failed: {e}")
            print("   Note: This is expected without aio_pika installed")
            return
        
        # Initialize and start services
        print("\n2. Starting RabbitMQ services...")
        
        try:
            success = await rabbitmq_manager.start()
            if success:
                print("   ✓ RabbitMQ services started successfully")
            else:
                print("   ✗ RabbitMQ services failed to start")
                return
        except Exception as e:
            print(f"   ✗ Startup failed: {e}")
            print("   Note: This is expected without RabbitMQ server running")
            return
        
        # Get service status
        print("\n3. Checking service status...")
        
        try:
            status = await rabbitmq_manager.get_status()
            print("   ✓ Service status retrieved:")
            print(f"      - Manager running: {status.get('manager', {}).get('is_running', False)}")
            print(f"      - Connection: {status.get('rabbitmq', {}).get('connection_status', 'unknown')}")
            print(f"      - Health: {status.get('health', {}).get('is_healthy', False)}")
        except Exception as e:
            print(f"   ✗ Status check failed: {e}")
        
        # Demonstrate event publishing
        print("\n4. Publishing test events...")
        
        try:
            # Publish a test event
            test_event_data = {
                "timestamp": time.time(),
                "source": "demo_script",
                "data": {"test": "value"}
            }
            
            success = await rabbitmq_manager.publish_event("demo.test.event", test_event_data)
            if success:
                print("   ✓ Test event published successfully")
            else:
                print("   ✗ Test event publishing failed")
            
            # Publish usage data
            usage_data = [
                {"tenantId": "demo-tenant-1", "usageEntity": "CHATBOT", "count": 3},
                {"tenantId": "demo-tenant-2", "usageEntity": "CHATBOT", "count": 1}
            ]
            
            success = await rabbitmq_manager.publish_usage_data("usage.demo.response", usage_data)
            if success:
                print("   ✓ Usage data published successfully")
            else:
                print("   ✗ Usage data publishing failed")
                
        except Exception as e:
            print(f"   ✗ Event publishing failed: {e}")
        
        # Demonstrate health check
        print("\n5. Testing health check...")
        
        try:
            is_healthy = await rabbitmq_service.is_healthy()
            print(f"   ✓ Health check result: {'Healthy' if is_healthy else 'Unhealthy'}")
        except Exception as e:
            print(f"   ✗ Health check failed: {e}")
        
        # Demonstrate consumer status
        print("\n6. Checking consumer status...")
        
        try:
            consumer_status = rabbitmq_service.get_consumer_status()
            print("   ✓ Consumer status retrieved:")
            print(f"      - Connection: {consumer_status.get('connection_status', 'unknown')}")
            print(f"      - Channel: {consumer_status.get('channel_status', 'unknown')}")
            print(f"      - Consumers: {len(consumer_status.get('consumers', {}))}")
            
            for queue_name, status in consumer_status.get('consumers', {}).items():
                print(f"        * {queue_name}: {status.get('status', 'unknown')}")
                
        except Exception as e:
            print(f"   ✗ Consumer status check failed: {e}")
        
        # Wait a moment to see any async activity
        print("\n7. Monitoring for 5 seconds...")
        await asyncio.sleep(5)
        print("   ✓ Monitoring completed")
        
        # Stop services
        print("\n8. Stopping RabbitMQ services...")
        
        try:
            await rabbitmq_manager.stop()
            print("   ✓ RabbitMQ services stopped successfully")
        except Exception as e:
            print(f"   ✗ Shutdown failed: {e}")
        
        print("\n" + "=" * 60)
        print("Demo completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}", exc_info=True)
        print(f"\n✗ Demo failed: {e}")


async def demo_event_handler():
    """
    Demonstrate custom event handler
    """
    print("\nDemonstrating custom event handler...")
    
    async def custom_event_handler(payload: Dict[str, Any], message):
        """Custom event handler for demo"""
        print(f"   📨 Received event: {payload}")
        print(f"   📋 Message routing key: {message.routing_key}")
        print(f"   ✅ Event processed successfully")
    
    try:
        from app.services.rabbitmq_service import rabbitmq_service
        
        # Register custom handler
        rabbitmq_service.register_event_handler("demo.custom.event", custom_event_handler)
        print("   ✓ Custom event handler registered")
        
        # This would normally be triggered by an incoming message
        print("   Note: Handler would be triggered by incoming RabbitMQ messages")
        
    except ImportError:
        print("   ✗ Cannot demonstrate without aio_pika")
    except Exception as e:
        print(f"   ✗ Handler demo failed: {e}")


def demo_configuration():
    """
    Demonstrate configuration options
    """
    print("\nDemonstrating configuration options...")
    
    import os
    
    config_vars = [
        ("RABBITMQ_URL", "amqp://guest:guest@localhost:5672/"),
        ("RABBITMQ_CONNECTION_TIMEOUT", "30"),
        ("RABBITMQ_HEARTBEAT", "300"),
        ("RABBITMQ_CONSUMER_TIMEOUT", "0"),
        ("RABBITMQ_HEALTH_CHECK_INTERVAL", "60"),
        ("RABBITMQ_AUTO_RECOVERY", "true"),
        ("SERVICE_NAME", "whatsapp-chatbot")
    ]
    
    print("   Current configuration:")
    for var_name, default_value in config_vars:
        current_value = os.getenv(var_name, default_value)
        print(f"      {var_name}: {current_value}")
    
    print("\n   Configuration can be customized via environment variables:")
    print("      export RABBITMQ_URL=amqp://user:pass@rabbitmq-server:5672/")
    print("      export RABBITMQ_HEARTBEAT=600")
    print("      export SERVICE_NAME=my-custom-service")


def demo_api_endpoints():
    """
    Demonstrate API endpoint usage
    """
    print("\nDemonstrating API endpoints...")
    
    endpoints = [
        ("GET", "/health/rabbitmq", "Check RabbitMQ service health"),
        ("POST", "/admin/rabbitmq/restart", "Restart RabbitMQ services"),
        ("POST", "/admin/rabbitmq/recover", "Force consumer recovery")
    ]
    
    print("   Available endpoints:")
    for method, path, description in endpoints:
        print(f"      {method:4} {path:25} - {description}")
    
    print("\n   Example usage:")
    print("      curl http://localhost:8000/health/rabbitmq")
    print("      curl -X POST http://localhost:8000/admin/rabbitmq/restart")
    print("      curl -X POST http://localhost:8000/admin/rabbitmq/recover")


async def main():
    """
    Main demo function
    """
    print("RabbitMQ Services Demonstration")
    print("This script shows how to use the RabbitMQ services")
    print("Note: Requires RabbitMQ server and aio_pika for full functionality\n")
    
    # Configuration demo (always works)
    demo_configuration()
    
    # API endpoints demo (always works)
    demo_api_endpoints()
    
    # Event handler demo (works without server)
    await demo_event_handler()
    
    # Full service demo (requires server)
    await demo_rabbitmq_services()
    
    print("\nFor production usage:")
    print("1. Install dependencies: pip install aio-pika")
    print("2. Start RabbitMQ server: docker run -d --name rabbitmq -p 5672:5672 rabbitmq:3")
    print("3. Configure environment variables as needed")
    print("4. Start your FastAPI application: uvicorn app.main:app")
    print("5. Check health: curl http://localhost:8000/health/rabbitmq")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo failed: {e}")
        logger.error("Demo failed", exc_info=True)
