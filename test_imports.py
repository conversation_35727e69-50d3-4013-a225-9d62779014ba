#!/usr/bin/env python3
"""
Test script to verify that all imports work correctly
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """Test all the imports that were causing issues"""
    
    print("Testing imports...")
    
    # Test 1: Basic aio_pika import
    try:
        from aio_pika.abc import AbstractIncomingMessage
        print("✓ aio_pika.abc.AbstractIncomingMessage import successful")
    except ImportError as e:
        print(f"✗ aio_pika.abc.AbstractIncomingMessage import failed: {e}")
        return False
    
    # Test 2: Conversation state utils
    try:
        from app.utils.conversation_state_utils import migrate_conversation_state, update_conversation_in_db
        print("✓ conversation_state_utils import successful")
    except ImportError as e:
        print(f"✗ conversation_state_utils import failed: {e}")
        return False
    
    # Test 3: Message event listener
    try:
        from app.services.message_event_listener import message_event_listener
        print("✓ message_event_listener import successful")
    except ImportError as e:
        print(f"✗ message_event_listener import failed: {e}")
        return False
    
    # Test 4: Event listeners
    try:
        from app.services.event_listeners import event_listener_manager
        print("✓ event_listeners import successful")
    except ImportError as e:
        print(f"✗ event_listeners import failed: {e}")
        return False
    
    # Test 5: RabbitMQ manager
    try:
        from app.services.rabbitmq_manager import rabbitmq_manager
        print("✓ rabbitmq_manager import successful")
    except ImportError as e:
        print(f"✗ rabbitmq_manager import failed: {e}")
        return False
    
    # Test 6: Conversation event publisher
    try:
        from app.services.conversation_event_publisher import conversation_event_publisher
        print("✓ conversation_event_publisher import successful")
    except ImportError as e:
        print(f"✗ conversation_event_publisher import failed: {e}")
        return False
    
    # Test 7: Charge calculator
    try:
        from app.services.charge_calculator import charge_calculator
        print("✓ charge_calculator import successful")
    except ImportError as e:
        print(f"✗ charge_calculator import failed: {e}")
        return False
    
    print("\n🎉 All imports successful!")
    return True

def test_basic_functionality():
    """Test basic functionality of imported modules"""
    
    print("\nTesting basic functionality...")
    
    try:
        from app.services.charge_calculator import charge_calculator
        
        # Test charge calculation
        charge = charge_calculator.calculate_question_charge(
            {"question": "Test question"}, 
            is_predefined=True, 
            is_llm_generated=False
        )
        assert charge == 1, f"Expected charge 1, got {charge}"
        print("✓ Charge calculator working correctly")
        
    except Exception as e:
        print(f"✗ Charge calculator test failed: {e}")
        return False
    
    try:
        from app.utils.conversation_state_utils import migrate_conversation_state
        
        # Test state migration
        old_state = {
            "questions": [{"id": 1, "question": "Test?"}],
            "current_question_index": 0,
            "chatbot_id": "test",
            "tenant_id": "test"
        }
        
        new_state = migrate_conversation_state(old_state)
        assert "remaining_questions" in new_state, "State migration failed"
        assert "asked_questions" in new_state, "State migration failed"
        print("✓ State migration working correctly")
        
    except Exception as e:
        print(f"✗ State migration test failed: {e}")
        return False
    
    print("✓ Basic functionality tests passed!")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("Import and Functionality Test")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    if imports_ok:
        # Test functionality
        functionality_ok = test_basic_functionality()
        
        if functionality_ok:
            print("\n🎉 All tests passed! The event-driven system is ready to use.")
            sys.exit(0)
        else:
            print("\n❌ Functionality tests failed.")
            sys.exit(1)
    else:
        print("\n❌ Import tests failed.")
        sys.exit(1)
