#!/usr/bin/env python3
"""
Server startup script for the Knowledge Base Chatbot API
"""

import os
import sys
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """Start the FastAPI server"""
    
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    reload = os.getenv("RELOAD", "false").lower() == "true"
    
    print(f"Starting server on {host}:{port}")
    print(f"Log level: {log_level}")
    print(f"Reload: {reload}")
    
    # Start the server
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        workers=4,
        log_level=log_level,
        reload=reload,
        access_log=True
    )

if __name__ == "__main__":
    main()