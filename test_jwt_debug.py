#!/usr/bin/env python3
"""
JWT Token Debug Script
Helps debug JWT token parsing issues
"""

import sys
import os
import json
from jose import jwt
from pydantic import ValidationError

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_jwt_parsing():
    """Test JWT parsing with different approaches"""
    print("🔍 JWT Token Debug Tool")
    print("=" * 50)
    
    # Example token that might be causing issues (based on the error)
    example_token_data = {
        "data": {
            "accessToken": "1c77c32f...",
            "userId": 3841,  # This is an int, but model expects string
            "tenantId": 2048,
            "permissions": [
                {
                    "id": 1,
                    "name": "test",
                    "description": "test permission",
                    "limits": 100,
                    "units": "requests",
                    "action": {"read": True}
                }
            ]
            # Missing: iss, refreshToken, username, source, meta
        }
    }
    
    print("Testing with example token data:")
    print(json.dumps(example_token_data, indent=2))
    
    # Test 1: Try with original model
    print("\n--- Test 1: Original JWTPayload Model ---")
    try:
        from app.models import JWTPayload
        payload = JWTPayload.model_validate(example_token_data)
        print("✅ Original model validation successful")
        print(f"   User ID: {payload.data.userId if payload.data else 'N/A'}")
        print(f"   Tenant ID: {payload.data.tenantId if payload.data else 'N/A'}")
    except ValidationError as e:
        print("❌ Original model validation failed:")
        for error in e.errors():
            print(f"   - {error['loc']}: {error['msg']}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    # Test 2: Try with minimal required fields
    print("\n--- Test 2: Minimal Token Data ---")
    minimal_token = {
        "iss": "test-issuer",
        "data": {
            "accessToken": "test-token",
            "userId": "3841",  # Convert to string
            "tenantId": 2048,
            "refreshToken": "test-refresh",
            "username": "test-user",
            "source": {
                "type": "test",
                "id": "test-id",
                "name": "test-name"
            },
            "meta": {
                "rate-limit": 100,
                "pid": 1234
            },
            "permissions": []
        }
    }
    
    try:
        payload = JWTPayload.model_validate(minimal_token)
        print("✅ Minimal model validation successful")
        print(f"   User ID: {payload.data.userId if payload.data else 'N/A'}")
        print(f"   Tenant ID: {payload.data.tenantId if payload.data else 'N/A'}")
    except ValidationError as e:
        print("❌ Minimal model validation failed:")
        for error in e.errors():
            print(f"   - {error['loc']}: {error['msg']}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    # Test 3: Test auth context creation
    print("\n--- Test 3: Auth Context Creation ---")
    try:
        from app.dependencies import AuthContext
        
        # Simulate successful payload
        if 'payload' in locals():
            auth_context = AuthContext(
                user_id="3841",
                tenant_id="2048",
                source_type="test",
                source_id="test-id",
                source_name="test-name",
                permissions=[]
            )
            print("✅ Auth context creation successful")
            print(f"   User ID: {auth_context.user_id}")
            print(f"   Tenant ID: {auth_context.tenant_id}")
        else:
            print("❌ No valid payload to create auth context")
    except Exception as e:
        print(f"❌ Auth context creation failed: {e}")
    
    # Test 4: Test JWT decoding without signature verification
    print("\n--- Test 4: JWT Decoding Test ---")
    
    # Create a test JWT token
    test_payload = {
        "iss": "test-issuer",
        "data": {
            "accessToken": "test-token",
            "userId": 3841,
            "tenantId": 2048,
            "permissions": []
        }
    }
    
    try:
        # Encode a test JWT
        test_jwt = jwt.encode(test_payload, "test-secret", algorithm="HS256")
        print(f"Created test JWT: {test_jwt[:50]}...")
        
        # Decode without signature verification
        decoded = jwt.decode(test_jwt, options={"verify_signature": False})
        print("✅ JWT decoding successful")
        print(f"   Decoded payload: {json.dumps(decoded, indent=2)}")
        
        # Try to validate with our model
        payload = JWTPayload.model_validate(decoded)
        print("✅ Model validation of decoded JWT successful")
        
    except Exception as e:
        print(f"❌ JWT decoding failed: {e}")

def test_flexible_parsing():
    """Test the flexible parsing approach"""
    print("\n" + "=" * 50)
    print("🔧 Testing Flexible Parsing Approach")
    print("=" * 50)
    
    # Simulate the problematic token structure from the error
    problematic_token = {
        "data": {
            "accessToken": "1c77c32f...",
            "userId": 3841,  # int instead of string
            "tenantId": 2048,
            "permissions": [{"action": {"read": True}}]
            # Missing required fields
        }
    }
    
    print("Testing flexible parsing with problematic token:")
    print(json.dumps(problematic_token, indent=2))
    
    try:
        from app.models import JWTPayload
        
        # Create a more flexible payload
        flexible_payload = {
            "iss": None,
            "data": problematic_token.get("data"),
            "accessToken": problematic_token.get("data", {}).get("accessToken"),
            "userId": problematic_token.get("data", {}).get("userId"),
            "tenantId": problematic_token.get("data", {}).get("tenantId"),
            "permissions": problematic_token.get("data", {}).get("permissions", []),
            "username": None,
            "refreshToken": None,
            "source": None,
            "meta": None
        }
        
        payload = JWTPayload.model_validate(flexible_payload)
        print("✅ Flexible parsing successful")
        
        # Test auth context extraction
        user_id = str(payload.data.userId) if payload.data and payload.data.userId else str(payload.userId) if payload.userId else "unknown"
        tenant_id = str(payload.data.tenantId) if payload.data and payload.data.tenantId else str(payload.tenantId) if payload.tenantId else "unknown"
        
        print(f"   Extracted User ID: {user_id}")
        print(f"   Extracted Tenant ID: {tenant_id}")
        
    except ValidationError as e:
        print("❌ Flexible parsing failed:")
        for error in e.errors():
            print(f"   - {error['loc']}: {error['msg']}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def main():
    """Run all tests"""
    try:
        test_jwt_parsing()
        test_flexible_parsing()
        
        print("\n" + "=" * 50)
        print("💡 Recommendations:")
        print("=" * 50)
        print("1. Ensure userId is sent as string, not integer")
        print("2. Include all required fields: iss, refreshToken, username, source, meta")
        print("3. Use the flexible JWT model that handles optional fields")
        print("4. Consider using environment variables for JWT secret")
        print("5. Add proper error handling for missing fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
