"""Update question structure to support lead and contact fields

Revision ID: d5e6f7g8h9i0
Revises: f6g7h8i9j0k1
Create Date: 2025-07-28 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd5e6f7g8h9i0'
down_revision: Union[str, None] = 'f6g7h8i9j0k1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """Upgrade to new question structure"""
    
    # Add new columns
    op.add_column('chatbot_questions', sa.Column('position', sa.Integer(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('lead_field_id', sa.Integer(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('lead_field_display_name', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('contact_field_id', sa.Integer(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('contact_field_display_name', sa.String(), nullable=True))
    
    # Increase question column length
    op.alter_column('chatbot_questions', 'question',
                    existing_type=sa.String(length=100),
                    type_=sa.String(length=500),
                    existing_nullable=False)
    
    # Migrate existing data - set position based on creation order and map old fields to lead fields
    # Use a subquery with window function to set position values
    op.execute("""
        UPDATE chatbot_questions
        SET position = subq.row_num,
            lead_field_id = field_id,
            lead_field_display_name = display_name
        FROM (
            SELECT id, row_number() OVER (PARTITION BY chatbot_id ORDER BY created_at) as row_num
            FROM chatbot_questions
            WHERE position IS NULL
        ) subq
        WHERE chatbot_questions.id = subq.id
          AND position IS NULL
    """)
    
    # Make position column non-nullable after data migration
    op.alter_column('chatbot_questions', 'position', nullable=False)
    
    # Drop old columns that are no longer needed
    op.drop_column('chatbot_questions', 'field_id')
    op.drop_column('chatbot_questions', 'display_name')
    op.drop_column('chatbot_questions', 'entity_type')
    op.drop_column('chatbot_questions', 'name')
    op.drop_column('chatbot_questions', 'standard')


def downgrade():
    """Downgrade to old question structure"""
    
    # Add back old columns
    op.add_column('chatbot_questions', sa.Column('field_id', sa.Integer(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('display_name', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('entity_type', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('name', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('standard', sa.Boolean(), nullable=True, default=False))
    
    # Migrate data back - use lead field data as primary
    op.execute("""
        UPDATE chatbot_questions
        SET field_id = COALESCE(lead_field_id, contact_field_id, 0),
            display_name = COALESCE(lead_field_display_name, contact_field_display_name, 'Unknown'),
            entity_type = 'LEAD',
            name = LOWER(REPLACE(COALESCE(lead_field_display_name, contact_field_display_name, 'unknown'), ' ', '_')),
            standard = false
        WHERE field_id IS NULL
    """)
    
    # Make old columns non-nullable
    op.alter_column('chatbot_questions', 'field_id', nullable=False)
    op.alter_column('chatbot_questions', 'display_name', nullable=False)
    op.alter_column('chatbot_questions', 'entity_type', nullable=False)
    op.alter_column('chatbot_questions', 'name', nullable=False)
    op.alter_column('chatbot_questions', 'standard', nullable=False)
    
    # Revert question column length
    op.alter_column('chatbot_questions', 'question',
                    existing_type=sa.String(length=500),
                    type_=sa.String(length=100),
                    existing_nullable=False)
    
    # Drop new columns
    op.drop_column('chatbot_questions', 'contact_field_display_name')
    op.drop_column('chatbot_questions', 'contact_field_id')
    op.drop_column('chatbot_questions', 'lead_field_display_name')
    op.drop_column('chatbot_questions', 'lead_field_id')
    op.drop_column('chatbot_questions', 'position')
