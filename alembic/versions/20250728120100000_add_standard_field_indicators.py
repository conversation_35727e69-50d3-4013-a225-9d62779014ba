"""Add standard field indicators to questions

Revision ID: e6f7g8h9i0j1
Revises: d5e6f7g8h9i0
Create Date: 2025-07-28 12:01:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e6f7g8h9i0j1'
down_revision: Union[str, None] = 'd5e6f7g8h9i0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """Add standard field indicator columns"""
    
    # Add new columns for standard field indicators
    op.add_column('chatbot_questions', sa.Column('is_lead_field_standard', sa.<PERSON>(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('is_contact_field_standard', sa.<PERSON>(), nullable=True))


def downgrade():
    """Remove standard field indicator columns"""
    
    # Drop the standard field indicator columns
    op.drop_column('chatbot_questions', 'is_contact_field_standard')
    op.drop_column('chatbot_questions', 'is_lead_field_standard')
