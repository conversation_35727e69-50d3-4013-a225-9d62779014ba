# Database Migrations with Alembic

This directory contains Alembic database migrations for the SD WhatsApp Chatbot application.

## Overview

Alembic is a database migration tool for SQLAlchemy. It provides a way to manage database schema changes in a version-controlled manner.

## Usage

### Check current migration status
```bash
alembic current
```

### Create a new migration
```bash
alembic revision --autogenerate -m "Description of changes"
```

### Apply migrations
```bash
alembic upgrade head
```

### Rollback migrations
```bash
alembic downgrade -1  # Go back one migration
alembic downgrade <revision_id>  # Go back to specific revision
```

### View migration history
```bash
alembic history
```

## Migration Files

- `eaf05b0f246b_create_all_required_database_tables.py` - Complete database schema with all required tables

## Configuration

- `alembic.ini` - Alembic configuration file
- `env.py` - Migration environment configuration
- `script.py.mako` - Template for new migration files

## Notes

- The database URL is automatically configured from environment variables
- All models are imported from `app.models`
- Manual migration scripts have been replaced with this Alembic setup
