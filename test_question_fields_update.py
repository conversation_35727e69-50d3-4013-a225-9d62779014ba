#!/usr/bin/env python3
"""
Test script to verify the question fields update implementation
"""

import sys
import os
from unittest.mock import Mock, patch

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chatbot_question_model():
    """Test that the ChatbotQuestion model has the new fields"""
    print("Testing ChatbotQuestion model updates...")
    try:
        from app.models import ChatbotQuestion
        
        # Check if the model has the new fields
        required_fields = ['entity_type', 'name', 'standard']
        
        for field in required_fields:
            if hasattr(ChatbotQuestion, field):
                print(f"✅ ChatbotQuestion model has {field} field")
            else:
                print(f"❌ ChatbotQuestion model missing {field} field")
                return False
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import ChatbotQuestion model: {e}")
        return False

def test_question_pydantic_models():
    """Test that the Pydantic models have been updated"""
    print("\nTesting Pydantic models updates...")
    try:
        from app.models import QuestionCreate, QuestionUpdate
        
        # Test QuestionCreate model
        print("Testing QuestionCreate model...")
        try:
            question_create = QuestionCreate(
                question="Test question",
                fieldId=1,
                displayName="Test Display",
                entityType="LEAD",
                name="test_name",
                standard=True
            )
            print("✅ QuestionCreate model accepts new fields")
        except Exception as e:
            print(f"❌ QuestionCreate model error: {e}")
            return False
        
        # Test QuestionUpdate model
        print("Testing QuestionUpdate model...")
        try:
            question_update = QuestionUpdate(
                question="Updated question",
                entityType="CONTACT",
                name="updated_name",
                standard=False
            )
            print("✅ QuestionUpdate model accepts new fields")
        except Exception as e:
            print(f"❌ QuestionUpdate model error: {e}")
            return False
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import Pydantic models: {e}")
        return False

def test_chatbot_service_question_creation():
    """Test that ChatbotService creates questions with new fields"""
    print("\nTesting ChatbotService question creation...")
    try:
        from app.services.chatbot_service import ChatbotService
        from app.models import QuestionCreate
        import inspect
        
        service = ChatbotService()
        
        # Check configure_chatbot_questions method signature
        configure_sig = inspect.signature(service.configure_chatbot_questions)
        expected_params = ['chatbot_id', 'questions', 'tenant_id', 'user_id', 'token']
        actual_params = list(configure_sig.parameters.keys())
        
        if actual_params == expected_params:
            print("✅ configure_chatbot_questions method has correct signature")
        else:
            print(f"❌ configure_chatbot_questions signature mismatch. Expected: {expected_params}, Got: {actual_params}")
            return False
        
        # Check update_chatbot_question method signature
        update_sig = inspect.signature(service.update_chatbot_question)
        expected_params = ['chatbot_id', 'question_id', 'question_data', 'tenant_id']
        actual_params = list(update_sig.parameters.keys())
        
        if actual_params == expected_params:
            print("✅ update_chatbot_question method has correct signature")
        else:
            print(f"❌ update_chatbot_question signature mismatch. Expected: {expected_params}, Got: {actual_params}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to test ChatbotService: {e}")
        return False

def test_migration_file():
    """Test that the migration file exists and has correct content"""
    print("\nTesting migration file...")
    try:
        migration_file = "alembic/versions/20250721175404822_c3d4e5f6g7h8_add_question_fields_entity_type_name_standard.py"
        
        if os.path.exists(migration_file):
            print("✅ Migration file exists")
            
            with open(migration_file, 'r') as f:
                content = f.read()
            
            # Check for required content
            required_content = [
                "add_column('chatbot_questions', sa.Column('entity_type'",
                "add_column('chatbot_questions', sa.Column('name'",
                "add_column('chatbot_questions', sa.Column('standard'"
            ]
            
            for required in required_content:
                if required in content:
                    print(f"✅ Migration contains: {required}")
                else:
                    print(f"❌ Migration missing: {required}")
                    return False
            
            return True
        else:
            print(f"❌ Migration file not found: {migration_file}")
            return False
    except Exception as e:
        print(f"❌ Failed to test migration file: {e}")
        return False

def test_question_response_format():
    """Test that question responses include new fields"""
    print("\nTesting question response format...")
    try:
        # This is a conceptual test since we can't actually run the service without DB
        # We'll check if the service methods are structured to return the new fields
        
        from app.services.chatbot_service import ChatbotService
        import inspect
        
        # Get the source code of configure_chatbot_questions method
        service = ChatbotService()
        method = service.configure_chatbot_questions
        source = inspect.getsource(method)
        
        # Check if the response includes the new fields
        required_response_fields = ['entityType', 'name', 'standard']
        
        for field in required_response_fields:
            if f'q.{field.lower().replace("type", "_type")}' in source:
                print(f"✅ Response includes {field} field")
            else:
                print(f"❌ Response missing {field} field")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to test response format: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Question Fields Update Implementation")
    print("=" * 60)
    
    tests = [
        test_chatbot_question_model,
        test_question_pydantic_models,
        test_chatbot_service_question_creation,
        test_migration_file,
        test_question_response_format
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Question fields update implementation is complete.")
        print("\n📝 Next steps:")
        print("1. Run the database migration: alembic upgrade head")
        print("2. Test the API endpoints with new question fields:")
        print("   - entityType: string")
        print("   - name: string") 
        print("   - standard: boolean")
        print("\n📋 Example API request:")
        print("""
        POST /v1/chatbot/{chatbot_id}/questions
        {
          "question": "What is your name?",
          "fieldId": 1,
          "displayName": "Customer Name",
          "entityType": "LEAD",
          "name": "customer_name",
          "standard": true
        }
        """)
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
