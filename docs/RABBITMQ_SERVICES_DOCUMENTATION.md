# RabbitMQ Services Documentation

## Overview

This document describes the comprehensive RabbitMQ services integration for the WhatsApp Chatbot application. The implementation provides robust message queuing, event handling, and service management with automatic recovery and health monitoring.

## Architecture

### Components

1. **RabbitMQService** (`app/services/rabbitmq_service.py`)
   - Core RabbitMQ connection and messaging functionality
   - Connection management with auto-recovery
   - Exchange and queue management
   - Message publishing and consuming
   - Health monitoring and status reporting

2. **RabbitMQManager** (`app/services/rabbitmq_manager.py`)
   - High-level service lifecycle management
   - Startup and shutdown coordination
   - Health monitoring and recovery
   - Admin operations and status reporting

3. **Event Listeners** (`app/services/event_listeners.py`)
   - Scheduler event handling
   - Usage collection event processing
   - Integration with ChatbotService

4. **FastAPI Integration** (`app/main.py`)
   - Application lifecycle integration
   - Health check endpoints
   - Admin management endpoints

## Features

### ✅ **Connection Management**
- Robust connection handling with `aio_pika.connect_robust`
- Automatic reconnection on connection loss
- Configurable connection timeouts and heartbeats
- Connection pooling and resource management

### ✅ **Message Publishing**
- Event publishing to exchanges
- Usage data publishing
- Message persistence and delivery guarantees
- JSON serialization and content-type handling

### ✅ **Message Consuming**
- Event-driven message consumption
- Automatic message acknowledgment
- Consumer recovery and monitoring
- Queue binding and routing

### ✅ **Health Monitoring**
- Continuous health checks
- Consumer status monitoring
- Automatic recovery on failures
- Detailed status reporting

### ✅ **Service Management**
- Graceful startup and shutdown
- Service lifecycle coordination
- Admin endpoints for management
- Configuration through environment variables

## Configuration

### Environment Variables

```bash
# RabbitMQ Connection
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_CONNECTION_TIMEOUT=30
RABBITMQ_HEARTBEAT=300
RABBITMQ_CONSUMER_TIMEOUT=0

# Health Monitoring
RABBITMQ_HEALTH_CHECK_INTERVAL=60
RABBITMQ_AUTO_RECOVERY=true

# Service Identity
SERVICE_NAME=whatsapp-chatbot
```

### Default Configuration

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `RABBITMQ_URL` | `amqp://guest:guest@localhost:5672/` | RabbitMQ connection URL |
| `RABBITMQ_CONNECTION_TIMEOUT` | `30` | Connection timeout in seconds |
| `RABBITMQ_HEARTBEAT` | `300` | Heartbeat interval in seconds |
| `RABBITMQ_CONSUMER_TIMEOUT` | `0` | Consumer timeout (0 = no timeout) |
| `RABBITMQ_HEALTH_CHECK_INTERVAL` | `60` | Health check interval in seconds |
| `RABBITMQ_AUTO_RECOVERY` | `true` | Enable automatic recovery |
| `SERVICE_NAME` | `whatsapp-chatbot` | Service identifier |

## Usage

### Application Startup

The RabbitMQ services are automatically started when the FastAPI application starts:

```python
@app.on_event("startup")
async def startup_event():
    rabbitmq_started = await rabbitmq_manager.start()
    if rabbitmq_started:
        logger.info("RabbitMQ services started successfully")
    else:
        logger.warning("RabbitMQ services failed to start")
```

### Publishing Events

```python
from app.services.rabbitmq_manager import rabbitmq_manager

# Publish a general event
await rabbitmq_manager.publish_event("user.created", {
    "user_id": "123",
    "tenant_id": "tenant-456"
})

# Publish usage data
await rabbitmq_manager.publish_usage_data("usage.collect.response", [
    {"tenantId": "tenant-123", "usageEntity": "CHATBOT", "count": 5}
])
```

### Event Handling

Events are automatically routed to registered handlers:

```python
# In event_listeners.py
async def handle_collect_usage_event(self, payload, message):
    """Handle scheduler.collect.usage event"""
    # Import and use ChatbotService
    from app.services.chatbot_service import ChatbotService
    
    chatbot_service = ChatbotService()
    await chatbot_service.collect_and_publish_chatbot_usage()
```

## API Endpoints

### Health Check

**GET** `/health/rabbitmq`

Returns the health status of RabbitMQ services:

```json
{
  "status": "healthy",
  "details": {
    "manager": {
      "is_initialized": true,
      "is_running": true,
      "auto_recovery": true,
      "health_monitor_running": true
    },
    "rabbitmq": {
      "connection_status": "connected",
      "channel_status": "open",
      "consumers": {
        "whatsapp-chatbot.scheduler.events": {
          "is_consuming": true,
          "consumer_tag_exists": true,
          "status": "healthy"
        }
      }
    },
    "health": {
      "is_healthy": true,
      "last_check": "just_now"
    }
  }
}
```

### Admin Endpoints

**POST** `/admin/rabbitmq/restart`

Restart RabbitMQ services:

```json
{
  "message": "RabbitMQ services restarted successfully"
}
```

**POST** `/admin/rabbitmq/recover`

Force recovery of RabbitMQ consumers:

```json
{
  "message": "RabbitMQ consumer recovery completed successfully"
}
```

## Event Flow

### Usage Collection Flow

1. **Scheduler Event**: External scheduler publishes `scheduler.collect.usage` event
2. **Event Reception**: RabbitMQ service receives event on `ex.scheduler` exchange
3. **Event Routing**: Event routed to `SchedulerEventListener.handle_collect_usage_event`
4. **Usage Collection**: ChatbotService collects usage data from database
5. **Usage Publishing**: Usage data published to `ex.usage` exchange with routing key `usage.collect.response`

### Message Flow Diagram

```
External Scheduler
       ↓
   ex.scheduler exchange
       ↓ (scheduler.collect.usage)
whatsapp-chatbot.scheduler.events queue
       ↓
SchedulerEventListener.handle_collect_usage_event
       ↓
ChatbotService.collect_and_publish_chatbot_usage
       ↓
   ex.usage exchange
       ↓ (usage.collect.response)
External Usage Consumers
```

## Error Handling

### Connection Failures
- Automatic reconnection with exponential backoff
- Health monitoring detects connection issues
- Consumer recovery on connection restoration
- Graceful degradation (REST API remains functional)

### Message Processing Errors
- Failed messages are rejected (not requeued)
- Detailed error logging with context
- Consumer continues processing other messages
- Health status reflects processing errors

### Recovery Mechanisms
- Automatic consumer recovery
- Connection health monitoring
- Manual recovery endpoints
- Service restart capabilities

## Monitoring and Logging

### Log Levels
- **INFO**: Service lifecycle, successful operations
- **WARNING**: Recoverable errors, health issues
- **ERROR**: Critical failures, processing errors
- **DEBUG**: Detailed operation traces

### Key Metrics
- Connection status and uptime
- Consumer health and message processing
- Event publishing success/failure rates
- Recovery operation frequency

### Health Indicators
- RabbitMQ connection status
- Channel availability
- Consumer activity and heartbeats
- Message processing latency

## Integration with ChatbotService

The ChatbotService integrates with RabbitMQ for usage data publishing:

```python
# In ChatbotService.collect_and_publish_chatbot_usage()
if payload:
    await rabbitmq_service.publish_message(
        "ex.usage",
        "usage.collect.response", 
        payload
    )
```

This ensures usage data is properly published to the usage exchange for consumption by external systems.

## Deployment Considerations

### Dependencies
```bash
pip install aio-pika
```

### RabbitMQ Server Requirements
- RabbitMQ 3.8+ recommended
- Management plugin enabled (optional, for monitoring)
- Appropriate user permissions for exchanges and queues

### Network Configuration
- Ensure RabbitMQ server is accessible from application
- Configure firewall rules for RabbitMQ ports (5672, 15672)
- Consider TLS/SSL for production deployments

### Resource Requirements
- Memory: ~50MB for RabbitMQ client connections
- CPU: Minimal overhead for message processing
- Network: Depends on message volume and frequency

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check RabbitMQ server is running
   - Verify connection URL and credentials
   - Check network connectivity and firewall rules

2. **Consumer Not Receiving Messages**
   - Verify queue bindings and routing keys
   - Check exchange and queue declarations
   - Review consumer registration and handlers

3. **High Memory Usage**
   - Monitor message queue lengths
   - Check for unprocessed messages
   - Review consumer processing performance

### Debug Commands

```bash
# Check RabbitMQ service health
curl http://localhost:8000/health/rabbitmq

# Restart RabbitMQ services
curl -X POST http://localhost:8000/admin/rabbitmq/restart

# Force consumer recovery
curl -X POST http://localhost:8000/admin/rabbitmq/recover
```

## Testing

The RabbitMQ services include comprehensive testing:

```bash
python3 test_rabbitmq_services.py
```

Tests cover:
- Service structure and method availability
- Integration patterns and imports
- Lifecycle management (with mocked dependencies)
- Environment variable configuration
- FastAPI endpoint integration

## Conclusion

The RabbitMQ services provide a robust, scalable messaging infrastructure for the WhatsApp Chatbot application. With automatic recovery, health monitoring, and comprehensive error handling, the services ensure reliable event processing and usage data collection even in challenging network conditions.
