# Chatbot New Fields Implementation

## Overview

This document describes the implementation of three new fields for the chatbot POST API:
- `welcomeMessage`: Optional welcome message displayed to users
- `thankYouMessage`: Optional thank you message shown after conversations
- `connectedAccountId`: Optional ID linking the chatbot to an external account

## Changes Made

### 1. Database Model Updates

**File**: `app/models.py`

Added three new nullable columns to the `Chatbot` model:

```python
class Chatbot(Base):
    __tablename__ = "chatbots"
    
    # ... existing fields ...
    welcome_message = Column(String, nullable=True)
    thank_you_message = Column(String, nullable=True)
    connected_account_id = Column(String, nullable=True)
    # ... existing fields ...
```

### 2. Pydantic Model Updates

**File**: `app/models.py`

Updated both `ChatbotCreate` and `ChatbotUpdate` models:

```python
class ChatbotCreate(BaseModel):
    name: str
    type: str  # 'AI' or 'RULE'
    description: str = None
    welcome_message: str = None
    thank_you_message: str = None
    connected_account_id: str = None

class ChatbotUpdate(BaseModel):
    name: str = None
    description: str = None
    welcome_message: str = None
    thank_you_message: str = None
    connected_account_id: str = None
    questions: List[QuestionCreate] = None
    knowledgebase_ids: List[str] = None
```

### 3. API Endpoint Updates

**File**: `app/routers/chatbot.py`

#### Create Chatbot Endpoint (`POST /chatbots`)

Updated to accept and store the new fields:

```python
chatbot = Chatbot(
    id=chatbot_id,
    tenant_id=tenant_id,
    name=chatbot_data.name,
    type=chatbot_type,
    description=chatbot_data.description or "",
    welcome_message=chatbot_data.welcome_message,
    thank_you_message=chatbot_data.thank_you_message,
    connected_account_id=chatbot_data.connected_account_id,
    status="DRAFT"
)
```

#### Update Chatbot Endpoint (`PUT /chatbots/{chatbot_id}`)

Updated to handle field updates:

```python
if chatbot_data.welcome_message is not None:
    chatbot.welcome_message = chatbot_data.welcome_message

if chatbot_data.thank_you_message is not None:
    chatbot.thank_you_message = chatbot_data.thank_you_message

if chatbot_data.connected_account_id is not None:
    chatbot.connected_account_id = chatbot_data.connected_account_id
```

#### Get Chatbot Endpoint (`GET /chatbots/{chatbot_id}`)

Updated to return the new fields in responses:

```python
return {
    "id": chatbot.id,
    "tenant_id": chatbot.tenant_id,
    "name": chatbot.name,
    "type": chatbot.type,
    "description": chatbot.description,
    "welcome_message": chatbot.welcome_message,
    "thank_you_message": chatbot.thank_you_message,
    "connected_account_id": chatbot.connected_account_id,
    "status": chatbot.status,
    "created_at": chatbot.created_at,
    "updated_at": chatbot.updated_at
}
```

### 4. Database Migration

**File**: `migrations/add_chatbot_message_fields.py`

Created a comprehensive migration script with:
- Forward migration to add columns
- Rollback capability to remove columns
- Migration status checking
- Proper error handling and logging

**Usage**:
```bash
# Run migration
python migrations/add_chatbot_message_fields.py

# Check if migration is needed
python migrations/add_chatbot_message_fields.py --check

# Rollback migration
python migrations/add_chatbot_message_fields.py --rollback
```

## API Usage Examples

### Create Chatbot with New Fields

```bash
POST /chatbots
Content-Type: application/json

{
    "name": "Customer Support Bot",
    "type": "AI",
    "description": "AI-powered customer support chatbot",
    "welcome_message": "Hello! Welcome to our customer support. How can I help you today?",
    "thank_you_message": "Thank you for contacting us. Have a great day!",
    "connected_account_id": "support_account_001"
}
```

### Update Chatbot Fields

```bash
PUT /chatbots/{chatbot_id}
Content-Type: application/json

{
    "welcome_message": "Hi there! Welcome to our improved support system.",
    "thank_you_message": "Thanks for using our service. We appreciate your feedback!",
    "connected_account_id": "updated_support_account_002"
}
```

### Partial Update (Single Field)

```bash
PUT /chatbots/{chatbot_id}
Content-Type: application/json

{
    "welcome_message": "New welcome message only"
}
```

## API Response Structure

All chatbot endpoints now return the new fields:

```json
{
    "id": "550e8400-e29b-41d4-a716-************",
    "tenant_id": "tenant_123",
    "name": "Customer Support Bot",
    "type": "AI",
    "description": "AI-powered customer support chatbot",
    "welcome_message": "Hello! Welcome to our customer support. How can I help you today?",
    "thank_you_message": "Thank you for contacting us. Have a great day!",
    "connected_account_id": "support_account_001",
    "status": "DRAFT",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
}
```

## Field Specifications

### welcomeMessage
- **Type**: String (optional)
- **Purpose**: Custom welcome message shown to users when they start a conversation
- **Default**: `null`
- **Example**: "Hello! Welcome to our customer support. How can I help you today?"

### thankYouMessage
- **Type**: String (optional)
- **Purpose**: Custom thank you message shown after conversations end
- **Default**: `null`
- **Example**: "Thank you for contacting us. Have a great day!"

### connectedAccountId
- **Type**: String (optional)
- **Purpose**: External account ID for integration with other systems
- **Default**: `null`
- **Example**: "support_account_001", "crm_lead_gen_bot"

## Use Cases

### 1. E-commerce Bot
```json
{
    "name": "Shopping Assistant",
    "welcome_message": "Welcome to our online store! Browse our products or ask for help.",
    "thank_you_message": "Thank you for shopping with us! Your order will be processed soon.",
    "connected_account_id": "ecommerce_store_001"
}
```

### 2. Support Bot
```json
{
    "name": "Technical Support",
    "welcome_message": "Hi! I'm here to help with any technical issues you might have.",
    "thank_you_message": "Thanks for contacting support. We'll follow up if needed.",
    "connected_account_id": "tech_support_team"
}
```

### 3. Lead Generation Bot
```json
{
    "name": "Sales Assistant",
    "welcome_message": "Hello! Interested in our services? Let me gather some information.",
    "thank_you_message": "Thank you for your interest! Our sales team will contact you soon.",
    "connected_account_id": "sales_team_crm"
}
```

## Testing

### Test Files Created
- `test_chatbot_new_fields.py`: Comprehensive tests for models and API
- `test_chatbot_fields_structure.py`: Structure validation tests

### Test Results
All tests passed successfully:
- ✅ Database model updated with 3 new nullable fields
- ✅ Pydantic models updated for API validation
- ✅ Create chatbot endpoint handles new fields
- ✅ Update chatbot endpoint handles new fields
- ✅ Get chatbot endpoint returns new fields
- ✅ Migration script created for database updates

## Deployment Steps

1. **Run Migration**:
   ```bash
   python migrations/add_chatbot_message_fields.py
   ```

2. **Verify Migration**:
   ```bash
   python migrations/add_chatbot_message_fields.py --check
   ```

3. **Test API Endpoints**:
   - Test creating chatbots with new fields
   - Test updating existing chatbots
   - Verify responses include new fields

4. **Update API Documentation**:
   - Update OpenAPI/Swagger documentation
   - Update client SDKs if applicable

## Backward Compatibility

- All new fields are optional (`nullable=True`)
- Existing API calls continue to work without modification
- Existing chatbots will have `null` values for new fields
- No breaking changes to existing functionality

## Future Enhancements

Potential improvements for the new fields:
1. **Message Templates**: Pre-defined message templates for common use cases
2. **Localization**: Multi-language support for welcome and thank you messages
3. **Dynamic Messages**: Context-aware messages based on user data
4. **Message Analytics**: Track effectiveness of different messages
5. **Rich Media**: Support for images, buttons, and rich content in messages
