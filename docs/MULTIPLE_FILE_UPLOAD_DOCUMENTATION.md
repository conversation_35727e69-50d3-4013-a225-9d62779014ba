# Multiple File Upload Documentation

## Overview

This document describes the implementation of multiple file upload functionality for chatbot knowledgebase. The feature allows users to upload multiple PDF files in a single request, with each file being stored in S3 and indexed in Elasticsearch with vector embeddings.

## Changes Made

### 1. Enhanced ChatbotService with Multiple File Upload

#### New Method: `upload_multiple_knowledgebase_files()`

```python
async def upload_multiple_knowledgebase_files(
    self, 
    chatbot_id: str, 
    files: List[UploadFile], 
    tenant_id: str, 
    user_id: str
) -> Dict[str, Any]:
```

**Key Features:**
- **Batch Processing**: Handles multiple files in a single request
- **Individual Validation**: Each file is validated independently
- **Robust Error Handling**: Continues processing even if some files fail
- **Detailed Results**: Returns success/failure status for each file
- **S3 Storage**: Each file is stored in S3 with unique keys
- **Elasticsearch Indexing**: Each file content is indexed with vector embeddings
- **Database Integration**: Creates document records and chatbot associations
- **Status Management**: Updates chatbot status when appropriate

#### New Helper Method: `_extract_pdf_text()`

```python
async def _extract_pdf_text(self, file_data: bytes, filename: str) -> str:
```

**Features:**
- **PDF Text Extraction**: Uses PyPDF2 to extract text from PDF files
- **Page-by-page Processing**: Extracts text from all pages
- **Error Handling**: Provides detailed error messages for extraction failures
- **Content Validation**: Ensures extracted content is not empty

### 2. Updated Router Endpoint

#### Before (Single File):
```python
@router.post("/{chatbot_id}/knowledgebase")
async def add_chatbot_knowledgebase(
    chatbot_id: str,
    request: Request,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    # 118 lines of complex logic
```

#### After (Multiple Files):
```python
@router.post("/{chatbot_id}/knowledgebase")
async def add_chatbot_knowledgebase(
    chatbot_id: str,
    request: Request,
    files: List[UploadFile] = File(...)
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id
    
    # Use ChatbotService to handle multiple file uploads
    chatbot_service = ChatbotService()
    return await chatbot_service.upload_multiple_knowledgebase_files(
        chatbot_id, files, tenant_id, user_id
    )
```

### 3. Enhanced Imports

```python
from fastapi import HTTPException, UploadFile
from io import BytesIO
import PyPDF2
from app.services.elasticsearch_service import ElasticsearchService
from app.services.s3_service import S3Service
```

## API Usage

### Request Format

**Endpoint:** `POST /chatbot/{chatbot_id}/knowledgebase`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `chatbot_id` (path): The chatbot ID
- `files` (form): Multiple PDF files

### Example Request

```bash
curl -X POST \
  "https://api.example.com/chatbot/123e4567-e89b-12d3-a456-************/knowledgebase" \
  -H "Authorization: Bearer <token>" \
  -F "files=@document1.pdf" \
  -F "files=@document2.pdf" \
  -F "files=@document3.pdf"
```

### Response Format

```json
{
  "message": "Processed 3 files",
  "chatbot_id": "123e4567-e89b-12d3-a456-************",
  "tenant_id": "tenant-123",
  "total_files": 3,
  "successful_uploads": 2,
  "failed_uploads": 1,
  "results": {
    "successful": [
      {
        "filename": "document1.pdf",
        "status": "success",
        "document_id": "doc-uuid-1",
        "s3_key": "tenant-123/chatbot-123/document1.pdf",
        "es_index": "documents_tenant_123"
      },
      {
        "filename": "document2.pdf",
        "status": "success",
        "document_id": "doc-uuid-2",
        "s3_key": "tenant-123/chatbot-123/document2.pdf",
        "es_index": "documents_tenant_123"
      }
    ],
    "failed": [
      {
        "filename": "document3.pdf",
        "status": "failed",
        "error": "Could not extract text from PDF"
      }
    ]
  },
  "chatbot_status": "ACTIVE"
}
```

## Processing Flow

### 1. **Request Validation**
- Validates chatbot exists and belongs to tenant
- Initializes S3 and Elasticsearch services
- Prepares result tracking arrays

### 2. **File Processing Loop**
For each uploaded file:

#### a. **File Validation**
- Checks file type (only PDF supported)
- Validates file is not empty
- Records validation failures

#### b. **Content Extraction**
- Reads file data into memory
- Extracts text using PyPDF2
- Validates extracted content is not empty

#### c. **Storage Operations**
- **S3 Upload**: Stores original file with unique key
- **Elasticsearch Indexing**: Indexes cleaned text with vector embeddings
- **Database Record**: Creates Document record with metadata

#### d. **Association Creation**
- Creates ChatbotKnowledgebase association
- Links document to chatbot

#### e. **Result Tracking**
- Records success with document details
- Records failures with error messages

### 3. **Post-Processing**
- Updates chatbot status if conditions met
- Commits successful operations to database
- Returns comprehensive results

## Error Handling

### File-Level Errors
- **Invalid File Type**: Only PDF files are accepted
- **Empty Files**: Files with no content are rejected
- **PDF Extraction Errors**: Corrupted or unreadable PDFs
- **S3 Upload Failures**: Network or permission issues
- **Elasticsearch Failures**: Indexing or embedding errors

### Request-Level Errors
- **Chatbot Not Found**: Invalid chatbot ID or tenant mismatch
- **Database Errors**: Connection or transaction failures
- **Service Unavailability**: S3 or Elasticsearch service issues

### Error Response Example

```json
{
  "message": "Processed 2 files",
  "total_files": 2,
  "successful_uploads": 1,
  "failed_uploads": 1,
  "results": {
    "successful": [
      {
        "filename": "valid.pdf",
        "status": "success",
        "document_id": "doc-uuid-1"
      }
    ],
    "failed": [
      {
        "filename": "invalid.txt",
        "status": "failed",
        "error": "Only PDF files are supported"
      }
    ]
  }
}
```

## Benefits

### 1. **Improved User Experience**
- Upload multiple files in single request
- Detailed feedback for each file
- Partial success handling (some files can fail without affecting others)

### 2. **Better Performance**
- Batch processing reduces API calls
- Efficient resource utilization
- Single database transaction for successful uploads

### 3. **Enhanced Reliability**
- Individual file error handling
- Transaction rollback on critical failures
- Comprehensive logging and monitoring

### 4. **Scalability**
- Handles variable number of files
- Memory-efficient processing
- Proper resource cleanup

## Storage Architecture

### S3 Storage Structure
```
bucket/
├── tenant-123/
│   ├── chatbot-456/
│   │   ├── document1.pdf
│   │   ├── document2.pdf
│   │   └── document3.pdf
│   └── chatbot-789/
│       └── other-docs.pdf
```

### Elasticsearch Index Structure
```json
{
  "index": "documents_tenant_123",
  "document": {
    "document_id": "doc-uuid-1",
    "content": "extracted and cleaned text",
    "embeddings": [0.1, 0.2, 0.3, ...],
    "metadata": {
      "filename": "document1.pdf",
      "chatbot_id": "chatbot-456",
      "tenant_id": "tenant-123"
    }
  }
}
```

### Database Schema
```sql
-- Documents table
INSERT INTO documents (
  id, tenant_id, document_name, document_type,
  es_index, es_document_id, s3_key, created_by
);

-- Chatbot-Document associations
INSERT INTO chatbot_knowledgebase (
  id, chatbot_id, document_id, tenant_id
);
```

## Testing

### Test Coverage
- ✅ **Service Method Existence**: Verifies method is present and async
- ✅ **Router Integration**: Confirms router accepts multiple files
- ✅ **Service Structure**: Validates all required components
- ✅ **Import Dependencies**: Checks all necessary imports
- ✅ **Multiple File Processing**: Mocked test with 3 files
- ✅ **File Validation**: Tests PDF validation and error handling
- ✅ **PDF Extraction**: Verifies text extraction method

### Test Results
```
Multiple File Upload Test Suite
======================================================================
✓ Router properly accepts multiple files
✓ Service structure is complete  
✓ All required imports are present
✓ Multiple file upload works correctly
  - Processed 3 files
  - 3 successful uploads
  - 0 failed uploads
✓ File validation works correctly
  - Valid files: 1
  - Invalid files: 2
  - Validation errors properly caught
```

## Migration Impact

### Backward Compatibility
- **API Endpoint**: Same URL, now accepts multiple files
- **Single File Support**: Still works with single file uploads
- **Response Format**: Enhanced with detailed results
- **Error Handling**: Improved with per-file error reporting

### Client Updates Required
- **Frontend**: Update file input to accept multiple files
- **API Calls**: Change from single file to file array
- **Response Handling**: Process new detailed response format

## Future Enhancements

1. **File Type Support**: Add support for other document types (DOC, TXT, etc.)
2. **Progress Tracking**: Real-time upload progress for large files
3. **Async Processing**: Background processing for very large files
4. **Duplicate Detection**: Prevent uploading duplicate documents
5. **File Compression**: Automatic compression for large files
6. **Batch Limits**: Configurable limits on number of files per request

## Conclusion

The multiple file upload feature successfully enhances the chatbot knowledgebase functionality by allowing efficient batch uploads while maintaining robust error handling and detailed feedback. The implementation follows best practices for file handling, storage, and user experience.
