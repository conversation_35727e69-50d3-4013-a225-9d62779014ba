# RabbitMQ Integration Documentation

## Overview

This document describes the RabbitMQ integration implemented in the WhatsApp Chatbot application. The integration provides event-driven communication capabilities, specifically listening for scheduler events on the `ex.scheduler` exchange.

## Architecture

### Components

1. **RabbitMQService** (`app/services/rabbitmq_service.py`)
   - Core service for RabbitMQ connection management
   - Handles exchange and queue declarations
   - Provides message routing and event handling

2. **Event Listeners** (`app/services/event_listeners.py`)
   - SchedulerEventListener for handling scheduler events
   - EventListenerManager for managing multiple listeners
   - Specific handler for `scheduler.collect.usage` events

3. **FastAPI Integration** (`app/main.py`)
   - Startup and shutdown event handlers
   - Automatic RabbitMQ connection management

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```env
# RabbitMQ settings
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_CONNECTION_TIMEOUT=30
RABBITMQ_HEARTBEAT=300
RABBITMQ_CONSUMER_TIMEOUT=0
RABBITMQ_HEALTH_CHECK_INTERVAL=60
SERVICE_NAME=whatsapp-chatbot

# Redis settings (if not already present)
REDIS_URL=redis://localhost:6379/0
```

### Consumer Persistence Configuration

- **RABBITMQ_HEARTBEAT=300**: Heartbeat interval (5 minutes) to maintain connection
- **RABBITMQ_CONSUMER_TIMEOUT=0**: No consumer timeout (prevents auto-removal)
- **RABBITMQ_HEALTH_CHECK_INTERVAL=60**: Health check every 60 seconds

### Dependencies

The following dependency has been added to `requirements.txt`:

```
aio-pika>=9.0.0
```

## Event Configuration

### Exchange and Queue Setup

- **Exchange**: `ex.scheduler` (topic exchange, durable)
- **Queue**: `{SERVICE_NAME}.scheduler.events` (durable)
- **Routing Key**: `scheduler.collect.usage`
- **Event Payload**: `{}` (empty object)

### Message Flow

```
Scheduler Service → ex.scheduler → scheduler.collect.usage → whatsapp-chatbot.scheduler.events → Event Handler
```

## Implementation Details

### RabbitMQ Service Features

- **Connection Management**: Robust connection with automatic reconnection
- **Exchange Declaration**: Automatic setup of required exchanges
- **Queue Management**: Dynamic queue creation and binding
- **Message Routing**: Event-based message routing to specific handlers
- **Error Handling**: Comprehensive error handling with message acknowledgment/rejection
- **Consumer Persistence**: Ensures consumers are never auto-removed due to idle timeouts
- **Auto-Recovery**: Automatic detection and recovery of failed consumers
- **Health Monitoring**: Continuous monitoring of consumer and connection health
- **Keepalive Mechanism**: Prevents connection timeouts with periodic keepalive signals

### Event Handling

The `scheduler.collect.usage` event handler:

1. Receives the event with empty payload `{}`
2. Logs the event receipt
3. Can trigger usage collection tasks (placeholder implementation)
4. Acknowledges the message upon successful processing

### Usage Collection Placeholder

The current implementation includes placeholder methods for:

- `_cleanup_old_conversations()`: Clean up old conversation data
- `_collect_token_usage_stats()`: Collect token usage statistics
- `_generate_usage_reports()`: Generate usage reports
- `_update_usage_metrics()`: Update usage metrics

## Usage

### Starting the Application

When the FastAPI application starts, it automatically:

1. Connects to RabbitMQ
2. Declares the `ex.scheduler` exchange
3. Creates the service-specific queue
4. Binds the queue to the exchange with the routing key
5. Starts consuming messages

### Event Processing

When a `scheduler.collect.usage` event is received:

```python
# Event payload (empty)
payload = {}

# Event is automatically routed to the handler
await handle_collect_usage_event(payload, message)
```

### Manual Testing

To test the integration manually:

1. Ensure RabbitMQ is running
2. Start the application
3. Publish a message to the `ex.scheduler` exchange with routing key `scheduler.collect.usage`

## Error Handling

### Connection Failures

- Application continues to function even if RabbitMQ is unavailable
- Connection failures are logged but don't prevent application startup
- Automatic reconnection attempts when RabbitMQ becomes available

### Message Processing Errors

- Failed messages are logged and rejected (not requeued)
- Error details are captured for debugging
- Application remains stable during message processing failures

### Graceful Degradation

- REST API functionality is not affected by RabbitMQ issues
- Event processing failures don't impact core chatbot functionality

## Monitoring and Logging

### Log Messages

The integration provides detailed logging for:

- Connection establishment and failures
- Exchange and queue declarations
- Message receipt and processing
- Error conditions and recovery

### Log Levels

- **INFO**: Normal operation events
- **WARNING**: Non-critical issues
- **ERROR**: Failures and exceptions
- **DEBUG**: Detailed message content (when enabled)

## Development and Testing

### Running Tests

```bash
# Test the integration structure
python3 test_rabbitmq_structure.py

# Test with actual RabbitMQ (requires aio-pika installation)
pip install aio-pika>=9.0.0
python3 test_rabbitmq_integration.py
```

### Local Development Setup

1. Install RabbitMQ locally or use Docker:
   ```bash
   docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management
   ```

2. Update `.env` with local RabbitMQ URL:
   ```env
   RABBITMQ_URL=amqp://guest:guest@localhost:5672/
   ```

3. Start the application:
   ```bash
   uvicorn app.main:app --reload
   ```

## Production Considerations

### Security

- Use proper authentication credentials in production
- Configure TLS/SSL for encrypted connections
- Implement proper access controls

### Performance

- Monitor queue depths and processing times
- Configure appropriate prefetch counts
- Scale consumers based on message volume

### Reliability

- Use durable exchanges and queues
- Implement proper message acknowledgment
- Monitor connection health and automatic recovery

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure RabbitMQ is running and accessible
2. **Authentication Failed**: Check credentials in RABBITMQ_URL
3. **Queue Not Found**: Verify exchange and queue declarations
4. **Messages Not Processed**: Check event handler registration

### Debug Steps

1. Check application logs for RabbitMQ connection status
2. Verify environment variables are properly set
3. Test RabbitMQ connectivity manually
4. Monitor RabbitMQ management interface (if available)

## Consumer Persistence Guarantees

### Problem Solved

The original issue was that RabbitMQ consumers were being auto-removed after idle periods, causing the application to stop receiving events. This implementation provides comprehensive solutions:

### Persistence Features

1. **No Consumer Timeout**: `RABBITMQ_CONSUMER_TIMEOUT=0` prevents automatic consumer removal
2. **Consumer Monitoring**: Continuous monitoring detects when consumers are removed
3. **Auto-Recovery**: Failed consumers are automatically re-established
4. **Health Checks**: Periodic health checks ensure consumers remain active
5. **Keepalive Signals**: Prevent connection timeouts with regular keepalive messages
6. **Connection Resilience**: Automatic reconnection when connection is lost

### Monitoring and Status

The service provides comprehensive status monitoring:

```python
# Get consumer status
status = rabbitmq_service.get_consumer_status()

# Example status response:
{
    "connection_status": "connected",
    "channel_status": "open",
    "consumers": {
        "whatsapp-chatbot.scheduler.events": {
            "is_consuming": True,
            "consumer_exists": True,
            "consumer_closed": False,
            "consumer_tag": "ctag-**********",
            "last_heartbeat": **********.0,
            "heartbeat_age_seconds": 30.5,
            "status": "healthy"
        }
    },
    "health_check_running": True,
    "keepalive_running": True,
    "is_shutting_down": False
}
```

### Recovery Mechanisms

1. **Automatic Detection**: Consumers are monitored every 30 seconds
2. **Health Checks**: Full health check every 60 seconds (configurable)
3. **Immediate Recovery**: Failed consumers are recovered within 5 seconds
4. **Retry Logic**: Failed recovery attempts are retried after 60 seconds
5. **Graceful Degradation**: System continues functioning during recovery

## Future Enhancements

Potential improvements for the RabbitMQ integration:

1. **Additional Events**: Support for more scheduler events
2. **Message Persistence**: Store processed events for audit trails
3. **Dead Letter Queues**: Handle permanently failed messages
4. **Metrics Integration**: Prometheus metrics for message processing
5. **Health Checks**: RabbitMQ connectivity health endpoints
6. **Consumer Scaling**: Dynamic consumer scaling based on queue depth
