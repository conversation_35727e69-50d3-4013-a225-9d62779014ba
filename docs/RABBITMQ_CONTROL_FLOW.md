# RabbitMQ Control Flow & Step-by-Step Guide

This document provides a detailed step-by-step breakdown of how RabbitMQ publishing and listening works in the WhatsApp Chatbot system.

## 🚀 System Startup Control Flow

### Step 1: Application Bootstrap (`app/main.py`)

```python
# File: app/main.py
@app.on_event("startup")
async def startup_event():
    # 1. Initialize database
    # 2. Initialize Redis
    # 3. Initialize RabbitMQ ← Our focus
    rabbitmq_started = await rabbitmq_manager.start()
```

**What happens**: FastAPI triggers RabbitMQ initialization on application startup.

### Step 2: RabbitMQ Manager Start (`app/services/rabbitmq_manager.py`)

```python
# File: app/services/rabbitmq_manager.py
async def start():
    # 1. Check if already initialized
    if not self.is_initialized:
        await self.initialize()  # ← Goes to Step 3
    
    # 2. Start event listeners
    await event_listener_manager.start_all()  # ← Goes to Step 6
    
    # 3. Start health monitoring
    self.health_check_task = asyncio.create_task(self._health_monitor_loop())
```

**What happens**: Orchestrates the complete RabbitMQ system startup.

### Step 3: RabbitMQ Manager Initialize (`app/services/rabbitmq_manager.py`)

```python
# File: app/services/rabbitmq_manager.py  
async def initialize():
    # 1. Connect to RabbitMQ server
    connected = await rabbitmq_service.connect()  # ← Goes to Step 4
    
    # 2. Setup all publishers (declare exchanges)
    await rabbitmq_service.setup_all_publishers()  # ← Goes to Step 5
    
    # 3. Setup scheduler listener
    await rabbitmq_service.setup_scheduler_listener()  # ← Goes to Step 5
```

**What happens**: Initializes core RabbitMQ infrastructure.

### Step 4: Core Connection (`app/services/rabbitmq_service.py`)

```python
# File: app/services/rabbitmq_service.py
async def connect():
    # 1. Create robust connection
    self.connection = await connect_robust(
        "amqp://sling_sales:test@rabbitmq:5672/",
        timeout=30,
        heartbeat=300
    )
    
    # 2. Create channel
    self.channel = await self.connection.channel()
    
    # 3. Set QoS (process one message at a time)
    await self.channel.set_qos(prefetch_count=1)
    
    # 4. Start background tasks
    self.health_check_task = asyncio.create_task(self._health_check_loop())
    self.keepalive_task = asyncio.create_task(self._keepalive_loop())
```

**What happens**: Establishes the actual connection to RabbitMQ server and starts monitoring.

### Step 5: Infrastructure Setup (`app/services/rabbitmq_service.py`)

```python
# File: app/services/rabbitmq_service.py

# A. Setup Publishers - Declare Exchanges
async def setup_all_publishers():
    await self.setup_whatsapp_chatbot_publisher()  # ex.chatbot
    await self.setup_usage_publisher()             # ex.usage  
    await self.setup_events_publisher()            # ex.events

# B. Setup Scheduler Listener - Declare Queue & Binding
async def setup_scheduler_listener():
    # 1. Declare exchange
    await self.declare_exchange("ex.scheduler", "topic")
    
    # 2. Declare queue
    queue_name = "whatsapp-chatbot.scheduler.events"
    await self.declare_queue(queue_name, durable=True)
    
    # 3. Bind queue to exchange
    await self.bind_queue_to_exchange(
        queue_name, 
        "ex.scheduler", 
        "scheduler.collect.usage"
    )
    
    # 4. Register event handler
    self.register_event_handler("scheduler.collect.usage", self.handle_collect_usage_event)
    
    # 5. Start consuming
    await self.start_consuming(queue_name)
```

**What happens**: Creates all exchanges, queues, and bindings needed for the system.

### Step 6: Event Listeners Start (`app/services/event_listener_manager.py`)

```python
# File: app/services/event_listener_manager.py
async def start_all():
    # 1. Start scheduler event listener
    await scheduler_event_listener.start()
    
    # 2. Start message event listener  
    await message_event_listener.start()
```

**What happens**: Starts all event listeners that will process incoming messages.

## 📤 Publishing Control Flow

### Publishing Example: Usage Data

#### Step 1: Application Triggers Publishing

```python
# File: app/services/chatbot_service.py
async def collect_and_publish_chatbot_usage():
    # 1. Collect usage data from database
    usage_data = await self._collect_usage_from_db()
    
    # 2. Publish via RabbitMQ manager
    await rabbitmq_manager.publish_usage_data("usage.collect.response", usage_data)
```

**What happens**: Business logic triggers publishing of usage data.

#### Step 2: RabbitMQ Manager Publishing (`app/services/rabbitmq_manager.py`)

```python
# File: app/services/rabbitmq_manager.py
async def publish_usage_data(routing_key, usage_data):
    # 1. Check if RabbitMQ is running
    if not self.is_running:
        return False
    
    # 2. Delegate to core service
    await rabbitmq_service.publish_message("ex.usage", routing_key, usage_data)
```

**What happens**: High-level publishing interface with health checks.

#### Step 3: Core Publishing Logic (`app/services/rabbitmq_service.py`)

```python
# File: app/services/rabbitmq_service.py
async def publish_message(exchange, routing_key, message, durable=True):
    # 1. Retry loop (max 3 attempts)
    for attempt in range(3):
        try:
            # 2. Ensure connection is healthy
            if not await self._ensure_connection():
                raise RuntimeError("Connection failed")
            
            # 3. Ensure exchange exists
            await self.declare_exchange(exchange)
            
            # 4. Serialize message
            message_body = json.dumps(message)
            
            # 5. Create AMQP message
            msg = Message(
                message_body.encode(),
                delivery_mode=2,  # Persistent
                content_type="application/json"
            )
            
            # 6. Publish to exchange
            await self.exchanges[exchange].publish(msg, routing_key=routing_key)
            
            return  # Success - exit retry loop
            
        except Exception as e:
            # 7. Handle retry logic
            if attempt < 2:
                await self._clear_connection_state()
                await asyncio.sleep(1.0 * (attempt + 1))
            else:
                raise
```

**What happens**: Core publishing logic with retry, serialization, and error handling.

#### Step 4: RabbitMQ Server Processing

```
1. RabbitMQ receives message on ex.usage exchange
2. Checks routing key "usage.collect.response"  
3. Routes to any queues bound with matching routing key
4. Delivers message to consuming applications
```

**What happens**: RabbitMQ server handles message routing and delivery.

## 📥 Listening Control Flow

### Listening Example: Scheduler Event

#### Step 1: RabbitMQ Delivers Message

```
External Scheduler publishes:
- Exchange: ex.scheduler
- Routing Key: scheduler.collect.usage
- Payload: {}
```

**What happens**: External system publishes an event that our system is listening for.

#### Step 2: Core Message Handler (`app/services/rabbitmq_service.py`)

```python
# File: app/services/rabbitmq_service.py
async def message_handler(message):
    try:
        # 1. Update consumer heartbeat
        current_time = time.time()
        for queue_name in self.is_consuming:
            self.last_heartbeat[queue_name] = current_time
        
        # 2. Extract routing key
        routing_key = message.routing_key  # "scheduler.collect.usage"
        
        # 3. Parse JSON payload
        if message.body:
            body = json.loads(message.body.decode())  # {}
        else:
            body = {}
        
        # 4. Route to specific handler
        if routing_key in self.event_handlers:
            handler = self.event_handlers[routing_key]  # handle_collect_usage_event
            await handler(body, message)  # ← Goes to Step 3
        
        # 5. Acknowledge message
        await message.ack()
        
    except Exception as e:
        # 6. Reject message on error
        await message.reject(requeue=False)
```

**What happens**: Core message routing and acknowledgment logic.

#### Step 3: Specific Event Handler (`app/services/event_listeners.py`)

```python
# File: app/services/event_listeners.py
async def handle_collect_usage_event(self, payload, message):
    # 1. Log event reception
    logger.info("Processing scheduler.collect.usage event")
    
    # 2. Perform usage collection
    await self._perform_usage_collection()  # ← Goes to Step 4

async def _perform_usage_collection(self):
    # 1. Import ChatbotService
    from app.services.chatbot_service import ChatbotService
    
    # 2. Collect usage data
    chatbot_service = ChatbotService()
    await chatbot_service.collect_and_publish_chatbot_usage()  # ← Triggers publishing
    
    # 3. Perform other collection tasks
    await self._collect_token_usage_stats()
    await self._generate_usage_reports()
```

**What happens**: Business logic processes the event and may trigger additional publishing.

#### Step 4: Usage Collection & Publishing (`app/services/chatbot_service.py`)

```python
# File: app/services/chatbot_service.py
async def collect_and_publish_chatbot_usage(self):
    # 1. Query database for usage statistics
    usage_data = []
    for tenant in tenants:
        count = db.query(ChatbotConversation).filter(
            ChatbotConversation.tenant_id == tenant.id
        ).count()
        usage_data.append({
            "tenantId": tenant.id,
            "usageEntity": "CHATBOT", 
            "count": count
        })
    
    # 2. Publish usage data back to RabbitMQ
    await rabbitmq_manager.publish_usage_data("usage.collect.response", usage_data)
```

**What happens**: Collects actual usage data and publishes results back to RabbitMQ.

## 🔄 Complete End-to-End Flow

### Usage Collection Cycle

```
1. External Scheduler
   ↓ publishes to ex.scheduler
   
2. RabbitMQ Server  
   ↓ routes to whatsapp-chatbot.scheduler.events queue
   
3. message_handler()
   ↓ routes by routing key "scheduler.collect.usage"
   
4. handle_collect_usage_event()
   ↓ triggers usage collection
   
5. collect_and_publish_chatbot_usage()
   ↓ queries database & publishes results
   
6. publish_usage_data()
   ↓ publishes to ex.usage exchange
   
7. RabbitMQ Server
   ↓ delivers to external systems listening for usage data
```

### User Message Processing Cycle

```
1. WhatsApp System
   ↓ publishes user message to ex.message
   
2. RabbitMQ Server
   ↓ routes to q.message.chatbot.user.response.chatbot queue
   
3. message_handler()
   ↓ routes by routing key "message.chatbot.user.response"
   
4. handle_user_message_event()
   ↓ extracts message & conversation ID
   
5. _process_conversation_message()
   ↓ processes chatbot conversation logic
   
6. Chatbot Response Publishing
   ↓ publishes response to ex.chatbot exchange
   
7. RabbitMQ Server
   ↓ delivers response to WhatsApp system
```

This control flow ensures reliable, asynchronous message processing with automatic retry, error handling, and monitoring throughout the entire system.
