# Event-Driven Conversation Architecture

## Overview

This document describes the new event-driven conversation architecture that replaces the previous flow-through API approach. The new system uses RabbitMQ message queues to handle conversation flow asynchronously, providing better scalability and decoupling.

## Architecture Components

### 1. Conversation Event Publisher

**File**: `app/services/conversation_event_publisher.py`

**Purpose**: Publishes chatbot conversation responses to the message queue system.

**Exchange**: `ex.whatsappChatbot`
**Routing Key**: `chatbot.conversation.response`

**Payload Structure**:
```json
{
  "chatbotConversationId": "conv-uuid",
  "message": "Thank you, <PERSON>! What's your email address?",
  "completed": false,
  "charge": 1
}
```

**Key Methods**:
- `publish_conversation_response()` - Publish conversation responses (including first question)
- `publish_conversation_completion()` - Publish conversation completion events

### 2. Message Event Listener

**File**: `app/services/message_event_listener.py`

**Purpose**: Listens for incoming user messages and processes them through the conversation flow.

**Exchange**: `ex.message`
**Queue**: `q.message.chatbot.user.response.chatbot`
**Routing Key**: `message.chatbot.user.response`

**Payload Structure**:
```json
{
  "message": "Hi, I need your help!",
  "chatbotConversationId": "1234567890abcdef",
  "completed": false
}
```

**Key Methods**:
- `handle_user_message_event()` - Main event handler
- `_handle_question_phase()` - Process question-answer flow
- `_handle_knowledge_phase()` - Process knowledge search phase
- `_handle_completion_phase()` - Process conversation completion

### 3. Charge Calculator

**File**: `app/services/charge_calculator.py`

**Purpose**: Calculates charges for conversation events based on question types.

**Charge Rules**:
- Predefined questions: 1 credit
- Custom/LLM generated questions: 2 credits
- Completion messages: 0 credits

**Key Methods**:
- `calculate_question_charge()` - Calculate charge for individual questions
- `calculate_conversation_charge()` - Calculate charge for conversation state
- `get_charge_summary()` - Get complete charge breakdown

## Event Flow

### 1. Starting a Conversation

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Publisher
    participant Queue
    participant Consumer

    Client->>API: POST /conversations
    API->>Client: Welcome Message
    API->>Publisher: publish_conversation_response()
    Publisher->>Queue: ex.whatsappChatbot
    Queue->>Consumer: chatbot.conversation.response
    Consumer->>Client: First Question (via event)
```

### 2. Continuing a Conversation

```mermaid
sequenceDiagram
    participant Client
    participant Queue1 as ex.message
    participant Listener
    participant Publisher
    participant Queue2 as ex.whatsappChatbot
    participant Consumer
    
    Client->>Queue1: User Message
    Queue1->>Listener: message.chatbot.user.response
    Listener->>Listener: Process Answer
    Listener->>Publisher: publish_conversation_response()
    Publisher->>Queue2: Next Question
    Queue2->>Consumer: chatbot.conversation.response
    Consumer->>Client: Next Question
```

## API Changes

### Start Conversation Endpoint

**Current Flow** (Hybrid API + Events):
```json
POST /v1/chatbot/conversations
Response: {
  "chatbotConversationId": "uuid",
  "message": "Welcome to our chatbot!"  // Welcome message in API response
}

// First question sent via event to ex.whatsappChatbot:
{
  "chatbotConversationId": "uuid",
  "message": "What is your name?",  // First question via event
  "completed": false,
  "charge": 1
}
```

### Continue Conversation Endpoint

**Status**: ACTIVE (No changes)

The `POST /conversations/{conversation_id}` endpoint remains unchanged and fully functional. However, for better scalability, consider migrating to the event-driven flow where user messages are published to the `ex.message` exchange.

## Configuration

### RabbitMQ Setup

The system automatically sets up the required exchanges and queues:

**Exchanges**:
- `ex.whatsappChatbot` (topic) - For chatbot responses
- `ex.message` (topic) - For user messages

**Queues**:
- `q.message.chatbot.user.response.chatbot` - User message queue

**Routing Keys**:
- `chatbot.conversation.response` - Chatbot responses
- `message.chatbot.user.response` - User messages

### Environment Variables

```bash
RABBITMQ_URL=amqp://user:pass@host:5672/
RABBITMQ_CONNECTION_TIMEOUT=30
RABBITMQ_HEARTBEAT=300
RABBITMQ_CONSUMER_TIMEOUT=0
SERVICE_NAME=whatsapp-chatbot
```

## Startup and Initialization

### FastAPI Integration

```python
from app.startup_events import lifespan

app = FastAPI(lifespan=lifespan)
```

### Manual Startup

```python
from app.startup_events import manual_startup, manual_shutdown

# Start the system
await manual_startup()

# Your application logic here

# Shutdown the system
await manual_shutdown()
```

## Testing

### Running Tests

```bash
# Run event-driven conversation tests
pytest test/test_event_driven_conversation.py -v

# Run all tests
pytest test/ -v
```

### Test Coverage

The test suite covers:
- Conversation event publishing
- Message event listening
- Charge calculation logic
- Event infrastructure setup
- Error handling and edge cases

## Migration Guide

### For Existing Clients

1. **Immediate**: Continue using existing APIs (deprecated but functional)
2. **Short-term**: Implement event listeners for chatbot responses
3. **Long-term**: Migrate to publishing user messages to `ex.message` exchange

### Migration Steps

1. **Setup Event Listeners**:
   - Listen to `ex.whatsappChatbot` exchange
   - Handle `chatbot.conversation.response` routing key
   - Parse conversation response payloads

2. **Update Message Publishing**:
   - Publish user messages to `ex.message` exchange
   - Use `message.chatbot.user.response` routing key
   - Include conversation ID in payload

3. **Remove Deprecated API Calls**:
   - Stop calling `POST /conversations/{id}` endpoint
   - Use event-driven flow exclusively

## Monitoring and Health Checks

### Health Check Endpoint

```python
from app.startup_events import event_system_health

@app.get("/health/events")
async def health_events():
    return await event_system_health()
```

### Monitoring Points

- RabbitMQ connection status
- Event listener status
- Message processing rates
- Error rates and failed messages
- Charge calculation accuracy

## Benefits

1. **Scalability**: Asynchronous processing allows better resource utilization
2. **Decoupling**: Services can be developed and deployed independently
3. **Reliability**: Message queues provide durability and retry mechanisms
4. **Monitoring**: Better observability into conversation flow
5. **Flexibility**: Easy to add new event types and handlers

## Troubleshooting

### Common Issues

1. **RabbitMQ Connection Failed**:
   - Check connection string and credentials
   - Verify RabbitMQ server is running
   - Check network connectivity

2. **Events Not Being Processed**:
   - Verify event listeners are started
   - Check queue bindings and routing keys
   - Review message format and structure

3. **Charge Calculation Errors**:
   - Verify question metadata (predefined vs custom)
   - Check conversation state structure
   - Review charge calculation logic

### Debug Mode

Enable debug logging for detailed event processing information:

```python
import logging
logging.getLogger('app.services').setLevel(logging.DEBUG)
```
