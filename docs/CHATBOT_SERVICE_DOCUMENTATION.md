# ChatbotService Documentation

## Overview

This document describes the creation and implementation of the `ChatbotService` class, which centralizes chatbot-related operations and moves the chatbot usage collection logic from the event listeners to a dedicated service.

## Changes Made

### 1. Created ChatbotService (`app/services/chatbot_service.py`)

A new service class that handles all chatbot-related operations:

#### Key Features:
- **Usage Collection**: Collects chatbot usage data by tenant and publishes to RabbitMQ
- **Chatbot Counting**: Provides methods to count chatbots by tenant
- **Chatbot Retrieval**: Methods to get chatbots by ID, tenant, or all tenants
- **Statistics**: Comprehensive chatbot statistics generation
- **Proper Error Handling**: Robust error handling with logging
- **Database Management**: Proper database session management with cleanup

#### Main Methods:

1. **`collect_and_publish_chatbot_usage()`** (async)
   - Collects non-DRAFT chatbots grouped by tenant
   - Formats data for usage reporting
   - Publishes to RabbitMQ exchange with routing key "usage.collected"

2. **`get_chatbot_count_by_tenant(tenant_id, include_draft=False)`**
   - Returns count of chatbots for a specific tenant
   - Option to include or exclude DRAFT status chatbots

3. **`get_all_tenant_chatbot_counts(include_draft=False)`**
   - Returns chatbot counts for all tenants
   - Returns list of dictionaries with tenant_id and count

4. **`get_chatbot_by_id(chatbot_id, tenant_id)`**
   - Retrieves a specific chatbot by ID and tenant

5. **`get_chatbots_by_tenant(tenant_id, include_draft=True)`**
   - Gets all chatbots for a specific tenant

6. **`get_chatbot_statistics(tenant_id=None)`**
   - Comprehensive statistics for chatbots
   - Can be filtered by tenant or global statistics

### 2. Updated Event Listeners (`app/services/event_listeners.py`)

#### Changes Made:
- **Removed Direct Database Logic**: Moved chatbot usage collection logic to ChatbotService
- **Simplified Imports**: Removed unused imports (Chatbot model, func from sqlalchemy, get_db)
- **Cleaner Code**: The `_perform_usage_collection` method now delegates to ChatbotService

#### Before:
```python
async def _perform_usage_collection(self):
    # Direct database queries and RabbitMQ publishing
    db = next(get_db())
    usage_data = db.query(Chatbot.tenant_id, func.count(Chatbot.id))...
    # ... complex logic
```

#### After:
```python
async def _perform_usage_collection(self):
    from app.services.chatbot_service import ChatbotService
    
    chatbot_service = ChatbotService()
    await chatbot_service.collect_and_publish_chatbot_usage()
    # ... other usage collection tasks
```

### 3. Created Test Suite (`test_chatbot_service.py`)

Comprehensive test suite to verify:
- Service import and instantiation
- Method availability and structure
- Integration with event listeners
- Mocked usage collection functionality
- File structure validation

## Benefits

### 1. **Separation of Concerns**
- Event listeners focus on event handling
- ChatbotService focuses on chatbot business logic
- Clear responsibility boundaries

### 2. **Reusability**
- ChatbotService can be used by other parts of the application
- Methods are available for API endpoints, background tasks, etc.

### 3. **Maintainability**
- Centralized chatbot logic makes updates easier
- Better error handling and logging
- Easier to test individual components

### 4. **Scalability**
- Service can be extended with additional chatbot operations
- Database session management is properly handled
- Async support for non-blocking operations

## Usage Examples

### Basic Usage Collection
```python
from app.services.chatbot_service import ChatbotService

service = ChatbotService()
await service.collect_and_publish_chatbot_usage()
```

### Get Chatbot Count for Tenant
```python
service = ChatbotService()
count = service.get_chatbot_count_by_tenant("tenant123")
print(f"Tenant has {count} active chatbots")
```

### Get Statistics
```python
service = ChatbotService()
stats = service.get_chatbot_statistics()
print(f"Total chatbots: {stats['total_chatbots']}")
print(f"Active chatbots: {stats['active_chatbots']}")
```

## Integration with Existing System

### RabbitMQ Integration
- Uses existing `rabbitmq_service` for publishing events
- Maintains the same event format and routing key
- No changes required to consumers of usage events

### Database Integration
- Uses existing database models and connection management
- Proper session cleanup with try/finally blocks
- Compatible with existing database configuration

### Event Listener Integration
- Seamlessly integrates with existing SchedulerEventListener
- No changes to external event handling interface
- Maintains backward compatibility

## File Structure

```
app/services/
├── chatbot_service.py          # New ChatbotService class
├── event_listeners.py          # Updated to use ChatbotService
├── rabbitmq_service.py         # Unchanged
├── elasticsearch_service.py    # Unchanged
├── redis_service.py           # Unchanged
└── s3_service.py              # Unchanged

test_chatbot_service.py         # New test suite
```

## Testing

Run the test suite to verify the implementation:

```bash
python3 test_chatbot_service.py
```

The test suite validates:
- ✅ Service structure and methods
- ✅ Integration with event listeners
- ✅ Mocked usage collection functionality
- ✅ File syntax and imports

## Future Enhancements

The ChatbotService can be extended with additional functionality:

1. **Chatbot Analytics**: Add methods for conversation analytics
2. **Performance Metrics**: Track chatbot performance metrics
3. **Bulk Operations**: Add bulk update/delete operations
4. **Caching**: Add Redis caching for frequently accessed data
5. **Validation**: Add chatbot configuration validation methods

## Conclusion

The ChatbotService successfully centralizes chatbot-related operations while maintaining compatibility with the existing system. The refactoring improves code organization, maintainability, and provides a foundation for future chatbot-related features.
