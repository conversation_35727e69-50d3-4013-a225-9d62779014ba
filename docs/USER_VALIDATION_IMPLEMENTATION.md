# User Validation Implementation

## Overview

This document describes the implementation of user validation and tracking system for the WhatsApp Chatbot application. The system validates users against an IAM service and tracks user actions in chatbot operations.

## Features Implemented

### 1. User Table and Model
- **Table**: `users` with columns:
  - `id` (String, Primary Key) - User ID from IAM
  - `name` (String, Not Null) - User's full name
  - `tenant_id` (BigInteger, Not Null, Indexed) - Tenant ID
  - `email` (String, Nullable) - User's email
  - `permissions` (JSON, Nullable) - User permissions from IAM
  - `created_at` (DateTime with timezone)
  - `updated_at` (DateTime with timezone)

### 2. Chatbot User Tracking
- **Added columns to `chatbots` table**:
  - `created_by` (String, Nullable) - User ID who created the chatbot
  - `updated_by` (String, Nullable) - User ID who last updated the chatbot

### 3. User Service (`app/services/user_service.py`)
- **IAM Integration**: `get_user_from_iam(user_id, token)`
  - Fetches user data from IAM service
  - Extracts and formats user information
  - Handles API errors gracefully
- **Database Operations**:
  - `get_user_by_id()` - Get user from local database
  - `create_or_update_user()` - Create or update user in database
  - `validate_and_get_user()` - Main validation method
  - `get_users_by_ids()` - Batch fetch users for efficient queries

### 4. Enhanced Chatbot Service
- **Updated Methods**:
  - `create_chatbot()` - Now requires `user_id` and `token` parameters
  - `update_chatbot()` - Now requires `user_id` and `token` parameters
  - `configure_chatbot_questions()` - Now requires `user_id` and `token` parameters
  - `upload_multiple_knowledgebase_files()` - Now requires `token` parameter
  - `list_chatbots()` - Returns user information in camelCase format

### 5. Updated API Endpoints
- **All chatbot endpoints now validate users**:
  - `POST /v1/chatbot/` - Create chatbot with user validation
  - `PUT /v1/chatbot/{chatbot_id}` - Update chatbot with user validation
  - `POST /v1/chatbot/{chatbot_id}/questions` - Configure questions with user validation
  - `POST /v1/chatbot/{chatbot_id}/knowledgebase` - Upload files with user validation
  - `GET /v1/chatbot/` - List chatbots with user information

### 6. Database Migration
- **File**: `alembic/versions/20250721172206721_b2c3d4e5f6g7_add_users_table_and_chatbot_user_tracking.py`
- **Creates**: `users` table with proper indexes
- **Adds**: `created_by` and `updated_by` columns to `chatbots` table

## API Response Format

### List Chatbots Response (camelCase)
```json
[
  {
    "id": "chatbot-uuid",
    "name": "My Chatbot",
    "type": "AI",
    "description": "Description",
    "status": "ACTIVE",
    "welcomeMessage": "Welcome!",
    "thankYouMessage": "Thank you!",
    "connectedAccountDisplayName": "Account Name",
    "entityType": "LEAD",
    "connectedAccountId": 123,
    "trigger": "NEW_ENTITY",
    "createdBy": {
      "id": "user-id",
      "name": "John Doe"
    },
    "updatedBy": {
      "id": "user-id",
      "name": "Jane Smith"
    },
    "createdAt": "2025-07-21T10:00:00Z",
    "updatedAt": "2025-07-21T11:00:00Z",
    "questionCount": 5,
    "knowledgebaseCount": 3
  }
]
```

## User Validation Flow

1. **API Request** → Extract JWT token from Authorization header
2. **Authentication** → Validate JWT and extract user_id
3. **User Validation** → Check if user exists in local database
4. **IAM Fallback** → If not found locally, fetch from IAM service
5. **Database Sync** → Create/update user in local database
6. **Operation** → Proceed with chatbot operation
7. **Tracking** → Record user_id in created_by/updated_by fields

## Environment Variables Required

```bash
# IAM Service Configuration
IAM_BASE_PATH=https://your-iam-service.com

# Database Configuration (existing)
DATABASE_URL=postgresql://user:password@host:port/database
```

## Installation and Setup

### 1. Run Database Migration
```bash
# Activate virtual environment
source venv/bin/activate

# Run migration
alembic upgrade head
```

### 2. Set Environment Variables
```bash
# Add to .env file
IAM_BASE_PATH=https://your-iam-service.com
```

### 3. Test Implementation
```bash
# Run test script
python3 test_user_validation_implementation.py
```

## Error Handling

- **User Not Found in IAM**: Returns HTTP 400 with message "Something didn't work as expected."
- **IAM Service Unavailable**: Returns HTTP 400 with connection error details
- **Invalid User**: Returns HTTP 500 with "User validation failed"
- **Missing IAM_BASE_PATH**: Raises ValueError with descriptive message

## Security Considerations

- User validation occurs on every API call that modifies data
- JWT tokens are validated before user lookup
- IAM service calls use Bearer token authentication
- User permissions are stored locally for potential future use
- All user operations are logged for audit trails

## Performance Optimizations

- **Batch User Fetching**: `get_users_by_ids()` for efficient list operations
- **Local Caching**: Users stored in database to reduce IAM calls
- **Lazy Loading**: User validation only when needed
- **Database Indexes**: Proper indexing on user_id and tenant_id

## Testing

The implementation includes a comprehensive test script that verifies:
- Model imports and structure
- Service method signatures
- Migration file content
- Database schema updates
- API endpoint integration

Run tests with: `python3 test_user_validation_implementation.py`

## Future Enhancements

1. **User Permission Validation**: Use stored permissions for authorization
2. **User Caching**: Implement Redis caching for frequently accessed users
3. **Audit Logging**: Enhanced logging for user actions
4. **User Management APIs**: CRUD operations for user management
5. **Bulk Operations**: Batch user validation for bulk operations
