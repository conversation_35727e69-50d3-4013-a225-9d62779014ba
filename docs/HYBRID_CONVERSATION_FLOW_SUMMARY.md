# Hybrid Conversation Flow - Final Implementation

## Overview

The conversation system now uses a **hybrid approach** that combines the existing API flow with event-driven architecture:

1. **Start Conversation API**: Returns conversation ID and welcome message (unchanged behavior)
2. **First Question**: Sent via event to `ex.whatsappChatbot` exchange
3. **Subsequent Flow**: All conversation continues through events

## Implementation Details

### 1. Start Conversation Flow

**API Call**:
```bash
POST /v1/chatbot/conversations
```

**API Response** (unchanged):
```json
{
  "chatbotConversationId": "conv-uuid-123",
  "message": "Welcome to our chatbot!"
}
```

**Event Published** (new):
```json
{
  "chatbotConversationId": "conv-uuid-123",
  "message": "What is your name?",
  "completed": false,
  "charge": 1
}
```
- **Exchange**: `ex.whatsappChatbot`
- **Routing Key**: `chatbot.conversation.response`

### 2. Continue Conversation Flow

**Option A: Existing API** (still works):
```bash
POST /v1/chatbot/conversations/{conversation_id}
```

**Option B: Event-Driven** (recommended):
- **Publish user message** to `ex.message` exchange
- **Listen for responses** from `ex.whatsappChatbot` exchange

### 3. Event Payloads

**User Message Event**:
```json
{
  "message": "John Doe",
  "chatbotConversationId": "conv-uuid-123",
  "completed": false
}
```
- **Exchange**: `ex.message`
- **Queue**: `q.message.chatbot.user.response.chatbot`
- **Routing Key**: `message.chatbot.user.response`

**Chatbot Response Event**:
```json
{
  "chatbotConversationId": "conv-uuid-123",
  "message": "Thank you, John! What's your email address?",
  "completed": false,
  "charge": 1
}
```
- **Exchange**: `ex.whatsappChatbot`
- **Routing Key**: `chatbot.conversation.response`

## Charge Calculation

### Charge Rules
- **Predefined questions**: 1 credit
- **Custom/LLM generated questions**: 2 credits
- **Completion messages**: 0 credits

### Charge Examples
```json
// Predefined question
{
  "message": "What is your name?",
  "charge": 1
}

// Custom question
{
  "message": "Tell me more about your specific requirements",
  "charge": 2
}

// Completion
{
  "message": "Thank you for your responses!",
  "completed": true,
  "charge": 0
}
```

## Integration Guide

### For WhatsApp Service

1. **Listen to Chatbot Responses**:
   ```python
   # Setup listener for ex.whatsappChatbot exchange
   exchange = "ex.whatsappChatbot"
   routing_key = "chatbot.conversation.response"
   
   # Handle incoming messages
   def handle_chatbot_response(payload):
       conversation_id = payload["chatbotConversationId"]
       message = payload["message"]
       completed = payload["completed"]
       charge = payload["charge"]
       
       # Send message to WhatsApp user
       send_whatsapp_message(conversation_id, message)
       
       # Track charges
       track_conversation_charge(conversation_id, charge)
   ```

2. **Publish User Messages**:
   ```python
   # When user sends message via WhatsApp
   def handle_whatsapp_message(user_message, conversation_id):
       payload = {
           "message": user_message,
           "chatbotConversationId": conversation_id,
           "completed": False
       }
       
       # Publish to ex.message exchange
       publish_message(
           exchange="ex.message",
           routing_key="message.chatbot.user.response",
           payload=payload
       )
   ```

### For Client Applications

1. **Start Conversation**:
   ```javascript
   // Call API to start conversation
   const response = await fetch('/v1/chatbot/conversations', {
       method: 'POST',
       body: JSON.stringify({
           message: "Hi, I need help",
           entityDetails: [{id: 123, entityType: "LEAD"}],
           connectedAccount: {id: 456, name: "Test Account"}
       })
   });
   
   const data = await response.json();
   console.log("Welcome:", data.message);
   console.log("Conversation ID:", data.chatbotConversationId);
   
   // Setup event listener for first question and subsequent responses
   setupEventListener(data.chatbotConversationId);
   ```

2. **Handle Events**:
   ```javascript
   function setupEventListener(conversationId) {
       // Listen for chatbot responses
       eventSource.addEventListener('chatbot.conversation.response', (event) => {
           const data = JSON.parse(event.data);
           
           if (data.chatbotConversationId === conversationId) {
               displayMessage(data.message);
               
               if (data.completed) {
                   console.log("Conversation completed");
               }
               
               // Track charges
               totalCharge += data.charge;
           }
       });
   }
   ```

## Benefits of Hybrid Approach

1. **Backward Compatibility**: Existing API clients continue to work
2. **Gradual Migration**: Can migrate to events incrementally
3. **Scalability**: Event-driven flow handles high volume better
4. **Separation of Concerns**: Welcome message in API, questions via events
5. **Charge Transparency**: Clear charge information in each event

## File Structure

```
app/
├── services/
│   ├── conversation_event_publisher.py    # Publishes chatbot responses
│   ├── message_event_listener.py          # Listens for user messages
│   ├── charge_calculator.py               # Calculates conversation charges
│   └── rabbitmq_service.py                # Extended with new exchanges
├── routers/
│   └── chatbot.py                         # Updated start conversation API
└── startup_events.py                      # Event system initialization

test/
├── test_event_driven_conversation.py      # Event system tests
└── test_hybrid_conversation_flow.py       # Integration tests

docs/
├── EVENT_DRIVEN_CONVERSATION_ARCHITECTURE.md
└── HYBRID_CONVERSATION_FLOW_SUMMARY.md
```

## Next Steps

1. **Test the Implementation**:
   ```bash
   pytest test/test_hybrid_conversation_flow.py -v
   ```

2. **Start Event System**:
   ```python
   from app.startup_events import manual_startup
   await manual_startup()
   ```

3. **Monitor Events**:
   - Check RabbitMQ management interface
   - Monitor event processing rates
   - Track charge calculations

4. **Migrate Clients**:
   - Update WhatsApp service to listen for events
   - Implement user message publishing
   - Test end-to-end flow

The system is now ready for production with the hybrid approach that maintains API compatibility while enabling event-driven scalability!
