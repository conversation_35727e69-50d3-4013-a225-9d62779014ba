# JWT Authentication Fix

## Issue Summary

The application was failing with JWT authentication errors:

```json
{
  "detail": "Authentication error: 401: Invalid authentication credentials: 401: Failed to parse token: JWT decode error: 6 validation errors for JWTPayload iss Field required [type=missing, input_value={'data': {'accessToken': ...}}, input_type=dict] data.refreshToken Field required [type=missing, input_value={'accessToken': '1c77c32f...}}, input_type=dict] data.userId Input should be a valid string [type=string_type, input_value=3841, input_type=int] data.username Field required [type=missing, input_value={'accessToken': '1c77c32f...}}, input_type=dict] data.source Field required [type=missing, input_value={'accessToken': '1c77c32f...}}, input_type=dict] data.meta Field required [type=missing, input_value={'accessToken': '1c77c32f...}}, input_type=dict]; JSON parse error: Expecting value: line 1 column 1 (char 0)"
}
```

## Root Cause Analysis

The JWT validation was failing due to:

1. **Strict Model Validation**: The `JWTPayload` model required all fields to be present
2. **Type Mismatches**: `userId` was being sent as integer but expected as string
3. **Missing Optional Fields**: Many fields like `iss`, `refreshToken`, `username`, `source`, `meta` were missing
4. **Incomplete Permission Objects**: Permission objects were missing required fields like `id`, `name`, `description`, etc.

## Solutions Implemented

### 1. Made JWT Models Flexible

#### Updated TokenData Model
**Before:**
```python
class TokenData(BaseModel):
    accessToken: str
    expiresIn: int
    expiry: int
    tokenType: str
    refreshToken: str
    permissions: List[TokenPermission]
    userId: str
    username: str
    tenantId: int
    source: TokenSource
    meta: TokenMeta
```

**After:**
```python
class TokenData(BaseModel):
    accessToken: str
    expiresIn: Optional[int] = None
    expiry: Optional[int] = None
    tokenType: Optional[str] = "Bearer"
    refreshToken: Optional[str] = None
    permissions: Optional[List[TokenPermission]] = []
    userId: Union[str, int]  # Allow both string and int
    username: Optional[str] = None
    tenantId: Union[str, int]  # Allow both string and int
    source: Optional[TokenSource] = None
    meta: Optional[TokenMeta] = None
```

#### Updated JWTPayload Model
**Added support for both nested and flat token structures:**
```python
class JWTPayload(BaseModel):
    iss: Optional[str] = None
    data: Optional[TokenData] = None
    
    # Alternative flat structure support
    accessToken: Optional[str] = None
    userId: Optional[Union[str, int]] = None
    tenantId: Optional[Union[str, int]] = None
    permissions: Optional[List[TokenPermission]] = []
    username: Optional[str] = None
    refreshToken: Optional[str] = None
    source: Optional[TokenSource] = None
    meta: Optional[TokenMeta] = None
```

#### Made Permission Models Optional
**Before:**
```python
class TokenPermission(BaseModel):
    id: int
    name: str
    description: str
    limits: int
    units: str
    action: TokenAction
```

**After:**
```python
class TokenPermission(BaseModel):
    id: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    limits: Optional[int] = None
    units: Optional[str] = None
    action: Optional[TokenAction] = None
```

### 2. Enhanced JWT Parsing Logic

#### Added Multiple Parsing Approaches
```python
# Approach 1: Try to decode as JWT
try:
    payload_dict = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM], options={"verify_signature": False})
    payload = JWTPayload.model_validate(payload_dict)
except Exception as e:
    error_messages.append(f"JWT decode error: {str(e)}")

# Approach 2: Try to parse as JSON
if payload is None:
    try:
        payload_json = json.loads(token)
        payload = JWTPayload.model_validate(payload_json)
    except Exception as e:
        error_messages.append(f"JSON parse error: {str(e)}")

# Approach 3: Try to create a minimal payload from available data
if payload is None:
    try:
        payload_dict = jwt.decode(token, options={"verify_signature": False})
        minimal_payload = {
            "iss": payload_dict.get("iss"),
            "data": payload_dict.get("data"),
            "accessToken": payload_dict.get("accessToken"),
            "userId": payload_dict.get("userId") or (payload_dict.get("data", {}).get("userId")),
            "tenantId": payload_dict.get("tenantId") or (payload_dict.get("data", {}).get("tenantId")),
            # ... other fields
        }
        payload = JWTPayload.model_validate(minimal_payload)
    except Exception as e:
        error_messages.append(f"Minimal payload creation error: {str(e)}")
```

### 3. Robust Auth Context Creation

#### Flexible Field Extraction
```python
# Try to extract user_id and tenant_id from various possible locations
if payload.data and hasattr(payload.data, 'userId') and payload.data.userId:
    user_id = str(payload.data.userId)
elif payload.userId:
    user_id = str(payload.userId)

if payload.data and hasattr(payload.data, 'tenantId') and payload.data.tenantId:
    tenant_id = str(payload.data.tenantId)
elif payload.tenantId:
    tenant_id = str(payload.tenantId)
```

## Supported Token Formats

### 1. Nested Structure (Original)
```json
{
  "iss": "issuer",
  "data": {
    "accessToken": "token",
    "userId": "3841",
    "tenantId": 2048,
    "permissions": [...],
    "source": {...},
    "meta": {...}
  }
}
```

### 2. Flat Structure
```json
{
  "iss": "issuer",
  "accessToken": "token",
  "userId": 3841,
  "tenantId": 2048,
  "permissions": [...]
}
```

### 3. Minimal Structure (Problematic Token)
```json
{
  "data": {
    "accessToken": "1c77c32f...",
    "userId": 3841,
    "tenantId": 2048,
    "permissions": [{"action": {"read": true}}]
  }
}
```

## Testing Results

### Before Fix
```
❌ JWT decode error: 6 validation errors for JWTPayload
   - iss: Field required
   - data.refreshToken: Field required  
   - data.userId: Input should be a valid string
   - data.username: Field required
   - data.source: Field required
   - data.meta: Field required
```

### After Fix
```
✅ All authentication tests passed!
   - Problematic Token: ✅ PASS
   - Minimal Token: ✅ PASS
   - Flat Token: ✅ PASS
   - Missing Credentials: ✅ PASS
```

## Key Features

### 1. **Backward Compatibility**
- Supports existing token formats
- No breaking changes for current clients

### 2. **Type Flexibility**
- Accepts both string and integer for `userId` and `tenantId`
- Automatically converts to string for internal use

### 3. **Optional Fields**
- All non-essential fields are optional
- Graceful handling of missing fields

### 4. **Multiple Token Structures**
- Supports nested (`data` object) and flat structures
- Automatic detection and parsing

### 5. **Robust Error Handling**
- Multiple parsing attempts
- Detailed error messages for debugging
- Graceful fallbacks

## Files Modified

1. **`app/models.py`**
   - Made JWT models flexible with optional fields
   - Added support for Union types (string/int)
   - Updated permission models

2. **`app/dependencies.py`**
   - Enhanced JWT parsing with multiple approaches
   - Robust auth context creation
   - Better error handling and debugging

3. **`test_jwt_debug.py`** (new)
   - JWT token debugging tool
   - Model validation testing

4. **`test_auth_fix.py`** (new)
   - Comprehensive authentication testing
   - Multiple token format validation

## Usage Examples

### Valid Authentication Headers
```bash
# With nested structure
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# With flat structure  
Authorization: Bearer eyJpc3MiOiJ0ZXN0IiwidXNlcklkIjozODQx...

# With minimal structure (problematic token now works)
Authorization: Bearer eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2M...
```

### Expected Auth Context
```python
AuthContext(
    user_id="3841",      # Always string
    tenant_id="2048",    # Always string  
    source_type="unknown",  # Default if not provided
    source_id="unknown",    # Default if not provided
    source_name="unknown",  # Default if not provided
    permissions=[...]       # List of permissions
)
```

## Debugging Tools

### JWT Debug Script
```bash
python3 test_jwt_debug.py
```

### Authentication Test
```bash
python3 test_auth_fix.py
```

## Recommendations

1. **Client-Side**: Send `userId` as string to avoid type conversion
2. **Include Essential Fields**: While optional, including `iss`, `source`, etc. improves security
3. **Use Environment Variables**: Set `JWT_SECRET_KEY` in environment
4. **Monitor Logs**: Check for authentication debug messages
5. **Test Token Formats**: Use provided tools to validate token structures

The JWT authentication system now handles various token formats gracefully and should resolve the authentication errors you were experiencing!
