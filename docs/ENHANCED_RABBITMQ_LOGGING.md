# Enhanced RabbitMQ Logging Documentation

This document describes the enhanced logging system implemented for RabbitMQ publishers and listeners to provide detailed payload tracking and debugging capabilities.

## Overview

The enhanced logging system provides comprehensive visibility into:
- **Message Publishing**: Detailed logs of outgoing messages with payload content
- **Message Reception**: Complete tracking of incoming messages and processing
- **Event Handling**: Specific logging for different event types
- **Performance Monitoring**: Timing information for publishing and processing
- **Error Tracking**: Enhanced error context and debugging information

## Log Categories

### 📤 Publisher Logging

**Location**: `app/services/rabbitmq_service.py` - `publish_message()` method

**Features**:
- Exchange and routing key identification
- Payload size and content logging
- Message durability settings
- Publishing duration tracking
- Smart payload truncation for large messages

**Example Log Output**:
```
📤 PUBLISHING MESSAGE - Exchange: ex.chatbot
📤 PUBLISHING MESSAGE - Routing Key: chatbot.conversation.started
📤 PUBLISHING MESSAGE - Payload Size: 245 bytes
📤 PUBLISHING MESSAGE - Durable: True
📤 PUBLISHING MESSAGE - Payload: {"conversationId": "conv-123", "message": "Hello"}
✅ MESSAGE PUBLISHED - Exchange: ex.chatbot, Routing Key: chatbot.conversation.started, Publish Time: 0.003s
```

### 📥 Listener Logging

**Location**: `app/services/rabbitmq_service.py` - `message_handler()` method

**Features**:
- Message reception details (exchange, routing key, size)
- Content type and payload analysis
- Handler routing information
- Processing time measurement
- Message acknowledgment tracking

**Example Log Output**:
```
📥 RECEIVED MESSAGE - Routing Key: scheduler.collect.usage
📥 RECEIVED MESSAGE - Exchange: ex.scheduler
📥 RECEIVED MESSAGE - Payload Size: 2 bytes
📥 RECEIVED MESSAGE - Content Type: application/json
📥 RECEIVED MESSAGE - Payload: {}
🔄 PROCESSING MESSAGE - Handler found for routing key: scheduler.collect.usage
✅ MESSAGE PROCESSED - Routing Key: scheduler.collect.usage, Processing Time: 0.125s
✅ MESSAGE ACKNOWLEDGED - Routing Key: scheduler.collect.usage
```

### 🎯 Event Handler Logging

**Locations**:
- `app/services/message_event_listener.py` - User message events
- `app/services/event_listeners.py` - Scheduler events

**Features**:
- Event type identification
- Payload structure analysis
- Data extraction logging
- Processing status tracking

**Example Log Output**:
```
🎯 HANDLING USER MESSAGE EVENT
🎯 EVENT HANDLER - Routing Key: message.chatbot.user.response
🎯 EVENT HANDLER - Exchange: ex.message
🎯 EVENT PAYLOAD - Full Payload: {"message": "John Doe", "chatbotConversationId": "conv-123"}
🎯 EXTRACTED DATA - User Message: 'John Doe'
🎯 EXTRACTED DATA - Conversation ID: conv-123
🔄 PROCESSING CONVERSATION - ID: conv-123
✅ USER MESSAGE EVENT PROCESSED SUCCESSFULLY
```

### 🤖 Chatbot Event Logging

**Location**: `app/services/chatbot_event_publisher.py`

**Features**:
- Chatbot-specific event identification
- Payload key analysis
- Sensitive data filtering
- Serialization size tracking

**Example Log Output**:
```
🤖 PUBLISHING CHATBOT EVENT - Type: conversation_started
🤖 CHATBOT EVENT - Routing Key: chatbot.conversation.started
🤖 CHATBOT EVENT - Exchange: ex.chatbot
🤖 CHATBOT EVENT - Payload Keys: ['chatbotId', 'conversationId', 'message', 'timestamp']
🤖 CHATBOT EVENT - Payload: {"chatbotId": "bot-123", "conversationId": "conv-456"}
🤖 CHATBOT EVENT - Serialized Size: 156 bytes
✅ CHATBOT EVENT PUBLISHED - Type: conversation_started, Routing Key: chatbot.conversation.started
```

### 💬 Conversation Event Logging

**Location**: `app/services/conversation_event_publisher.py`

**Features**:
- Conversation-specific event tracking
- Message content logging
- User interaction monitoring

**Example Log Output**:
```
💬 PUBLISHING CONVERSATION EVENT - Type: message_sent
💬 CONVERSATION EVENT - Routing Key: conversation.message.sent
💬 CONVERSATION EVENT - Exchange: ex.conversation
💬 CONVERSATION EVENT - Payload Keys: ['conversationId', 'userId', 'message']
💬 CONVERSATION EVENT - Payload: {"conversationId": "conv-789", "message": "Hello"}
💬 CONVERSATION EVENT - Serialized Size: 89 bytes
✅ CONVERSATION EVENT PUBLISHED - Type: message_sent, Routing Key: conversation.message.sent
```

### 📊 Usage Data Logging

**Location**: `app/services/rabbitmq_manager.py`

**Features**:
- Usage data collection tracking
- Data volume monitoring
- Publishing status verification

**Example Log Output**:
```
📊 RABBITMQ MANAGER - Publishing usage data with routing key: usage.collect.response
📊 RABBITMQ MANAGER - Usage data type: <class 'list'>
📊 RABBITMQ MANAGER - Usage data count: 5 items
✅ RABBITMQ MANAGER - Usage data published successfully with routing key: usage.collect.response
```

## Security Features

### Sensitive Data Filtering

The logging system automatically filters sensitive information:
- `token` fields are excluded from logs
- `password` fields are excluded from logs  
- `secret` fields are excluded from logs

### Payload Size Management

- **Small payloads** (< 1000 chars): Logged in full
- **Large payloads** (≥ 1000 chars): Truncated with full payload in debug logs
- **Size reporting**: Always shows actual payload size in bytes

## Performance Monitoring

### Timing Metrics

- **Publishing Time**: Time taken to publish messages to RabbitMQ
- **Processing Time**: Time taken by event handlers to process messages
- **End-to-End Tracking**: Complete message lifecycle timing

### Resource Monitoring

- **Payload Sizes**: Track message sizes for performance optimization
- **Queue Performance**: Monitor processing rates and bottlenecks
- **Connection Health**: Track connection stability and recovery

## Error Handling

### Enhanced Error Context

```
❌ MESSAGE PROCESSING ERROR - Routing Key: scheduler.collect.usage
❌ MESSAGE PROCESSING ERROR - Error: Connection lost during processing
❌ JSON DECODE ERROR - Failed to parse message body: Invalid JSON format
❌ EVENT PUBLISH FAILED - Event: test.event, Error: Exchange not found
```

### Error Categories

- **JSON Decode Errors**: Invalid message format
- **Processing Errors**: Handler execution failures
- **Publishing Errors**: Message delivery failures
- **Connection Errors**: RabbitMQ connectivity issues

## Configuration

### Log Levels

- **INFO**: Standard operational logs (publishing, receiving, processing)
- **DEBUG**: Detailed payload content and internal operations
- **WARNING**: Non-critical issues and fallbacks
- **ERROR**: Critical failures and exceptions

### Environment Variables

The logging respects existing RabbitMQ configuration:
- `RABBITMQ_CONNECTION_TIMEOUT`
- `RABBITMQ_HEARTBEAT`
- `RABBITMQ_CONSUMER_TIMEOUT`
- `RABBITMQ_HEALTH_CHECK_INTERVAL`

## Usage Examples

### Monitoring Message Flow

1. **Check Publishing**: Look for `📤 PUBLISHING MESSAGE` logs
2. **Verify Reception**: Look for `📥 RECEIVED MESSAGE` logs  
3. **Track Processing**: Look for `🔄 PROCESSING MESSAGE` logs
4. **Confirm Completion**: Look for `✅ MESSAGE PROCESSED` logs

### Debugging Issues

1. **Publishing Problems**: Check for `❌ EVENT PUBLISH FAILED` logs
2. **Processing Errors**: Look for `❌ MESSAGE PROCESSING ERROR` logs
3. **Handler Issues**: Check for `⚠️ NO HANDLER` warnings
4. **Payload Problems**: Look for `❌ JSON DECODE ERROR` logs

### Performance Analysis

1. **Publishing Speed**: Check `Publish Time` in success logs
2. **Processing Speed**: Check `Processing Time` in completion logs
3. **Payload Sizes**: Monitor `Payload Size` in publishing logs
4. **Message Volume**: Track message frequency in logs

## Benefits

1. **Complete Visibility**: See exactly what data is being published and received
2. **Easy Debugging**: Quickly identify issues with detailed error context
3. **Performance Monitoring**: Track message processing performance
4. **Security Compliance**: Automatic filtering of sensitive data
5. **Operational Insights**: Understand message flow patterns and volumes
