# RabbitMQ Quick Reference Guide

This is a quick reference for understanding the RabbitMQ system in the WhatsApp Chatbot application.

## 📁 Key Files & Their Roles

| File | Role | Key Functions |
|------|------|---------------|
| `rabbitmq_service.py` | **Core Engine** | Connection, queues, message routing |
| `rabbitmq_manager.py` | **Orchestrator** | Startup, health monitoring, high-level API |
| `event_listeners.py` | **Scheduler Handler** | Processes usage collection events |
| `message_event_listener.py` | **Message Handler** | Processes user messages from WhatsApp |
| `chatbot_event_publisher.py` | **Chatbot Publisher** | Publishes chatbot lifecycle events |
| `conversation_event_publisher.py` | **Conversation Publisher** | Publishes conversation flow events |

## 🏗️ System Architecture

### Exchanges (Topic-based routing)
- `ex.scheduler` → Scheduler events
- `ex.message` → User messages from WhatsApp
- `ex.chatbot` → Chatbot lifecycle events
- `ex.conversation` → Conversation flow events
- `ex.usage` → Usage data collection
- `ex.events` → General application events

### Queues (Message storage)
- `whatsapp-chatbot.scheduler.events` → Scheduler events queue
- `q.message.chatbot.user.response.chatbot` → User messages queue

### Routing Keys (Message routing patterns)
- `scheduler.collect.usage` → Trigger usage collection
- `message.chatbot.user.response` → User message from WhatsApp
- `usage.collect.response` → Usage data results
- `chatbot.conversation.*` → Chatbot events
- `conversation.message.*` → Conversation events

## 🚀 How It All Works

### 1. System Startup
```
FastAPI starts → RabbitMQ Manager → RabbitMQ Service → Connect to server
                                                    → Declare exchanges
                                                    → Declare queues  
                                                    → Bind queues to exchanges
                                                    → Start consuming messages
                                                    → Start health monitoring
```

### 2. Publishing Messages
```
Application Code → RabbitMQ Manager → RabbitMQ Service → Serialize to JSON
                                                      → Create AMQP message
                                                      → Publish to exchange
                                                      → RabbitMQ routes to queues
```

### 3. Receiving Messages
```
RabbitMQ delivers → message_handler() → Parse JSON payload
                                     → Route by routing key
                                     → Call specific event handler
                                     → Process business logic
                                     → Acknowledge message
```

## 📤 Publishing Examples

### Publish Usage Data
```python
# High-level API
await rabbitmq_manager.publish_usage_data("usage.collect.response", usage_data)

# What happens:
# 1. Checks if RabbitMQ is running
# 2. Calls rabbitmq_service.publish_message("ex.usage", routing_key, data)
# 3. Serializes data to JSON
# 4. Publishes to ex.usage exchange
# 5. RabbitMQ routes to bound queues
```

### Publish Chatbot Event
```python
# Specialized publisher
await chatbot_event_publisher.publish_conversation_started(payload)

# What happens:
# 1. Adds event-specific logging
# 2. Filters sensitive data
# 3. Calls rabbitmq_service.publish_message("ex.chatbot", routing_key, payload)
# 4. Publishes to ex.chatbot exchange
```

## 📥 Listening Examples

### Scheduler Event Processing
```python
# Event arrives: scheduler.collect.usage with payload {}
# 1. message_handler() receives message
# 2. Routes to handle_collect_usage_event() based on routing key
# 3. Event handler processes usage collection
# 4. Calls ChatbotService to collect data from database
# 5. Publishes results back to ex.usage exchange
```

### User Message Processing
```python
# Event arrives: message.chatbot.user.response with payload {message, conversationId}
# 1. message_handler() receives message
# 2. Routes to handle_user_message_event() based on routing key
# 3. Extracts user message and conversation ID
# 4. Processes conversation using existing chatbot logic
# 5. May publish response events back to ex.chatbot
```

## 🔧 Configuration

### Environment Variables
```bash
RABBITMQ_CONNECTION_TIMEOUT=30      # Connection timeout
RABBITMQ_HEARTBEAT=300             # Heartbeat interval (5 min)
RABBITMQ_CONSUMER_TIMEOUT=0        # Consumer timeout (no limit)
RABBITMQ_HEALTH_CHECK_INTERVAL=60  # Health check interval (1 min)
SERVICE_NAME=whatsapp-chatbot      # Service identifier
```

### Connection Details
```python
# RabbitMQ server connection
URL: "amqp://sling_sales:test@rabbitmq:5672/"
Heartbeat: 300 seconds (5 minutes)
QoS: prefetch_count=1 (process one message at a time)
```

## 🔍 Monitoring & Health

### Health Checks
- **Connection Health**: Monitors RabbitMQ connection status
- **Consumer Health**: Tracks consumer heartbeats and recovery
- **Queue Health**: Monitors queue states and message processing
- **Keepalive**: Sends periodic keepalive signals to maintain connection

### Logging Patterns
- `📤 PUBLISHING MESSAGE` → Outgoing message details
- `📥 RECEIVED MESSAGE` → Incoming message details
- `🔄 PROCESSING MESSAGE` → Event handler processing
- `✅ MESSAGE PROCESSED` → Successful completion
- `❌ MESSAGE PROCESSING ERROR` → Error handling

## 🎯 Common Patterns

### Event-Driven Processing
1. External system publishes event
2. RabbitMQ routes to appropriate queue
3. Event listener processes business logic
4. Results published back to RabbitMQ
5. External systems consume results

### Request-Response Pattern
1. WhatsApp publishes user message
2. Message listener processes conversation
3. Chatbot response published back
4. WhatsApp receives and displays response

### Usage Collection Pattern
1. Scheduler triggers usage collection
2. System queries database for statistics
3. Usage data published to usage exchange
4. External analytics systems consume data

## 🚨 Error Handling

### Automatic Recovery
- **Connection Loss**: Automatic reconnection with exponential backoff
- **Consumer Failure**: Automatic consumer recovery and queue re-binding
- **Message Failure**: Message rejection with no requeue to prevent loops
- **Health Monitoring**: Continuous monitoring with automatic recovery

### Retry Logic
- **Publishing**: 3 retry attempts with increasing delays
- **Connection**: Automatic reconnection on failure
- **Consumer**: Automatic recovery on consumer failure

## 📚 Related Documentation

- `RABBITMQ_ARCHITECTURE_GUIDE.md` → Detailed architecture explanation
- `RABBITMQ_CONTROL_FLOW.md` → Step-by-step control flow
- `ENHANCED_RABBITMQ_LOGGING.md` → Logging system details
- `RABBITMQ_SERVICES_DOCUMENTATION.md` → Service API documentation

## 🔗 Key Integration Points

- **FastAPI**: Startup/shutdown lifecycle management
- **Database**: Conversation and usage data queries
- **Redis**: Conversation state management
- **Elasticsearch**: Knowledge base queries and AI operations
- **WhatsApp**: Message exchange and response delivery

This system provides reliable, scalable, asynchronous message processing with comprehensive monitoring, automatic recovery, and detailed logging for debugging and operational visibility.
