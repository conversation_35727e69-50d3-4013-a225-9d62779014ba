# Elasticsearch Master Index Strategy

## Overview

This document describes the master index strategy implemented for storing knowledge base documents in Elasticsearch for the WhatsApp chatbot system. This strategy optimizes multi-tenant data organization and improves scalability.

## Architecture

### Master Index Strategy

Instead of creating individual indices for each tenant (which can lead to resource overhead), we use a master index approach:

- **Master Indices**: `whatsapp-chatbot-1`, `whatsapp-chatbot-2`, etc.
- **Tenant Aliases**: `tenant-{tenant_id}` pointing to appropriate master indices with tenant filtering
- **Capacity**: Each master index supports up to 100 tenant aliases
- **Auto-scaling**: New master indices are created automatically when capacity is reached
- **Data Isolation**: Aliases include filters to ensure tenant data isolation at the Elasticsearch level

### Benefits

1. **Resource Efficiency**: Reduces the number of Elasticsearch indices
2. **Better Performance**: Fewer indices mean better cluster performance
3. **Simplified Management**: Centralized index management
4. **Scalability**: Automatic scaling as tenant count grows
5. **Multi-tenancy**: Complete data isolation through aliases and filtering

## Implementation Details

### Database Schema

A new table `tenant_index_mappings` tracks the relationship between tenants and master indices:

```sql
CREATE TABLE tenant_index_mappings (
    id VARCHAR PRIMARY KEY,
    tenant_id VARCHAR UNIQUE NOT NULL,
    master_index_number INTEGER NOT NULL,
    alias_name VARCHAR UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);
```

### Index Mapping

Each master index uses the following mapping:

```json
{
  "mappings": {
    "properties": {
      "content": {"type": "text"},
      "document_id": {"type": "keyword"},
      "chatbot_id": {"type": "keyword"},
      "chunk_id": {"type": "keyword"},
      "chunk_index": {"type": "integer"},
      "tenant_id": {"type": "keyword"},
      "embedding": {
        "type": "dense_vector",
        "dims": 1536,
        "index": true,
        "similarity": "cosine"
      }
    }
  }
}
```

### Data Flow

1. **Document Indexing**:
   - Get or create tenant alias for the tenant
   - Index document chunks into the tenant alias
   - Each document includes `tenant_id` for filtering

2. **Search Operations**:
   - Use tenant alias for search
   - Apply tenant_id filter for data isolation
   - Optional chatbot_id filtering for specific chatbots

## Usage

### Indexing Documents

```python
from app.services.elasticsearch_service import ElasticsearchService
from app.database import get_db

es_service = ElasticsearchService()
db = next(get_db())

# Index a document using the new strategy
es_index = es_service.index_document(
    tenant_id="tenant123",
    document_id="doc456",
    content="Document content...",
    chatbot_id="chatbot789",
    chunk_size=300,
    overlap=30,
    db=db  # Required for master index strategy
)
```

### Searching Documents

```python
# Search using the new strategy
search_results = es_service.semantic_search(
    tenant_id="tenant123",
    query="search query",
    chatbot_id="chatbot789",  # Optional
    limit=3,
    db=db  # Required for master index strategy
)
```

## Management Commands

Use the management script for various operations:

### Show Statistics
```bash
python app/scripts/elasticsearch_index_management.py stats
```

### Create Master Indices
```bash
python app/scripts/elasticsearch_index_management.py create-masters --count 3
```

### Migrate Existing Indices
```bash
# Dry run first
python app/scripts/elasticsearch_index_management.py migrate --dry-run

# Actual migration
python app/scripts/elasticsearch_index_management.py migrate
```

### Create Tenant Alias
```bash
python app/scripts/elasticsearch_index_management.py create-alias tenant123
```

### List Tenant Mappings
```bash
python app/scripts/elasticsearch_index_management.py list-mappings
```

### Health Check
```bash
python app/scripts/elasticsearch_index_management.py health
```

## Migration from Old Strategy

### Backward Compatibility

The system maintains backward compatibility:
- Old tenant-specific indices continue to work
- New documents use the master index strategy when `db` parameter is provided
- Gradual migration is supported

### Migration Process

1. **Assessment**: Run `migrate --dry-run` to see what will be migrated
2. **Migration**: Run `migrate` to move data from old indices to new aliases
3. **Verification**: Use `stats` and `health` commands to verify migration
4. **Cleanup**: Optionally remove old indices after verification

## Monitoring and Maintenance

### Key Metrics to Monitor

1. **Index Distribution**: Ensure even distribution across master indices
2. **Capacity Utilization**: Monitor tenant count per master index
3. **Search Performance**: Track query response times
4. **Storage Usage**: Monitor index sizes and growth

### Maintenance Tasks

1. **Regular Health Checks**: Use the health command
2. **Capacity Planning**: Monitor tenant distribution
3. **Performance Optimization**: Adjust shard settings if needed
4. **Backup Strategy**: Ensure proper backup of master indices

## Configuration

### Environment Variables

- `ELASTICSEARCH_URL`: Elasticsearch cluster URL (default: "http://elasticsearch-vector-master:9200")
- `ELASTICSEARCH_INDEX_PREFIX`: Prefix for backward compatibility (default: "kb-")

### Tunable Parameters

- `max_tenants_per_index`: Maximum tenants per master index (default: 100)
- `master_index_prefix`: Prefix for master indices (default: "whatsapp-chatbot")
- `tenant_alias_prefix`: Prefix for tenant aliases (default: "tenant")

## Troubleshooting

### Common Issues

1. **Alias Not Found**: Run `create-alias <tenant_id>` to create missing alias
2. **Search Returns No Results**: Verify tenant_id filtering is working
3. **Index Full**: New master index will be created automatically
4. **Migration Errors**: Check Elasticsearch logs and retry failed tenants

### Debug Commands

```bash
# Check specific tenant mapping
python app/scripts/elasticsearch_index_management.py list-mappings | grep tenant123

# Verify alias exists
curl -X GET "localhost:9200/_alias/tenant-tenant123"

# Check index health
curl -X GET "localhost:9200/_cluster/health?level=indices"
```

## Best Practices

1. **Always pass `db` parameter** for new operations to use master index strategy
2. **Monitor capacity** regularly to ensure optimal distribution
3. **Test migrations** in staging environment first
4. **Keep backups** of both Elasticsearch and PostgreSQL data
5. **Use health checks** in monitoring systems
6. **Plan capacity** based on expected tenant growth

## Future Enhancements

1. **Auto-rebalancing**: Automatically redistribute tenants across indices
2. **Hot/Cold Architecture**: Move older data to cold storage
3. **Advanced Monitoring**: Integration with monitoring systems
4. **Performance Optimization**: Index template optimization
5. **Multi-region Support**: Cross-region replication strategy
