# RabbitMQ Architecture & Flow Guide

This document explains the complete RabbitMQ publishing and listening architecture, including file responsibilities, control flow, and queue initialization.

## 📁 File Structure & Responsibilities

### Core RabbitMQ Files

```
app/services/
├── rabbitmq_service.py          # Core RabbitMQ connection & message handling
├── rabbitmq_manager.py          # High-level RabbitMQ orchestration
├── event_listeners.py           # Scheduler event listener
├── message_event_listener.py    # User message event listener
├── chatbot_event_publisher.py   # Chatbot-specific event publishing
└── conversation_event_publisher.py # Conversation event publishing
```

### File Responsibilities

| File | Purpose | Key Functions |
|------|---------|---------------|
| `rabbitmq_service.py` | Core RabbitMQ operations | Connection, queues, exchanges, message handling |
| `rabbitmq_manager.py` | Service orchestration | Startup, shutdown, health monitoring |
| `event_listeners.py` | Scheduler events | Usage collection event handling |
| `message_event_listener.py` | User messages | WhatsApp user message processing |
| `chatbot_event_publisher.py` | Chatbot events | Conversation lifecycle events |
| `conversation_event_publisher.py` | Conversation events | Message flow events |

## 🚀 System Initialization Flow

### 1. Application Startup (`app/main.py`)

```python
@app.on_event("startup")
async def startup_event():
    # Initialize RabbitMQ services
    await rabbitmq_manager.initialize()
    await rabbitmq_manager.start()
```

### 2. RabbitMQ Manager Initialization (`rabbitmq_manager.py`)

```python
async def initialize():
    # 1. Connect to RabbitMQ server
    connected = await rabbitmq_service.connect()
    
    # 2. Setup publishers (declare exchanges)
    await rabbitmq_service.setup_all_publishers()
    
    # 3. Setup scheduler listener (declare queues & bindings)
    await rabbitmq_service.setup_scheduler_listener()
```

### 3. Core Service Connection (`rabbitmq_service.py`)

```python
async def connect():
    # 1. Establish robust connection to RabbitMQ
    self.connection = await connect_robust(self.rabbitmq_url)
    
    # 2. Create channel
    self.channel = await self.connection.channel()
    
    # 3. Set QoS (prefetch_count=1)
    await self.channel.set_qos(prefetch_count=1)
    
    # 4. Start health check and keepalive tasks
    self.health_check_task = asyncio.create_task(self._health_check_loop())
    self.keepalive_task = asyncio.create_task(self._keepalive_loop())
```

## 🏗️ Queue & Exchange Initialization

### Exchange Declaration

**Location**: `rabbitmq_service.py` - `declare_exchange()`

```python
# Exchanges created during startup:
await self.declare_exchange("ex.scheduler", "topic")    # Scheduler events
await self.declare_exchange("ex.message", "topic")      # User messages  
await self.declare_exchange("ex.chatbot", "topic")      # Chatbot events
await self.declare_exchange("ex.conversation", "topic") # Conversation events
await self.declare_exchange("ex.usage", "topic")        # Usage data
await self.declare_exchange("ex.events", "topic")       # General events
```

### Queue Declaration & Binding

**Location**: `rabbitmq_service.py` - Various setup methods

#### Scheduler Queue Setup
```python
# Queue: whatsapp-chatbot.scheduler.events
# Exchange: ex.scheduler
# Routing Key: scheduler.collect.usage
await self.setup_scheduler_listener()
```

#### Message Queue Setup  
```python
# Queue: q.message.chatbot.user.response.chatbot
# Exchange: ex.message
# Routing Key: message.chatbot.user.response
await self.setup_message_listener()
```

## 📤 Publishing Flow

### 1. High-Level Publishing (`rabbitmq_manager.py`)

```python
# Entry point for publishing events
await rabbitmq_manager.publish_event("event.name", payload)
await rabbitmq_manager.publish_usage_data("routing.key", data)
```

### 2. Service-Level Publishing (`rabbitmq_service.py`)

```python
async def publish_message(exchange, routing_key, message, durable=True):
    # 1. Ensure connection is healthy
    await self._ensure_connection()
    
    # 2. Ensure exchange exists
    await self.declare_exchange(exchange)
    
    # 3. Serialize message to JSON
    message_body = json.dumps(message)
    
    # 4. Create AMQP message
    msg = Message(message_body.encode(), delivery_mode=2)
    
    # 5. Publish to exchange
    await self.exchanges[exchange].publish(msg, routing_key=routing_key)
```

### 3. Specialized Publishers

#### Chatbot Events (`chatbot_event_publisher.py`)
```python
# Publishes to: ex.chatbot
await chatbot_event_publisher.publish_conversation_started(payload)
await chatbot_event_publisher.publish_conversation_completed(payload)
await chatbot_event_publisher.publish_question_asked(payload)
```

#### Conversation Events (`conversation_event_publisher.py`)
```python  
# Publishes to: ex.conversation
await conversation_event_publisher.publish_conversation_message_sent(payload)
await conversation_event_publisher.publish_conversation_updated(payload)
```

## 📥 Listening Flow

### 1. Event Handler Registration

**Location**: `rabbitmq_service.py` - `register_event_handler()`

```python
# Register handlers for specific routing keys
self.event_handlers = {
    "scheduler.collect.usage": self.handle_collect_usage_event,
    "message.chatbot.user.response": message_event_listener.handle_user_message_event
}
```

### 2. Message Consumption Start

**Location**: `rabbitmq_service.py` - `start_consuming()`

```python
async def start_consuming(queue_name):
    # 1. Get queue reference
    queue = self.queues[queue_name]
    
    # 2. Start consuming with message handler
    consumer_tag = await queue.consume(
        self.message_handler,  # Routes to specific handlers
        no_ack=False,
        exclusive=False
    )
    
    # 3. Track consumer state
    self.consumer_tags[queue_name] = consumer_tag
    self.is_consuming[queue_name] = True
```

### 3. Message Processing Pipeline

**Location**: `rabbitmq_service.py` - `message_handler()`

```python
async def message_handler(message):
    # 1. Parse JSON payload
    body = json.loads(message.body.decode())
    routing_key = message.routing_key
    
    # 2. Route to specific handler
    if routing_key in self.event_handlers:
        handler = self.event_handlers[routing_key]
        await handler(body, message)
    
    # 3. Acknowledge message
    await message.ack()
```

## 🎯 Event Handler Details

### Scheduler Event Handler (`event_listeners.py`)

```python
class SchedulerEventListener:
    async def handle_collect_usage_event(self, payload, message):
        # 1. Process usage collection trigger
        # 2. Delegate to ChatbotService for actual collection
        # 3. Publish usage data back to ex.usage
        await self._perform_usage_collection()
```

**Flow**:
1. Receives `scheduler.collect.usage` event (empty payload `{}`)
2. Triggers usage collection from database
3. Publishes collected data to `ex.usage` with routing key `usage.collect.response`

### Message Event Handler (`message_event_listener.py`)

```python
class MessageEventListener:
    async def handle_user_message_event(self, payload, message):
        # 1. Extract user message and conversation ID
        user_message = payload.get("message")
        conversation_id = payload.get("chatbotConversationId")
        
        # 2. Process conversation logic
        await self._process_conversation_message(conversation_id, user_message)
```

**Flow**:
1. Receives `message.chatbot.user.response` event
2. Extracts user message and conversation ID from payload
3. Processes conversation using existing chatbot logic
4. May publish response events back to conversation exchange

## 🔄 Complete Message Flow Examples

### Example 1: Usage Collection Flow

```
1. External Scheduler → publishes to ex.scheduler
   Routing Key: scheduler.collect.usage
   Payload: {}

2. SchedulerEventListener → receives event
   Queue: whatsapp-chatbot.scheduler.events
   Handler: handle_collect_usage_event()

3. ChatbotService → collects usage data
   Database queries for conversation statistics

4. Usage Publisher → publishes results
   Exchange: ex.usage
   Routing Key: usage.collect.response
   Payload: [{"tenantId": "...", "count": 5}, ...]
```

### Example 2: User Message Flow

```
1. WhatsApp System → publishes user message
   Exchange: ex.message
   Routing Key: message.chatbot.user.response
   Payload: {"message": "John Doe", "chatbotConversationId": "conv-123"}

2. MessageEventListener → receives message
   Queue: q.message.chatbot.user.response.chatbot
   Handler: handle_user_message_event()

3. Conversation Processing → processes user input
   Updates conversation state, generates response

4. Response Publishing → publishes chatbot response
   Exchange: ex.chatbot
   Routing Key: chatbot.conversation.response
   Payload: {"conversationId": "conv-123", "response": "..."}
```

## 🔧 Configuration & Control

### Environment Variables

```bash
RABBITMQ_CONNECTION_TIMEOUT=30      # Connection timeout in seconds
RABBITMQ_HEARTBEAT=300             # Heartbeat interval (5 minutes)
RABBITMQ_CONSUMER_TIMEOUT=0        # Consumer timeout (0 = no timeout)
RABBITMQ_HEALTH_CHECK_INTERVAL=60  # Health check interval (1 minute)
SERVICE_NAME=whatsapp-chatbot      # Service identifier for queues
```

### Queue Naming Convention

```
Format: {SERVICE_NAME}.{COMPONENT}.{PURPOSE}
Examples:
- whatsapp-chatbot.scheduler.events
- q.message.chatbot.user.response.chatbot
```

### Exchange & Routing Key Patterns

```
Exchanges:
- ex.scheduler  → Scheduler events
- ex.message    → User messages
- ex.chatbot    → Chatbot lifecycle events  
- ex.conversation → Conversation flow events
- ex.usage      → Usage data
- ex.events     → General application events

Routing Keys:
- scheduler.collect.usage
- message.chatbot.user.response
- chatbot.conversation.started
- conversation.message.sent
- usage.collect.response
```

## 🔍 Monitoring & Health

### Health Checks

**Location**: `rabbitmq_service.py` - `_health_check_loop()`

- Monitors consumer heartbeats
- Detects stale consumers (>10 minutes)
- Triggers automatic recovery

### Connection Management

**Location**: `rabbitmq_service.py` - `_ensure_connection()`

- Automatic reconnection on connection loss
- Connection state validation
- Graceful degradation and recovery

### Consumer Recovery

**Location**: `rabbitmq_service.py` - `_recover_consumer()`

- Automatic consumer recovery on failure
- Queue re-declaration and re-binding
- Consumer state restoration

## 📊 System Flow Diagrams

### Startup Sequence Diagram

```
Application Startup
        ↓
RabbitMQ Manager Initialize
        ↓
RabbitMQ Service Connect
        ↓
Declare Exchanges (ex.scheduler, ex.message, etc.)
        ↓
Setup Scheduler Listener
        ↓
Declare Queue (whatsapp-chatbot.scheduler.events)
        ↓
Bind Queue to Exchange (scheduler.collect.usage)
        ↓
Register Event Handlers
        ↓
Start Consuming Messages
        ↓
Start Health Check & Keepalive Tasks
        ↓
System Ready
```

### Message Publishing Flow

```
Application Code
        ↓
rabbitmq_manager.publish_event()
        ↓
rabbitmq_service.publish_message()
        ↓
Ensure Connection & Exchange
        ↓
Serialize Payload to JSON
        ↓
Create AMQP Message
        ↓
Publish to Exchange
        ↓
RabbitMQ Routes to Bound Queues
```

### Message Listening Flow

```
RabbitMQ Delivers Message
        ↓
rabbitmq_service.message_handler()
        ↓
Parse JSON Payload
        ↓
Route by Routing Key
        ↓
Call Specific Event Handler
        ↓
Process Business Logic
        ↓
Acknowledge Message
        ↓
Update Consumer Heartbeat
```

## 🎯 Key Integration Points

### FastAPI Integration (`app/main.py`)
- **Startup**: Initialize RabbitMQ services
- **Shutdown**: Graceful cleanup of connections
- **Health Endpoints**: RabbitMQ status monitoring

### Database Integration
- **Conversation Processing**: Read/write conversation state
- **Usage Collection**: Query conversation statistics
- **Credit Tracking**: Record token usage

### Redis Integration
- **State Management**: Store conversation state
- **Session Handling**: Manage user sessions

### Elasticsearch Integration
- **Knowledge Search**: Query knowledge base
- **Question Selection**: AI-powered question selection

This architecture provides a robust, scalable RabbitMQ system with automatic recovery, comprehensive monitoring, and clear separation of concerns.
