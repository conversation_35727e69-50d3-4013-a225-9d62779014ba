#!/usr/bin/env python3
"""
Setup script for chatbot changes implementation
"""

import os
import sys
import subprocess

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step_num, title):
    """Print a formatted step"""
    print(f"\n📋 Step {step_num}: {title}")
    print("-" * 40)

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔧 {description}")
    print(f"   Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Success!")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"   ❌ Failed: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (compatible)")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (requires 3.8+)")
        return False

def main():
    """Main setup function"""
    print_header("Chatbot Changes Setup")
    
    print("This script will help you set up the new chatbot features:")
    print("• Entity-based triggers")
    print("• Enhanced welcome messages")
    print("• Credit-based billing")
    print("• Streamlined API")
    
    # Step 1: Check Python version
    print_step(1, "Check Python Version")
    if not check_python_version():
        print("Please upgrade to Python 3.8 or higher")
        return False
    
    # Step 2: Check and install dependencies
    print_step(2, "Check Dependencies")
    if os.path.exists("check_and_install_dependencies.py"):
        success = run_command("python3 check_and_install_dependencies.py", 
                            "Checking and installing dependencies")
        if not success:
            print("⚠️  Some dependencies may be missing. Continuing anyway...")
    else:
        print("Installing basic dependencies...")
        packages = ["sqlalchemy", "psycopg2-binary", "fastapi", "uvicorn", "requests"]
        for package in packages:
            run_command(f"pip install {package}", f"Installing {package}")
    
    # Step 3: Database migration
    print_step(3, "Database Migration")
    
    # Check if we have database configuration
    db_configured = any([
        os.getenv("DATABASE_URL"),
        os.getenv("DB_HOST"),
        all([os.getenv(var) for var in ["DB_HOST", "DB_USER", "DB_NAME"]])
    ])
    
    if not db_configured:
        print("⚠️  Database not configured. Please set environment variables:")
        print("   Option 1: Set DATABASE_URL")
        print("   Option 2: Set DB_HOST, DB_USER, DB_PASSWORD, DB_NAME")
        print("\nSkipping database migration for now.")
    else:
        # Try the simple migration first
        if os.path.exists("simple_migration.py"):
            success = run_command("python3 simple_migration.py", 
                                "Running database migration")
            if not success:
                print("Migration failed. You can run it manually later.")
        else:
            print("Migration script not found. Please run manually:")
            print("python3 migrations/add_trigger_and_credit_usage.py")
    
    # Step 4: Test the changes
    print_step(4, "Test Implementation")
    
    if os.path.exists("test_comprehensive_chatbot_changes.py"):
        print("Test script available. To run tests:")
        print("1. Start the API server: uvicorn app.main:app --reload")
        print("2. Run tests: python3 test_comprehensive_chatbot_changes.py")
    else:
        print("Test script not found.")
    
    # Step 5: Final instructions
    print_step(5, "Next Steps")
    
    print("🎉 Setup completed! Here's what to do next:")
    print()
    print("1. 🗄️  Database Migration (if not done):")
    print("   python3 simple_migration.py")
    print()
    print("2. 🚀 Start the API server:")
    print("   uvicorn app.main:app --reload")
    print()
    print("3. 🧪 Run tests:")
    print("   python3 test_comprehensive_chatbot_changes.py")
    print()
    print("4. 📖 Check documentation:")
    print("   - README.md (updated with new features)")
    print("   - IMPLEMENTATION_SUMMARY_AND_NEXT_ACTIONS.md")
    print()
    print("5. 🌐 Access API documentation:")
    print("   http://localhost:8000/docs")
    
    print("\n" + "=" * 60)
    print("✅ Setup script completed!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed: {e}")
        sys.exit(1)
