#!/usr/bin/env python3
"""
Test script for chatbot activation API and event publishing
"""

import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi.testclient import TestClient
from app.main import app

# Test client
client = TestClient(app)

# Mock JWT token for testing
MOCK_JWT_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzZWxsIiwiZGF0YSI6eyJ1c2VySWQiOiI3IiwidGVuYW50SWQiOjd9fQ.mock_signature"

def mock_auth_context():
    """Mock authentication context"""
    mock_context = MagicMock()
    mock_context.user_id = "7"
    mock_context.tenant_id = 7
    return mock_context

def test_chatbot_activate_api():
    """Test the chatbot activation API endpoint"""

    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()

        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service

            # Mock successful activation response
            mock_service.update_chatbot_status = AsyncMock(return_value={
                "id": "chatbot-123",
                "name": "Sales Account Chatbot",
                "status": "ACTIVE",
                "previousStatus": "DRAFT",
                "connectedAccount": {
                    "displayName": "Sales Team",
                    "accountId": 123
                },
                "trigger": "NEW_ENTITY",
                "updated_at": "2025-01-22T12:00:00Z",
                "updated_by": "7"
            })

            with patch('fastapi.Request') as mock_request:
                mock_request_instance = MagicMock()
                mock_request_instance.state.auth_context = mock_auth_context()
                mock_request_instance.headers.get.return_value = MOCK_JWT_TOKEN

                response = client.post(
                    "/v1/chatbot/chatbot-123/activate",
                    headers={"Authorization": MOCK_JWT_TOKEN}
                )

            # Verify response
            assert response.status_code == 200
            response_data = response.json()

            # Check response structure
            assert response_data["id"] == "chatbot-123"
            assert response_data["status"] == "ACTIVE"
            assert response_data["previousStatus"] == "DRAFT"
            assert "connectedAccount" in response_data
            assert "updated_at" in response_data

            # Verify service was called correctly
            mock_service.update_chatbot_status.assert_called_once_with(
                "chatbot-123", "ACTIVE", 7, "7", MOCK_JWT_TOKEN
            )

            print("✓ Chatbot activation API test passed")

def test_chatbot_deactivate_api():
    """Test the chatbot deactivation API endpoint"""

    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()

        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service

            # Mock successful deactivation response
            mock_service.update_chatbot_status = AsyncMock(return_value={
                "id": "chatbot-123",
                "name": "Sales Account Chatbot",
                "status": "INACTIVE",
                "previousStatus": "ACTIVE",
                "connectedAccount": {
                    "displayName": "Sales Team",
                    "accountId": 123
                },
                "trigger": "NEW_ENTITY",
                "updated_at": "2025-01-22T12:00:00Z",
                "updated_by": "7"
            })

            with patch('fastapi.Request') as mock_request:
                mock_request_instance = MagicMock()
                mock_request_instance.state.auth_context = mock_auth_context()
                mock_request_instance.headers.get.return_value = MOCK_JWT_TOKEN

                response = client.post(
                    "/v1/chatbot/chatbot-123/deactivate",
                    headers={"Authorization": MOCK_JWT_TOKEN}
                )

            # Verify response
            assert response.status_code == 200
            response_data = response.json()

            # Check response structure
            assert response_data["id"] == "chatbot-123"
            assert response_data["status"] == "INACTIVE"
            assert response_data["previousStatus"] == "ACTIVE"
            assert "connectedAccount" in response_data
            assert "updated_at" in response_data

            # Verify service was called correctly
            mock_service.update_chatbot_status.assert_called_once_with(
                "chatbot-123", "INACTIVE", 7, "7", MOCK_JWT_TOKEN
            )

            print("✓ Chatbot deactivation API test passed")

def test_chatbot_status_update_api():
    """Test the generic chatbot status update API endpoint"""

    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()

        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service

            # Mock successful status update response
            mock_service.update_chatbot_status = AsyncMock(return_value={
                "id": "chatbot-123",
                "name": "Sales Account Chatbot",
                "status": "DRAFT",
                "previousStatus": "ACTIVE",
                "connectedAccount": {
                    "displayName": "Sales Team",
                    "accountId": 123
                },
                "trigger": "NEW_ENTITY",
                "updated_at": "2025-01-22T12:00:00Z",
                "updated_by": "7"
            })

            # Test status update request to DRAFT
            request_data = {
                "status": "DRAFT"
            }

            with patch('fastapi.Request') as mock_request:
                mock_request_instance = MagicMock()
                mock_request_instance.state.auth_context = mock_auth_context()
                mock_request_instance.headers.get.return_value = MOCK_JWT_TOKEN

                response = client.patch(
                    "/v1/chatbot/chatbot-123/status",
                    json=request_data,
                    headers={"Authorization": MOCK_JWT_TOKEN}
                )

            # Verify response
            assert response.status_code == 200
            response_data = response.json()

            # Check response structure
            assert response_data["id"] == "chatbot-123"
            assert response_data["status"] == "DRAFT"
            assert response_data["previousStatus"] == "ACTIVE"
            assert "connectedAccount" in response_data
            assert "updated_at" in response_data

            # Verify service was called correctly
            mock_service.update_chatbot_status.assert_called_once_with(
                "chatbot-123", "DRAFT", 7, "7", MOCK_JWT_TOKEN
            )

            print("✓ Chatbot status update API test passed")

def test_chatbot_status_validation():
    """Test chatbot status validation"""
    
    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()
        
        # Test invalid status
        request_data = {
            "status": "INVALID_STATUS"
        }
        
        with patch('fastapi.Request') as mock_request:
            mock_request_instance = MagicMock()
            mock_request_instance.state.auth_context = mock_auth_context()
            mock_request_instance.headers.get.return_value = MOCK_JWT_TOKEN
            
            response = client.patch(
                "/v1/chatbot/chatbot-123/status",
                json=request_data,
                headers={"Authorization": MOCK_JWT_TOKEN}
            )
        
        # Should return 400 for invalid status
        assert response.status_code == 400
        assert "Status must be one of: DRAFT, ACTIVE, INACTIVE" in response.json()["detail"]
        
        print("✓ Chatbot status validation test passed")

def test_event_publishing():
    """Test event publishing functionality"""
    
    from app.services.chatbot_event_publisher import ChatbotEventPublisher
    
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.is_connected.return_value = True
        mock_rabbitmq.publish_message = AsyncMock()
        
        # Test status updated event
        publisher = ChatbotEventPublisher()
        
        # Test publishing status updated event
        import asyncio
        
        async def test_status_event():
            result = await publisher.publish_status_updated_event(
                status="ACTIVE",
                connected_account_id=123,
                connected_account_name="Sales Team",
                chatbot_id="chatbot-123",
                tenant_id=7
            )
            return result
        
        result = asyncio.run(test_status_event())
        
        # Verify event was published
        assert result == True
        mock_rabbitmq.publish_message.assert_called_once()
        
        # Check the call arguments
        call_args = mock_rabbitmq.publish_message.call_args
        assert call_args[1]["exchange"] == "ex.whatsappChatbot"
        assert call_args[1]["routing_key"] == "chatbot.status.updated"
        
        # Parse the message to verify payload
        message_body = call_args[1]["message"]
        payload = json.loads(message_body)
        
        assert payload["status"] == "ACTIVE"
        assert payload["connectedAccount"]["id"] == 123
        assert payload["connectedAccount"]["name"] == "Sales Team"
        
        print("✓ Event publishing test passed")

def test_chatbot_creation_with_event():
    """Test chatbot creation publishes DRAFT status event"""
    
    with patch('app.routers.chatbot.get_auth_context') as mock_get_auth:
        mock_get_auth.return_value = mock_auth_context()
        
        with patch('app.routers.chatbot.ChatbotService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value = mock_service
            
            # Mock successful chatbot creation
            mock_service.create_chatbot = AsyncMock(return_value={
                "id": "chatbot-123",
                "tenant_id": 7,
                "name": "Sales Account Chatbot",
                "type": "AI",
                "status": "DRAFT",
                "connectedAccount": {
                    "displayName": "Sales Team",
                    "accountId": 123
                },
                "trigger": "NEW_ENTITY",
                "created_at": "2025-01-22T12:00:00Z"
            })
            
            # Test chatbot creation
            request_data = {
                "name": "Sales Account Chatbot",
                "type": "AI",
                "description": "Chatbot for sales account",
                "connectedAccount": {
                    "displayName": "Sales Team",
                    "accountId": 123
                },
                "trigger": "NEW_ENTITY"
            }
            
            with patch('fastapi.Request') as mock_request:
                mock_request_instance = MagicMock()
                mock_request_instance.state.auth_context = mock_auth_context()
                mock_request_instance.headers.get.return_value = MOCK_JWT_TOKEN
                
                response = client.post(
                    "/v1/chatbot/",
                    json=request_data,
                    headers={"Authorization": MOCK_JWT_TOKEN}
                )
            
            # Verify response
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "DRAFT"
            
            # Verify service was called
            mock_service.create_chatbot.assert_called_once()
            
            print("✓ Chatbot creation with event test passed")

def test_exchange_configuration():
    """Test exchange configuration"""
    
    from app.services.chatbot_event_publisher import ChatbotEventPublisher
    
    publisher = ChatbotEventPublisher()
    
    # Test exchange name
    assert publisher.get_exchange_name() == "ex.whatsappChatbot"
    
    # Test routing keys
    assert publisher.get_status_routing_key() == "chatbot.status.updated"
    assert publisher.get_created_routing_key() == "chatbot.created"
    
    print("✓ Exchange configuration test passed")

def run_all_tests():
    """Run all chatbot activation tests"""
    print("Running chatbot activation and event publishing tests...")
    
    try:
        test_chatbot_activate_api()
        test_chatbot_deactivate_api()
        test_chatbot_status_update_api()
        test_chatbot_status_validation()
        test_event_publishing()
        test_chatbot_creation_with_event()
        test_exchange_configuration()

        print("\n✓ All chatbot activation tests passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
