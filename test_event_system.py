#!/usr/bin/env python3
"""
Test script for the event-driven conversation system
This script tests the system without requiring a full server setup
"""

import asyncio
import sys
import os
import json
from unittest.mock import MagicMock, AsyncMock, patch

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

async def test_conversation_event_publisher():
    """Test the conversation event publisher"""
    print("\n--- Testing Conversation Event Publisher ---")
    
    try:
        from app.services.conversation_event_publisher import conversation_event_publisher
        
        # Mock RabbitMQ service
        with patch('app.services.conversation_event_publisher.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.connection = MagicMock()
            mock_rabbitmq.connection.is_closed = False
            mock_rabbitmq.channel = MagicMock()
            mock_rabbitmq.channel.is_closed = False
            mock_rabbitmq.publish_message = AsyncMock()
            
            # Test publishing a conversation response
            result = await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id="test-conv-123",
                message="What is your name?",
                completed=False,
                charge=1
            )
            
            assert result is True, "Publisher should return True on success"
            mock_rabbitmq.publish_message.assert_called_once()
            
            # Check the call arguments
            call_args = mock_rabbitmq.publish_message.call_args
            assert call_args[1]['exchange'] == 'ex.whatsappChatbot'
            assert call_args[1]['routing_key'] == 'chatbot.conversation.response'
            
            # Parse and check the message
            message_body = json.loads(call_args[1]['message'])
            assert message_body['chatbotConversationId'] == 'test-conv-123'
            assert message_body['message'] == 'What is your name?'
            assert message_body['completed'] is False
            assert message_body['charge'] == 1
            
            print("✓ Conversation event publisher working correctly")
            return True
            
    except Exception as e:
        print(f"✗ Conversation event publisher test failed: {e}")
        return False

async def test_message_event_listener():
    """Test the message event listener"""
    print("\n--- Testing Message Event Listener ---")
    
    try:
        from app.services.message_event_listener import message_event_listener
        
        # Mock dependencies
        with patch.object(message_event_listener, '_process_conversation_message') as mock_process:
            mock_process.return_value = None
            
            # Create test payload
            payload = {
                "message": "John Doe",
                "chatbotConversationId": "test-conv-123",
                "completed": False
            }
            
            # Create mock message
            mock_message = MagicMock()
            mock_message.routing_key = "message.chatbot.user.response"
            
            # Test handling the event
            await message_event_listener.handle_user_message_event(payload, mock_message)
            
            # Verify processing was called
            mock_process.assert_called_once_with("test-conv-123", "John Doe")
            
            print("✓ Message event listener working correctly")
            return True
            
    except Exception as e:
        print(f"✗ Message event listener test failed: {e}")
        return False

def test_charge_calculator():
    """Test the charge calculator"""
    print("\n--- Testing Charge Calculator ---")
    
    try:
        from app.services.charge_calculator import charge_calculator
        
        # Test predefined question charge
        charge1 = charge_calculator.calculate_question_charge(
            {"question": "What is your name?", "is_predefined": True},
            is_predefined=True,
            is_llm_generated=False
        )
        assert charge1 == 1, f"Expected charge 1 for predefined question, got {charge1}"
        
        # Test custom question charge
        charge2 = charge_calculator.calculate_question_charge(
            {"question": "Custom question", "is_llm_generated": True},
            is_predefined=False,
            is_llm_generated=True
        )
        assert charge2 == 2, f"Expected charge 2 for custom question, got {charge2}"
        
        # Test completion charge
        charge3 = charge_calculator.calculate_completion_charge()
        assert charge3 == 0, f"Expected charge 0 for completion, got {charge3}"
        
        # Test conversation charge calculation
        conversation_state = {
            "questions": [
                {"question": "What is your name?", "is_predefined": True},
                {"question": "Custom question", "is_llm_generated": True}
            ],
            "original_questions": [
                {"question": "What is your name?"}
            ]
        }
        
        charge4 = charge_calculator.calculate_conversation_charge(conversation_state, 0)
        assert charge4 == 1, f"Expected charge 1 for first question, got {charge4}"
        
        charge5 = charge_calculator.calculate_conversation_charge(conversation_state, 1)
        assert charge5 == 2, f"Expected charge 2 for second question, got {charge5}"
        
        # Test charge summary
        summary = charge_calculator.get_charge_summary(conversation_state)
        # Debug: print actual values
        print(f"Debug: summary = {summary}")

        # The actual calculation: 1 predefined + 1 custom = 3 total
        # (The test was expecting 2 predefined + 1 custom = 4, but we only have 2 questions total)
        assert summary["total_charge"] == 3, f"Expected total charge 3, got {summary['total_charge']}"
        assert summary["predefined_questions"] == 1, f"Expected 1 predefined question, got {summary['predefined_questions']}"
        assert summary["custom_questions"] == 1, f"Expected 1 custom question, got {summary['custom_questions']}"
        
        print("✓ Charge calculator working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Charge calculator test failed: {e}")
        return False

def test_conversation_state_utils():
    """Test conversation state utilities"""
    print("\n--- Testing Conversation State Utils ---")
    
    try:
        from app.utils.conversation_state_utils import (
            migrate_conversation_state, 
            validate_conversation_state,
            clean_conversation_state
        )
        
        # Test state migration
        old_state = {
            "questions": [
                {"id": 1, "question": "What is your name?"},
                {"id": 2, "question": "What is your email?"}
            ],
            "current_question_index": 0,
            "chatbot_id": "test-bot",
            "tenant_id": "test-tenant",
            "history": []
        }
        
        new_state = migrate_conversation_state(old_state)
        assert "remaining_questions" in new_state, "State should have remaining_questions"
        assert "asked_questions" in new_state, "State should have asked_questions"
        assert len(new_state["remaining_questions"]) == 2, "Should have 2 remaining questions"
        assert len(new_state["asked_questions"]) == 0, "Should have 0 asked questions initially"
        
        # Test state validation
        valid_state = {
            "chatbot_id": "test-bot",
            "tenant_id": "test-tenant",
            "history": []
        }
        assert validate_conversation_state(valid_state) is True, "Valid state should pass validation"
        
        invalid_state = {
            "chatbot_id": "test-bot"
            # Missing required fields
        }
        assert validate_conversation_state(invalid_state) is False, "Invalid state should fail validation"
        
        # Test state cleaning
        dirty_state = {
            "chatbot_id": "test-bot",
            "tenant_id": None,
            "history": [],
            "some_field": "value"
        }
        clean_state = clean_conversation_state(dirty_state)
        assert "tenant_id" not in clean_state or clean_state["tenant_id"] is not None, "None values should be cleaned"
        assert "history" in clean_state, "Required fields should be preserved"
        
        print("✓ Conversation state utils working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Conversation state utils test failed: {e}")
        return False

async def test_rabbitmq_infrastructure():
    """Test RabbitMQ infrastructure setup"""
    print("\n--- Testing RabbitMQ Infrastructure ---")
    
    try:
        from app.services.rabbitmq_service import rabbitmq_service
        
        # Mock RabbitMQ connection
        with patch.object(rabbitmq_service, 'declare_exchange') as mock_declare_exchange, \
             patch.object(rabbitmq_service, 'declare_queue') as mock_declare_queue, \
             patch.object(rabbitmq_service, 'bind_queue_to_exchange') as mock_bind:
            
            mock_declare_exchange.return_value = MagicMock()
            mock_declare_queue.return_value = MagicMock()
            mock_bind.return_value = None
            
            # Test WhatsApp chatbot publisher setup
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()
            mock_declare_exchange.assert_called_with("ex.whatsappChatbot", "topic")
            
            # Reset mocks
            mock_declare_exchange.reset_mock()
            mock_declare_queue.reset_mock()
            mock_bind.reset_mock()
            
            # Test message listener setup
            await rabbitmq_service.setup_message_listener()
            mock_declare_exchange.assert_called_with("ex.message", "topic")
            mock_declare_queue.assert_called_with("q.message.chatbot.user.response.chatbot", durable=True)
            mock_bind.assert_called_with(
                "q.message.chatbot.user.response.chatbot",
                "ex.message",
                "message.chatbot.user.response"
            )
            
            print("✓ RabbitMQ infrastructure setup working correctly")
            return True
            
    except Exception as e:
        print(f"✗ RabbitMQ infrastructure test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("=" * 60)
    print("Event-Driven Conversation System Test Suite")
    print("=" * 60)
    
    tests = [
        test_charge_calculator,
        test_conversation_state_utils,
        test_conversation_event_publisher,
        test_message_event_listener,
        test_rabbitmq_infrastructure
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()
            
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The event-driven conversation system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
