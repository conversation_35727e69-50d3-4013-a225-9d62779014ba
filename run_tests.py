#!/usr/bin/env python3
"""
Test runner script for local development and CI/CD
"""

import subprocess
import sys
import os
import argparse


def run_command(command, description=""):
    """Run a command and return success status"""
    if description:
        print(f"🔄 {description}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {command}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def install_dependencies():
    """Install test dependencies"""
    print("📦 Installing test dependencies...")
    commands = [
        "pip install pytest pytest-asyncio pytest-mock pytest-timeout pytest-cov",
    ]

    for cmd in commands:
        if not run_command(cmd, f"Running: {cmd}"):
            return False

    # Verify pytest-asyncio is working
    if not run_command("python -c 'import pytest_asyncio; print(\"✅ pytest-asyncio installed successfully\")'", "Verifying pytest-asyncio"):
        print("⚠️  pytest-asyncio verification failed, but continuing...")

    return True


def run_unit_tests():
    """Run unit tests only"""
    print("🧪 Running unit tests...")
    return run_command(
        "python3 -m pytest test/ -m 'unit or not integration' -v --tb=short --disable-warnings",
        "Running unit tests (including unmarked tests, excluding integration)"
    )


def run_integration_tests():
    """Run integration tests only"""
    print("🔗 Running integration tests...")
    return run_command(
        "python3 -m pytest test/ -m integration -v --tb=short --disable-warnings",
        "Running integration tests"
    )


def run_all_tests():
    """Run all tests (excluding integration tests that require running server)"""
    print("🧪 Running all tests...")
    return run_command(
        "python3 -m pytest test/ -m 'not integration' -v --tb=short --disable-warnings",
        "Running all tests (excluding integration tests)"
    )


def run_specific_test(test_file):
    """Run a specific test file"""
    print(f"🎯 Running specific test: {test_file}")
    return run_command(
        f"python -m pytest test/{test_file} -v --tb=short --disable-warnings",
        f"Running {test_file}"
    )


def run_with_coverage():
    """Run tests with coverage report"""
    print("📊 Running tests with coverage...")
    return run_command(
        "python -m pytest test/ --cov=app --cov-report=html --cov-report=term -v -m 'not integration'",
        "Running tests with coverage (excluding integration tests)"
    )


def run_verification_scripts():
    """Run verification scripts"""
    print("🔍 Running verification scripts...")
    verification_scripts = [
        "test/verify_tenant_id_changes.py"
    ]
    
    success = True
    for script in verification_scripts:
        if os.path.exists(script):
            if not run_command(f"python {script}", f"Running {script}"):
                success = False
        else:
            print(f"⚠️  Verification script not found: {script}")
    
    return success


def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description="Test runner for SD WhatsApp Chatbot")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--verify", action="store_true", help="Run verification scripts")
    parser.add_argument("--file", type=str, help="Run specific test file")
    parser.add_argument("--all", action="store_true", help="Run all tests (default)")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("SD WhatsApp Chatbot Test Runner")
    print("=" * 60)
    
    success = True
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_dependencies():
            success = False
    
    # Run specific test file
    if args.file:
        if not run_specific_test(args.file):
            success = False
    
    # Run unit tests
    elif args.unit:
        if not run_unit_tests():
            success = False
    
    # Run integration tests
    elif args.integration:
        if not run_integration_tests():
            success = False
    
    # Run tests with coverage
    elif args.coverage:
        if not run_with_coverage():
            success = False
    
    # Run verification scripts
    elif args.verify:
        if not run_verification_scripts():
            success = False
    
    # Run all tests (default)
    else:
        if not run_all_tests():
            success = False
    
    # Always run verification scripts unless running specific file
    if not args.file and not args.verify:
        print("\n" + "=" * 60)
        if not run_verification_scripts():
            success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
        print("\n📋 Available test commands:")
        print("  python run_tests.py --unit          # Run unit tests only")
        print("  python run_tests.py --integration   # Run integration tests only")
        print("  python run_tests.py --coverage      # Run with coverage report")
        print("  python run_tests.py --verify        # Run verification scripts")
        print("  python run_tests.py --file <name>   # Run specific test file")
        print("  python run_tests.py --all           # Run all tests")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
