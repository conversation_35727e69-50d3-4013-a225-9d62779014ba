#!/usr/bin/env python3
"""
Test script to verify that the import issues are resolved
"""

import sys
import os

def test_rabbitmq_imports():
    """Test RabbitMQ service imports"""
    print("Testing RabbitMQ imports...")
    
    try:
        import app.services.rabbitmq_service
        print("✓ rabbitmq_service imports successfully")
        
        import app.services.event_listeners
        print("✓ event_listeners imports successfully")
        
        return True
    except Exception as e:
        print(f"✗ RabbitMQ import failed: {str(e)}")
        return False


def test_model_imports():
    """Test model imports"""
    print("\nTesting model imports...")
    
    try:
        # Test individual model imports without database connection
        import app.models
        print("✓ models imports successfully")
        
        # Test that new fields exist in models
        from app.models import Chatbot
        if hasattr(Chatbot, 'welcome_message'):
            print("✓ welcome_message field exists in Chatbot model")
        else:
            print("✗ welcome_message field missing from Chatbot model")
            return False
            
        if hasattr(Chatbot, 'thank_you_message'):
            print("✓ thank_you_message field exists in Chatbot model")
        else:
            print("✗ thank_you_message field missing from Chatbot model")
            return False
            
        if hasattr(Chatbot, 'connected_account_id'):
            print("✓ connected_account_id field exists in Chatbot model")
        else:
            print("✗ connected_account_id field missing from Chatbot model")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Model import failed: {str(e)}")
        return False


def test_router_imports():
    """Test router imports"""
    print("\nTesting router imports...")
    
    try:
        # Import routers without triggering database connection
        import app.routers.chatbot
        print("✓ chatbot router imports successfully")
        
        return True
    except Exception as e:
        print(f"✗ Router import failed: {str(e)}")
        return False


def test_service_imports():
    """Test service imports"""
    print("\nTesting service imports...")
    
    try:
        import app.services.openai_service
        print("✓ openai_service imports successfully")
        
        import app.services.elasticsearch_service
        print("✓ elasticsearch_service imports successfully")
        
        return True
    except Exception as e:
        print(f"✗ Service import failed: {str(e)}")
        return False


def test_app_structure():
    """Test overall app structure without database"""
    print("\nTesting app structure...")
    
    try:
        # Test that we can import the main components
        import app.database
        print("✓ database module imports successfully")
        
        import app.monitoring
        print("✓ monitoring module imports successfully")
        
        import app.logging_config
        print("✓ logging_config module imports successfully")
        
        return True
    except Exception as e:
        print(f"✗ App structure import failed: {str(e)}")
        return False


def test_pydantic_models():
    """Test Pydantic model validation"""
    print("\nTesting Pydantic models...")
    
    try:
        from app.models import ChatbotCreate, ChatbotUpdate
        
        # Test ChatbotCreate with new fields
        chatbot_create = ChatbotCreate(
            name="Test Bot",
            type="AI",
            welcome_message="Welcome!",
            thank_you_message="Thank you!",
            connected_account_id="test_account"
        )
        print("✓ ChatbotCreate with new fields works")
        
        # Test ChatbotUpdate with new fields
        chatbot_update = ChatbotUpdate(
            welcome_message="Updated welcome",
            thank_you_message="Updated thank you",
            connected_account_id="updated_account"
        )
        print("✓ ChatbotUpdate with new fields works")
        
        return True
    except Exception as e:
        print(f"✗ Pydantic model test failed: {str(e)}")
        return False


def test_rabbitmq_api_compatibility():
    """Test RabbitMQ API compatibility"""
    print("\nTesting RabbitMQ API compatibility...")
    
    try:
        from app.services.rabbitmq_service import RabbitMQService
        
        # Test that service can be instantiated
        service = RabbitMQService()
        print("✓ RabbitMQService can be instantiated")
        
        # Test that required methods exist
        required_methods = [
            'connect', 'disconnect', 'declare_exchange', 'declare_queue',
            'start_consuming', 'get_consumer_status'
        ]
        
        for method in required_methods:
            if hasattr(service, method):
                print(f"✓ {method} method exists")
            else:
                print(f"✗ {method} method missing")
                return False
        
        return True
    except Exception as e:
        print(f"✗ RabbitMQ API compatibility test failed: {str(e)}")
        return False


def main():
    """Run all import tests"""
    print("=" * 70)
    print("Import Fix Verification Test Suite")
    print("=" * 70)
    
    tests = [
        test_rabbitmq_imports,
        test_model_imports,
        test_router_imports,
        test_service_imports,
        test_app_structure,
        test_pydantic_models,
        test_rabbitmq_api_compatibility
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with error: {str(e)}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All import tests passed! The application should start correctly.")
        print("\nFixed Issues:")
        print("  ✓ RabbitMQ AbstractConsumer import issue resolved")
        print("  ✓ Consumer management updated to use consumer tags")
        print("  ✓ All new chatbot fields are properly implemented")
        print("  ✓ Pydantic models work with new fields")
        print("  ✓ RabbitMQ service API is compatible")
        print("\nNext Steps:")
        print("  1. Ensure PostgreSQL is running")
        print("  2. Run database migration if needed")
        print("  3. Start the application with: uvicorn app.main:app --reload")
    else:
        print("✗ Some import tests failed. Please review the issues above.")
    
    print("=" * 70)


if __name__ == "__main__":
    main()
