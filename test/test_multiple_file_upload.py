#!/usr/bin/env python3
"""
Test script for multiple file upload functionality
"""

import os
import sys
import logging
import asyncio
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from io import BytesIO

# Set up basic logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_service_has_multiple_upload_method():
    """Test that ChatbotService has the multiple file upload method"""
    print("Testing ChatbotService multiple file upload method...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        
        # Check if the method exists
        if hasattr(service, 'upload_multiple_knowledgebase_files'):
            method = getattr(service, 'upload_multiple_knowledgebase_files')
            if callable(method):
                print("✓ upload_multiple_knowledgebase_files method found")
                
                # Check if it's async
                import inspect
                if inspect.iscoroutinefunction(method):
                    print("✓ Method is async")
                    return True
                else:
                    print("✗ Method is not async")
                    return False
            else:
                print("✗ upload_multiple_knowledgebase_files is not callable")
                return False
        else:
            print("✗ upload_multiple_knowledgebase_files method not found")
            return False
            
    except Exception as e:
        print(f"✗ Method test failed: {str(e)}")
        return False


def test_router_accepts_multiple_files():
    """Test that the router endpoint accepts multiple files"""
    print("\nTesting router accepts multiple files...")
    
    try:
        with open('app/routers/chatbot.py', 'r') as f:
            content = f.read()
        
        # Check for multiple file patterns
        multiple_file_patterns = [
            'files: List[UploadFile] = File(...)',
            'upload_multiple_knowledgebase_files',
            'chatbot_id, files, tenant_id, user_id'
        ]
        
        found_patterns = []
        for pattern in multiple_file_patterns:
            if pattern in content:
                found_patterns.append(pattern)
                print(f"  ✓ {pattern}")
            else:
                print(f"  ✗ {pattern} not found")
        
        if len(found_patterns) == len(multiple_file_patterns):
            print("✓ Router properly accepts multiple files")
            return True
        else:
            print(f"✗ Missing {len(multiple_file_patterns) - len(found_patterns)} patterns")
            return False
            
    except Exception as e:
        print(f"✗ Router test failed: {str(e)}")
        return False


def test_service_structure():
    """Test the structure of the multiple file upload service"""
    print("\nTesting service structure...")
    
    try:
        with open('app/services/chatbot_service.py', 'r') as f:
            content = f.read()
        
        required_components = [
            'async def upload_multiple_knowledgebase_files',
            'files: List[UploadFile]',
            'successful_uploads = []',
            'failed_uploads = []',
            'for file in files:',
            's3_service = S3Service()',
            'es_service = ElasticsearchService()',
            'await self._extract_pdf_text',
            'file.filename.lower().endswith(\'.pdf\')',
            'document_id = str(uuid.uuid4())',
            'es_service.index_document',
            's3_service.upload_file',
            'ChatbotKnowledgebase(',
            'db.commit()'
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        if len(found_components) >= len(required_components) - 2:  # Allow some flexibility
            print("✓ Service structure is complete")
            return True
        else:
            print(f"✗ Missing {len(required_components) - len(found_components)} required components")
            return False
            
    except Exception as e:
        print(f"✗ Structure test failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_multiple_file_upload_mock():
    """Test multiple file upload with mocked dependencies"""
    print("\nTesting multiple file upload (mocked)...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        # Create mock files
        mock_files = []
        for i in range(3):
            mock_file = MagicMock()
            mock_file.filename = f"test_file_{i}.pdf"
            mock_file.read = AsyncMock(return_value=b"fake pdf content")
            mock_files.append(mock_file)
        
        # Mock dependencies
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.S3Service') as mock_s3_service, \
             patch('app.services.chatbot_service.ElasticsearchService') as mock_es_service:
            
            # Mock database session
            mock_db = MagicMock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock chatbot exists
            mock_chatbot = MagicMock()
            mock_chatbot.status = "DRAFT"
            mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
            mock_db.query.return_value.filter.return_value.count.return_value = 1  # Has questions
            
            # Mock S3 service
            mock_s3_instance = MagicMock()
            mock_s3_instance.upload_file.return_value = "s3://bucket/key"
            mock_s3_service.return_value = mock_s3_instance
            
            # Mock ES service
            mock_es_instance = MagicMock()
            mock_es_instance.clean_text_for_embedding.return_value = "cleaned content"
            mock_es_instance.index_document.return_value = "test_index"
            mock_es_service.return_value = mock_es_instance
            
            # Test the service
            service = ChatbotService()
            
            # Mock the PDF extraction method
            with patch.object(service, '_extract_pdf_text', new_callable=AsyncMock) as mock_extract:
                mock_extract.return_value = "extracted pdf text"
                
                result = await service.upload_multiple_knowledgebase_files(
                    chatbot_id="test-chatbot-id",
                    files=mock_files,
                    tenant_id="test-tenant",
                    user_id="test-user"
                )
                
                # Verify results
                assert result["total_files"] == 3
                assert result["successful_uploads"] == 3
                assert result["failed_uploads"] == 0
                assert len(result["results"]["successful"]) == 3
                assert len(result["results"]["failed"]) == 0
                
                # Verify S3 upload was called for each file
                assert mock_s3_instance.upload_file.call_count == 3
                
                # Verify ES indexing was called for each file
                assert mock_es_instance.index_document.call_count == 3
                
                # Verify database operations
                assert mock_db.add.call_count == 6  # 3 documents + 3 associations
                mock_db.commit.assert_called_once()
                
                print("✓ Multiple file upload works correctly")
                print(f"  - Processed {result['total_files']} files")
                print(f"  - {result['successful_uploads']} successful uploads")
                print(f"  - {result['failed_uploads']} failed uploads")
                
                return True
                
    except Exception as e:
        print(f"✗ Multiple file upload test failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_file_validation():
    """Test file validation in multiple upload"""
    print("\nTesting file validation...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        # Create mock files with different types
        mock_files = []
        
        # Valid PDF file
        pdf_file = MagicMock()
        pdf_file.filename = "valid.pdf"
        pdf_file.read = AsyncMock(return_value=b"fake pdf content")
        mock_files.append(pdf_file)
        
        # Invalid file type
        txt_file = MagicMock()
        txt_file.filename = "invalid.txt"
        txt_file.read = AsyncMock(return_value=b"text content")
        mock_files.append(txt_file)
        
        # Empty file
        empty_file = MagicMock()
        empty_file.filename = "empty.pdf"
        empty_file.read = AsyncMock(return_value=b"")
        mock_files.append(empty_file)
        
        # Mock dependencies
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.S3Service') as mock_s3_service, \
             patch('app.services.chatbot_service.ElasticsearchService') as mock_es_service:
            
            # Mock database session
            mock_db = MagicMock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock chatbot exists
            mock_chatbot = MagicMock()
            mock_chatbot.status = "DRAFT"
            mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
            mock_db.query.return_value.filter.return_value.count.return_value = 1
            
            # Mock services
            mock_s3_instance = MagicMock()
            mock_s3_instance.upload_file.return_value = "s3://bucket/key"
            mock_s3_service.return_value = mock_s3_instance
            
            mock_es_instance = MagicMock()
            mock_es_instance.clean_text_for_embedding.return_value = "cleaned content"
            mock_es_instance.index_document.return_value = "test_index"
            mock_es_service.return_value = mock_es_instance
            
            # Test the service
            service = ChatbotService()
            
            # Mock the PDF extraction method
            with patch.object(service, '_extract_pdf_text', new_callable=AsyncMock) as mock_extract:
                mock_extract.return_value = "extracted pdf text"
                
                result = await service.upload_multiple_knowledgebase_files(
                    chatbot_id="test-chatbot-id",
                    files=mock_files,
                    tenant_id="test-tenant",
                    user_id="test-user"
                )
                
                # Verify results
                assert result["total_files"] == 3
                assert result["successful_uploads"] == 1  # Only PDF should succeed
                assert result["failed_uploads"] == 2     # TXT and empty should fail
                
                # Check failure reasons
                failed_results = result["results"]["failed"]
                assert len(failed_results) == 2
                
                # Find the specific failures
                txt_failure = next((f for f in failed_results if f["filename"] == "invalid.txt"), None)
                empty_failure = next((f for f in failed_results if f["filename"] == "empty.pdf"), None)
                
                assert txt_failure is not None
                assert "Only PDF files are supported" in txt_failure["error"]
                
                assert empty_failure is not None
                assert "File is empty" in empty_failure["error"]
                
                print("✓ File validation works correctly")
                print(f"  - Valid files: {result['successful_uploads']}")
                print(f"  - Invalid files: {result['failed_uploads']}")
                print(f"  - Validation errors properly caught")
                
                return True
                
    except Exception as e:
        print(f"✗ File validation test failed: {str(e)}")
        return False


def test_pdf_extraction_method():
    """Test the PDF text extraction helper method"""
    print("\nTesting PDF text extraction method...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        
        # Check if the method exists
        if hasattr(service, '_extract_pdf_text'):
            method = getattr(service, '_extract_pdf_text')
            if callable(method):
                print("✓ _extract_pdf_text method found")
                
                # Check if it's async
                import inspect
                if inspect.iscoroutinefunction(method):
                    print("✓ Method is async")
                    
                    # Check method signature
                    sig = inspect.signature(method)
                    params = list(sig.parameters.keys())
                    
                    expected_params = ['self', 'file_data', 'filename']
                    if all(param in params for param in expected_params):
                        print("✓ Method has correct parameters")
                        return True
                    else:
                        print(f"✗ Method parameters incorrect. Expected: {expected_params}, Got: {params}")
                        return False
                else:
                    print("✗ Method is not async")
                    return False
            else:
                print("✗ _extract_pdf_text is not callable")
                return False
        else:
            print("✗ _extract_pdf_text method not found")
            return False
            
    except Exception as e:
        print(f"✗ PDF extraction method test failed: {str(e)}")
        return False


def test_imports_and_dependencies():
    """Test that all required imports are present"""
    print("\nTesting imports and dependencies...")
    
    try:
        with open('app/services/chatbot_service.py', 'r') as f:
            content = f.read()
        
        required_imports = [
            'from fastapi import HTTPException, UploadFile',
            'from io import BytesIO',
            'import PyPDF2',
            'from app.services.elasticsearch_service import ElasticsearchService',
            'from app.services.s3_service import S3Service'
        ]
        
        found_imports = []
        for imp in required_imports:
            if imp in content:
                found_imports.append(imp)
                print(f"  ✓ {imp}")
            else:
                print(f"  ✗ {imp} not found")
        
        if len(found_imports) == len(required_imports):
            print("✓ All required imports are present")
            return True
        else:
            print(f"✗ Missing {len(required_imports) - len(found_imports)} required imports")
            return False
            
    except Exception as e:
        print(f"✗ Imports test failed: {str(e)}")
        return False


async def run_async_tests():
    """Run async tests"""
    async_tests = [
        test_multiple_file_upload_mock,
        test_file_validation
    ]
    
    results = []
    for test in async_tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Async test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    return results


def main():
    """Run all multiple file upload tests"""
    print("=" * 70)
    print("Multiple File Upload Test Suite")
    print("=" * 70)
    
    # Sync tests
    sync_tests = [
        test_service_has_multiple_upload_method,
        test_router_accepts_multiple_files,
        test_service_structure,
        test_pdf_extraction_method,
        test_imports_and_dependencies
    ]
    
    sync_results = []
    for test in sync_tests:
        try:
            result = test()
            sync_results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {str(e)}")
            sync_results.append(False)
    
    # Run async tests
    async_results = asyncio.run(run_async_tests())
    
    # Combine results
    all_results = sync_results + async_results
    passed = sum(all_results)
    total = len(all_results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Multiple file upload is working correctly.")
        print("\nMultiple File Upload Features:")
        print("  • Accepts multiple PDF files in single request")
        print("  • Validates each file individually")
        print("  • Stores each file in S3 with unique keys")
        print("  • Indexes each file content in Elasticsearch")
        print("  • Creates database records for each file")
        print("  • Associates files with chatbot")
        print("  • Returns detailed success/failure results")
        print("  • Proper error handling per file")
        print("  • Updates chatbot status when appropriate")
    else:
        print("✗ Some tests failed. Please review the implementation.")
    
    print("=" * 70)


if __name__ == "__main__":
    main()
