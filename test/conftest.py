"""
Shared test fixtures and configuration for pytest
"""

import pytest
import sys
import os
import asyncio
from unittest.mock import Mock, patch, AsyncMock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure asyncio for pytest
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture
def mock_database():
    """Mock database session for unit tests"""
    mock_db = Mock()
    mock_db.query.return_value = Mock()
    mock_db.add.return_value = None
    mock_db.commit.return_value = None
    mock_db.rollback.return_value = None
    mock_db.close.return_value = None
    return mock_db


@pytest.fixture
def mock_elasticsearch():
    """Mock Elasticsearch client for unit tests"""
    mock_es = Mock()
    mock_es.indices.exists.return_value = True
    mock_es.indices.create.return_value = {"acknowledged": True}
    mock_es.indices.put_alias.return_value = {"acknowledged": True}
    mock_es.index.return_value = {"_id": "test_id", "result": "created"}
    mock_es.search.return_value = {
        "hits": {
            "total": {"value": 0},
            "hits": []
        }
    }
    return mock_es


@pytest.fixture
def mock_s3_service():
    """Mock S3 service for unit tests"""
    mock_s3 = Mock()
    mock_s3.upload_file.return_value = "test/s3/key.pdf"
    return mock_s3


@pytest.fixture
def mock_openai_service():
    """Mock OpenAI service for unit tests"""
    mock_openai = Mock()
    mock_openai.generate_embedding.return_value = [0.1] * 1536  # Mock embedding vector
    mock_openai.chat_completion.return_value = "Mock AI response"
    return mock_openai


@pytest.fixture
def sample_tenant_id():
    """Sample tenant ID for tests"""
    return 12345


@pytest.fixture
def sample_chatbot_data():
    """Sample chatbot data for tests"""
    return {
        "name": "Test Chatbot",
        "type": "AI",
        "description": "Test chatbot description",
        "status": "DRAFT",
        "welcome_message": "Welcome to test chatbot",
        "thank_you_message": "Thank you for using test chatbot",
        "entity_type": "LEAD",
        "connected_account_id": 123,
        "trigger": "NEW_ENTITY"
    }


@pytest.fixture
def sample_question_data():
    """Sample question data for tests"""
    return {
        "question": "What is your name?",
        "fieldId": 1,
        "displayName": "Name"
    }


@pytest.fixture
def sample_upload_file():
    """Mock upload file for tests"""
    from unittest.mock import AsyncMock
    
    mock_file = AsyncMock()
    mock_file.filename = "test_document.pdf"
    mock_file.size = 1024 * 1024  # 1MB
    mock_file.read.return_value = b"Mock PDF content"
    return mock_file


@pytest.fixture
def mock_auth_context():
    """Mock authentication context for tests"""
    mock_context = Mock()
    mock_context.tenant_id = 12345
    mock_context.user_id = "test_user_123"
    return mock_context


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables"""
    test_env_vars = {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_PORT': '5432',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_password',
        'ELASTICSEARCH_URL': 'http://localhost:9200',
        'OPENAI_API_KEY': 'test_openai_key',
        'S3_ENDPOINT_URL': 'https://test.s3.endpoint.com',
        'S3_ACCESS_KEY_ID': 'test_access_key',
        'S3_SECRET_ACCESS_KEY': 'test_secret_key',
        'S3_BUCKET_NAME': 'test-bucket',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test_user',
        'RABBITMQ_PASSWORD': 'test_password'
    }
    
    with patch.dict(os.environ, test_env_vars):
        yield


@pytest.fixture
def mock_file_content():
    """Mock file content for upload tests"""
    return b"Mock file content for testing file uploads and processing"


@pytest.fixture
def mock_pdf_content():
    """Mock PDF content for document processing tests"""
    return "Mock extracted text from PDF document for testing purposes"


# Pytest markers for test categorization
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names"""
    for item in items:
        # Add unit marker to unit tests
        if "unit" in item.nodeid:
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker to integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add slow marker to migration and comprehensive tests
        if any(keyword in item.nodeid for keyword in ["migration", "comprehensive", "api_integration"]):
            item.add_marker(pytest.mark.slow)
