#!/usr/bin/env python3
"""
Standalone test for conversation state migration functionality
"""

def migrate_conversation_state(state):
    """
    Migrate old conversation state format to new format for backward compatibility
    """
    # Check if state is already in new format
    if "remaining_questions" in state and "asked_questions" in state:
        return state
    
    # Migrate from old format
    if "questions" in state and "current_question_index" in state:
        current_index = state.get("current_question_index", 0)
        all_questions = state.get("questions", [])
        
        # Split questions into asked and remaining
        asked_questions = all_questions[:current_index + 1] if current_index < len(all_questions) else all_questions
        remaining_questions = all_questions[current_index + 1:] if current_index + 1 < len(all_questions) else []
        
        # Update state structure
        state["all_questions"] = all_questions
        state["asked_questions"] = asked_questions
        state["remaining_questions"] = remaining_questions
        
        # Remove old fields
        if "current_question_index" in state:
            del state["current_question_index"]
        if "questions" in state:
            del state["questions"]
    
    return state

def test_state_migration():
    """Test the conversation state migration functionality"""
    print("Testing conversation state migration...")
    
    # Old format state
    old_state = {
        "chatbot_id": "test-chatbot",
        "tenant_id": "test-tenant",
        "questions": [
            {"id": "1", "question": "What is your name?"},
            {"id": "2", "question": "What is your email?"},
            {"id": "3", "question": "What is your phone?"}
        ],
        "current_question_index": 1,
        "answers": [
            {"question_id": "1", "question": "What is your name?", "answer": "John"}
        ],
        "history": []
    }
    
    print("Old state structure:")
    print(f"  - questions: {len(old_state['questions'])}")
    print(f"  - current_question_index: {old_state['current_question_index']}")
    
    # Migrate state
    migrated_state = migrate_conversation_state(old_state.copy())
    
    print("Migrated state structure:")
    print(f"  - all_questions: {len(migrated_state.get('all_questions', []))}")
    print(f"  - asked_questions: {len(migrated_state.get('asked_questions', []))}")
    print(f"  - remaining_questions: {len(migrated_state.get('remaining_questions', []))}")
    print(f"  - current_question_index removed: {'current_question_index' not in migrated_state}")
    
    # Verify the migration logic
    expected_asked = 2  # index 1 means questions 0 and 1 have been asked
    expected_remaining = 1  # only question 2 remains
    
    actual_asked = len(migrated_state.get('asked_questions', []))
    actual_remaining = len(migrated_state.get('remaining_questions', []))
    
    print(f"  - Asked questions count correct: {actual_asked == expected_asked} ({actual_asked}/{expected_asked})")
    print(f"  - Remaining questions count correct: {actual_remaining == expected_remaining} ({actual_remaining}/{expected_remaining})")
    
    # Test with already migrated state
    already_migrated = migrate_conversation_state(migrated_state.copy())
    print(f"  - Re-migration preserves structure: {migrated_state == already_migrated}")

def test_new_state_format():
    """Test that new state format is handled correctly"""
    print("\nTesting new state format handling...")
    
    # New format state
    new_state = {
        "chatbot_id": "test-chatbot",
        "tenant_id": "test-tenant",
        "all_questions": [
            {"id": "1", "question": "What is your name?"},
            {"id": "2", "question": "What is your email?"},
            {"id": "3", "question": "What is your phone?"}
        ],
        "remaining_questions": [
            {"id": "2", "question": "What is your email?"},
            {"id": "3", "question": "What is your phone?"}
        ],
        "asked_questions": [
            {"id": "1", "question": "What is your name?"}
        ],
        "answers": [],
        "history": []
    }
    
    # Should not be modified
    result = migrate_conversation_state(new_state.copy())
    print(f"✓ New format preserved: {new_state == result}")

def test_edge_cases():
    """Test edge cases in migration"""
    print("\nTesting edge cases...")
    
    # Test with index at end
    state_at_end = {
        "questions": [{"id": "1", "question": "Q1"}, {"id": "2", "question": "Q2"}],
        "current_question_index": 1,
        "answers": []
    }
    
    migrated = migrate_conversation_state(state_at_end.copy())
    print(f"✓ End index handled: asked={len(migrated['asked_questions'])}, remaining={len(migrated['remaining_questions'])}")
    
    # Test with index beyond end
    state_beyond_end = {
        "questions": [{"id": "1", "question": "Q1"}],
        "current_question_index": 5,
        "answers": []
    }
    
    migrated = migrate_conversation_state(state_beyond_end.copy())
    print(f"✓ Beyond end index handled: asked={len(migrated['asked_questions'])}, remaining={len(migrated['remaining_questions'])}")
    
    # Test with empty questions
    empty_state = {
        "questions": [],
        "current_question_index": 0,
        "answers": []
    }
    
    migrated = migrate_conversation_state(empty_state.copy())
    print(f"✓ Empty questions handled: asked={len(migrated['asked_questions'])}, remaining={len(migrated['remaining_questions'])}")

if __name__ == "__main__":
    print("=" * 60)
    print("Conversation State Migration Test Suite")
    print("=" * 60)
    
    try:
        test_state_migration()
        test_new_state_format()
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("✓ All migration tests passed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
