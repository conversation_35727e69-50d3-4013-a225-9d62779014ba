#!/usr/bin/env python3
"""
Simple verification script for tenant_id type changes
"""

import os
import re

def check_models_file():
    """Check that models.py has been updated correctly"""
    print("Checking app/models.py...")
    
    try:
        with open('app/models.py', 'r') as f:
            content = f.read()
        
        # Check for BigInteger import
        if 'from sqlalchemy import Column, String, Integer, BigInteger' in content:
            print("✅ BigInteger imported in models.py")
        else:
            print("❌ BigInteger not imported in models.py")
            return False
        
        # Check for BigInteger usage in tenant_id columns
        bigint_patterns = [
            r'tenant_id = Column\(BigInteger',
            r'tenantId: int'  # For JWT model
        ]
        
        found_patterns = 0
        for pattern in bigint_patterns:
            if re.search(pattern, content):
                found_patterns += 1
        
        if found_patterns >= 2:
            print(f"✅ Found {found_patterns} BigInteger/int tenant_id declarations")
        else:
            print(f"❌ Only found {found_patterns} BigInteger/int tenant_id declarations")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking models.py: {str(e)}")
        return False


def check_elasticsearch_mappings():
    """Check Elasticsearch mapping changes"""
    print("\nChecking Elasticsearch mappings...")
    
    files_to_check = [
        'app/services/elasticsearch_index_manager.py',
        'app/services/elasticsearch_service.py'
    ]
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Check for 'long' type in mappings
            if '"tenant_id": {"type": "long"}' in content or '"tenantId": {"type": "long"}' in content:
                print(f"✅ {file_path} uses 'long' type for tenant_id")
            else:
                print(f"❌ {file_path} doesn't use 'long' type for tenant_id")
                return False
                
        except Exception as e:
            print(f"❌ Error checking {file_path}: {str(e)}")
            return False
    
    return True


def check_service_signatures():
    """Check service method signatures"""
    print("\nChecking service method signatures...")
    
    try:
        with open('app/services/chatbot_service.py', 'r') as f:
            content = f.read()
        
        # Look for method signatures with tenant_id: int
        int_tenant_patterns = [
            r'tenant_id: int',
            r'def.*tenant_id: int'
        ]
        
        found_int_patterns = 0
        for pattern in int_tenant_patterns:
            matches = re.findall(pattern, content)
            found_int_patterns += len(matches)
        
        if found_int_patterns >= 10:  # Should have many method signatures updated
            print(f"✅ Found {found_int_patterns} int tenant_id type annotations")
        else:
            print(f"❌ Only found {found_int_patterns} int tenant_id type annotations")
            return False
        
        # Check ElasticsearchService
        with open('app/services/elasticsearch_service.py', 'r') as f:
            es_content = f.read()
        
        if 'tenant_id: int' in es_content:
            print("✅ ElasticsearchService uses int tenant_id")
        else:
            print("❌ ElasticsearchService doesn't use int tenant_id")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking service signatures: {str(e)}")
        return False


def check_migration_file():
    """Check consolidated migration file"""
    print("\nChecking consolidated migration file...")

    # Check that old migration files are removed
    old_migration_files = [
        "alembic/versions/eaf05b0f246b_create_all_required_database_tables.py",
        "alembic/versions/2001b317c8db_add_tenant_index_mapping_table_for_.py",
        "alembic/versions/20250717143530456_a1b2c3d4e5f6_change_tenant_id_from_varchar_to_bigint.py"
    ]

    for old_file in old_migration_files:
        if os.path.exists(old_file):
            print(f"❌ Old migration file still exists: {old_file}")
            return False

    print("✅ Old migration files successfully removed")

    migration_file = "alembic/versions/20250717144500123_a1b2c3d4e5f6_create_all_database_tables_with_bigint_tenant_id.py"

    try:
        if os.path.exists(migration_file):
            print("✅ Consolidated migration file exists with timestamp prefix")

            with open(migration_file, 'r') as f:
                content = f.read()

            required_elements = [
                'sa.BigInteger()',
                'tenant_id',
                'chatbot_knowledgebases',
                'chatbot_questions',
                'chatbots',
                'documents',
                'tenant_index_mappings',
                'chatbot_conversations',
                'chatbot_credit_usage',
                'conversation_token_usage',
                'def upgrade()',
                'def downgrade()'
            ]

            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)

            if not missing_elements:
                print("✅ Consolidated migration file contains all required elements")

                # Count BigInteger usages for tenant_id
                bigint_count = content.count('sa.BigInteger()')
                if bigint_count >= 8:  # Should have BigInteger for all tenant_id columns
                    print(f"✅ Found {bigint_count} BigInteger tenant_id columns")
                else:
                    print(f"❌ Only found {bigint_count} BigInteger tenant_id columns, expected at least 8")
                    return False

                return True
            else:
                print(f"❌ Migration file missing: {missing_elements}")
                return False
        else:
            print("❌ Consolidated migration file not found")
            return False

    except Exception as e:
        print(f"❌ Error checking migration file: {str(e)}")
        return False


def check_alembic_config():
    """Check alembic.ini configuration"""
    print("\nChecking alembic.ini configuration...")
    
    try:
        with open('alembic.ini', 'r') as f:
            content = f.read()
        
        # Check for millisecond timestamp template
        if 'file_template = %%(year)d%%(month).2d%%(day).2d%%(hour).2d%%(minute).2d%%(second).2d%%(microsecond).3d' in content:
            print("✅ alembic.ini configured for millisecond timestamps")
            return True
        else:
            print("❌ alembic.ini not configured for millisecond timestamps")
            return False
            
    except Exception as e:
        print(f"❌ Error checking alembic.ini: {str(e)}")
        return False


def main():
    """Run all verification checks"""
    print("=" * 60)
    print("Tenant ID Type Change Verification")
    print("=" * 60)
    
    checks = [
        check_models_file(),
        check_elasticsearch_mappings(),
        check_service_signatures(),
        check_migration_file(),
        check_alembic_config(),
    ]
    
    passed = sum(checks)
    total = len(checks)
    
    print("=" * 60)
    print(f"Verification Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All verifications passed!")
        print("\n✅ Summary of Changes:")
        print("  • Database models updated: VARCHAR → BIGINT")
        print("  • Elasticsearch mappings updated: keyword → long")
        print("  • Service method signatures updated: str → int")
        print("  • JWT token model updated: str → int")
        print("  • Migration script created with millisecond timestamp")
        print("  • Alembic configured for millisecond timestamps")
        print("\n📋 Next Steps:")
        print("  1. Run the migration: alembic upgrade head")
        print("  2. Update existing Elasticsearch indices with new mappings")
        print("  3. Test with actual integer tenant IDs")
    else:
        print("❌ Some verifications failed. Please review the implementation.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
