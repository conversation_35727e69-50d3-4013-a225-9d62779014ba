#!/usr/bin/env python3
"""
Test script to verify tenant_id type changes from varchar/string to bigint/integer
"""

import sys
import os
from unittest.mock import Mock, patch

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_models():
    """Test that database models use BigInteger for tenant_id"""
    print("Testing database models...")
    
    try:
        from app.models import (
            Document, Chatbot, ChatbotQuestion, ChatbotKnowledgebase,
            TenantIndexMapping, ChatbotConversation, ConversationTokenUsage,
            ChatbotCreditUsage, TokenData
        )
        from sqlalchemy import BigInteger
        
        # Test Document model
        tenant_id_column = Document.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ Document.tenant_id uses BigInteger")
        else:
            print(f"❌ Document.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test Chatbot model
        tenant_id_column = Chatbot.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ Chatbot.tenant_id uses BigInteger")
        else:
            print(f"❌ Chatbot.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test ChatbotQuestion model
        tenant_id_column = ChatbotQuestion.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ ChatbotQuestion.tenant_id uses BigInteger")
        else:
            print(f"❌ ChatbotQuestion.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test ChatbotKnowledgebase model
        tenant_id_column = ChatbotKnowledgebase.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ ChatbotKnowledgebase.tenant_id uses BigInteger")
        else:
            print(f"❌ ChatbotKnowledgebase.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test TenantIndexMapping model
        tenant_id_column = TenantIndexMapping.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ TenantIndexMapping.tenant_id uses BigInteger")
        else:
            print(f"❌ TenantIndexMapping.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test ChatbotConversation model
        tenant_id_column = ChatbotConversation.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ ChatbotConversation.tenant_id uses BigInteger")
        else:
            print(f"❌ ChatbotConversation.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test ConversationTokenUsage model
        tenant_id_column = ConversationTokenUsage.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ ConversationTokenUsage.tenant_id uses BigInteger")
        else:
            print(f"❌ ConversationTokenUsage.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test ChatbotCreditUsage model
        tenant_id_column = ChatbotCreditUsage.__table__.columns['tenant_id']
        if isinstance(tenant_id_column.type, BigInteger):
            print("✅ ChatbotCreditUsage.tenant_id uses BigInteger")
        else:
            print(f"❌ ChatbotCreditUsage.tenant_id uses {type(tenant_id_column.type)}, expected BigInteger")
            return False
        
        # Test JWT TokenData model
        token_data = TokenData(
            accessToken="test",
            expiresIn=3600,
            expiry=1234567890,
            tokenType="Bearer",
            refreshToken="refresh",
            permissions=[],
            userId="user123",
            username="testuser",
            tenantId=12345,  # Should accept integer
            source={"type": "test", "id": "1", "name": "test"},
            meta={"rate-limit": 5, "pid": 1}
        )
        
        if isinstance(token_data.tenantId, int):
            print("✅ TokenData.tenantId accepts integer")
        else:
            print(f"❌ TokenData.tenantId is {type(token_data.tenantId)}, expected int")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing database models: {str(e)}")
        return False


def test_elasticsearch_mappings():
    """Test that Elasticsearch mappings use 'long' type for tenant_id"""
    print("\nTesting Elasticsearch mappings...")
    
    try:
        from app.services.elasticsearch_index_manager import ElasticsearchIndexManager
        
        # Create index manager instance
        index_manager = ElasticsearchIndexManager()
        
        # Check index mapping configuration
        mapping = index_manager.index_mapping
        properties = mapping["mappings"]["properties"]
        
        # Check tenant_id field
        if "tenant_id" in properties and properties["tenant_id"]["type"] == "long":
            print("✅ Elasticsearch tenant_id mapping uses 'long' type")
        else:
            print(f"❌ Elasticsearch tenant_id mapping: {properties.get('tenant_id', 'NOT FOUND')}")
            return False
        
        # Check tenantId field (camelCase)
        if "tenantId" in properties and properties["tenantId"]["type"] == "long":
            print("✅ Elasticsearch tenantId mapping uses 'long' type")
        else:
            print(f"❌ Elasticsearch tenantId mapping: {properties.get('tenantId', 'NOT FOUND')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Elasticsearch mappings: {str(e)}")
        return False


def test_service_method_signatures():
    """Test that service methods accept integer tenant_id"""
    print("\nTesting service method signatures...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        from app.services.elasticsearch_service import ElasticsearchService
        import inspect
        
        # Test ChatbotService methods
        chatbot_service = ChatbotService()
        
        # Check a few key methods
        methods_to_check = [
            'get_chatbot_count_by_tenant',
            'get_chatbot_by_id',
            'create_chatbot',
            'list_chatbots'
        ]
        
        for method_name in methods_to_check:
            method = getattr(chatbot_service, method_name)
            sig = inspect.signature(method)
            
            if 'tenant_id' in sig.parameters:
                param = sig.parameters['tenant_id']
                if param.annotation == int:
                    print(f"✅ ChatbotService.{method_name} accepts int tenant_id")
                else:
                    print(f"❌ ChatbotService.{method_name} tenant_id annotation: {param.annotation}")
                    return False
        
        # Test ElasticsearchService methods
        es_service = ElasticsearchService()
        
        # Check index_document method
        method = getattr(es_service, 'index_document')
        sig = inspect.signature(method)
        
        if 'tenant_id' in sig.parameters:
            param = sig.parameters['tenant_id']
            if param.annotation == int:
                print("✅ ElasticsearchService.index_document accepts int tenant_id")
            else:
                print(f"❌ ElasticsearchService.index_document tenant_id annotation: {param.annotation}")
                return False
        
        # Check semantic_search method
        method = getattr(es_service, 'semantic_search')
        sig = inspect.signature(method)
        
        if 'tenant_id' in sig.parameters:
            param = sig.parameters['tenant_id']
            if param.annotation == int:
                print("✅ ElasticsearchService.semantic_search accepts int tenant_id")
            else:
                print(f"❌ ElasticsearchService.semantic_search tenant_id annotation: {param.annotation}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing service method signatures: {str(e)}")
        return False


def test_migration_file():
    """Test that migration file exists and has correct structure"""
    print("\nTesting migration file...")
    
    try:
        migration_file = "alembic/versions/20250717143530456_a1b2c3d4e5f6_change_tenant_id_from_varchar_to_bigint.py"
        
        if os.path.exists(migration_file):
            print("✅ Migration file exists")
            
            with open(migration_file, 'r') as f:
                content = f.read()
                
            # Check for key migration elements
            if "tenant_id_new" in content and "BIGINT" in content:
                print("✅ Migration file contains BIGINT conversion logic")
            else:
                print("❌ Migration file missing BIGINT conversion logic")
                return False
                
            if "upgrade()" in content and "downgrade()" in content:
                print("✅ Migration file has upgrade and downgrade functions")
            else:
                print("❌ Migration file missing upgrade/downgrade functions")
                return False
                
            return True
        else:
            print("❌ Migration file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing migration file: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("=" * 60)
    print("Tenant ID Type Change Tests")
    print("=" * 60)
    
    tests = [
        test_database_models(),
        test_elasticsearch_mappings(),
        test_service_method_signatures(),
        test_migration_file(),
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Tenant ID type changes are implemented correctly.")
        print("\nChanges Summary:")
        print("  • Database models: VARCHAR → BIGINT")
        print("  • Elasticsearch mappings: keyword → long")
        print("  • Service methods: str → int")
        print("  • JWT token model: str → int")
        print("  • Migration script: Created with timestamp prefix")
    else:
        print("❌ Some tests failed. Please review the implementation.")
        sys.exit(1)


if __name__ == "__main__":
    main()
