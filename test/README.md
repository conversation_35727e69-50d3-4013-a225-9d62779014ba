# Test Suite Documentation

This directory contains all test files for the SD WhatsApp Chatbot application.

## Test Organization

### Test Categories

- **Unit Tests**: Tests that don't require external dependencies
- **Integration Tests**: Tests that require external services (DB, Elasticsearch, etc.)
- **API Tests**: Tests for API endpoints
- **Service Tests**: Tests for service layer functionality
- **Migration Tests**: Database migration validation tests
- **Validation Tests**: Input validation and error handling tests

### Test Files

#### Core Functionality Tests
- `test_chatbot_service.py` - Chatbot service layer tests
- `test_chatbot_crud_refactor.py` - CRUD operations tests
- `test_api_integration.py` - API endpoint integration tests

#### Feature-Specific Tests
- `test_chatbot_new_fields.py` - New chatbot fields functionality
- `test_multiple_file_upload.py` - File upload functionality
- `test_conversation_token_storage.py` - Token usage tracking
- `test_llm_question_selection.py` - LLM question selection logic

#### Infrastructure Tests
- `test_rabbitmq_integration.py` - RabbitMQ integration tests
- `test_rabbitmq_services.py` - RabbitMQ service tests
- `test_elasticsearch_*.py` - Elasticsearch functionality tests

#### Migration Tests
- `test_migration.py` - Database migration tests
- `test_migration_files.py` - Migration file validation
- `test_tenant_id_type_changes.py` - Tenant ID type change tests

#### Validation Tests
- `test_file_upload_validation.py` - File upload validation
- `test_entity_type_change.py` - Entity type validation
- `verify_tenant_id_changes.py` - Tenant ID changes verification

## Running Tests

### Using pytest (Recommended)

```bash
# Run all tests
pytest test/

# Run specific test categories
pytest test/ -m unit          # Unit tests only
pytest test/ -m integration   # Integration tests only
pytest test/ -m slow          # Slow tests only

# Run specific test file
pytest test/test_chatbot_service.py

# Run with coverage
pytest test/ --cov=app --cov-report=html

# Run with verbose output
pytest test/ -v --tb=short
```

### Using the Test Runner Script

```bash
# Run all tests
python run_tests.py

# Run specific categories
python run_tests.py --unit
python run_tests.py --integration
python run_tests.py --coverage

# Run specific test file
python run_tests.py --file test_chatbot_service.py

# Run verification scripts
python run_tests.py --verify
```

### CI/CD Integration

The Jenkins pipeline automatically runs:
```bash
pytest test/ -v --tb=short --disable-warnings
```

## Test Configuration

### pytest.ini
- Test discovery patterns
- Output formatting
- Test markers
- Timeout settings
- Warning filters

### conftest.py
- Shared test fixtures
- Mock objects for external dependencies
- Test environment setup
- Common test utilities

## Test Fixtures

### Available Fixtures
- `mock_database` - Mock database session
- `mock_elasticsearch` - Mock Elasticsearch client
- `mock_s3_service` - Mock S3 service
- `mock_openai_service` - Mock OpenAI service
- `sample_tenant_id` - Sample tenant ID (12345)
- `sample_chatbot_data` - Sample chatbot data
- `sample_question_data` - Sample question data
- `sample_upload_file` - Mock upload file
- `mock_auth_context` - Mock authentication context

### Environment Variables
Test fixtures automatically set up test environment variables for:
- Database connection
- Elasticsearch connection
- OpenAI API
- S3 configuration
- RabbitMQ connection

## Test Markers

Use pytest markers to categorize tests:

```python
import pytest

@pytest.mark.unit
def test_unit_functionality():
    """Unit test that doesn't require external dependencies"""
    pass

@pytest.mark.integration
def test_integration_functionality():
    """Integration test that requires external services"""
    pass

@pytest.mark.slow
def test_slow_functionality():
    """Test that takes a long time to run"""
    pass
```

## Writing New Tests

### Test File Naming
- Use `test_*.py` pattern
- Group related tests in the same file
- Use descriptive names

### Test Function Naming
- Use `test_*` pattern
- Use descriptive names that explain what is being tested
- Include the expected outcome

### Example Test Structure

```python
import pytest
from unittest.mock import Mock, patch

class TestChatbotService:
    """Test class for ChatbotService functionality"""
    
    def test_create_chatbot_success(self, mock_database, sample_chatbot_data, sample_tenant_id):
        """Test successful chatbot creation"""
        # Arrange
        from app.services.chatbot_service import ChatbotService
        service = ChatbotService()
        
        # Act
        result = service.create_chatbot(sample_chatbot_data, sample_tenant_id)
        
        # Assert
        assert result is not None
        assert result["status"] == "success"
    
    @pytest.mark.integration
    def test_create_chatbot_database_integration(self):
        """Test chatbot creation with real database"""
        # Integration test implementation
        pass
```

## Best Practices

1. **Isolation**: Each test should be independent
2. **Mocking**: Mock external dependencies for unit tests
3. **Descriptive Names**: Use clear, descriptive test names
4. **Arrange-Act-Assert**: Follow the AAA pattern
5. **Fixtures**: Use fixtures for common test data
6. **Markers**: Use markers to categorize tests
7. **Coverage**: Aim for high test coverage
8. **Performance**: Keep tests fast and focused

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the project root is in Python path
2. **Missing Dependencies**: Install test dependencies with `pip install -r requirements.txt`
3. **Environment Variables**: Check that test environment variables are set
4. **Database Connection**: Ensure test database is available for integration tests

### Debug Mode

Run tests with additional debugging:
```bash
pytest test/ -v -s --tb=long --capture=no
```

## Coverage Reports

Generate coverage reports:
```bash
pytest test/ --cov=app --cov-report=html --cov-report=term
```

View HTML coverage report:
```bash
open htmlcov/index.html
```
