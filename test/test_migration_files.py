#!/usr/bin/env python3
"""
Test script to verify migration files are correct and complete
"""

import os
import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_sql_migration_file():
    """Test the SQL migration file structure and content"""
    print("Testing SQL migration file...")
    
    try:
        with open('add_chatbot_columns.sql', 'r') as f:
            content = f.read()
        
        # Check for required SQL components
        required_components = [
            'BEGIN;',
            'COMMIT;',
            'ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS welcome_message',
            'ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS thank_you_message',
            'ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS connected_account_id',
            'information_schema.columns',
            'table_name = \'chatbots\'',
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        # Check for proper transaction handling
        if 'BEGIN;' in content and 'COMMIT;' in content:
            print("  ✓ Proper transaction handling")
        else:
            print("  ✗ Missing transaction handling")
        
        # Check for error handling
        if 'IF NOT EXISTS' in content:
            print("  ✓ Safe column addition with IF NOT EXISTS")
        else:
            print("  ✗ Missing safe column addition")
        
        if len(found_components) >= len(required_components) - 1:
            print("✓ SQL migration file is well-structured")
            return True
        else:
            print(f"✗ SQL migration file missing {len(required_components) - len(found_components)} components")
            return False
            
    except FileNotFoundError:
        print("✗ add_chatbot_columns.sql not found")
        return False
    except Exception as e:
        print(f"✗ Error testing SQL file: {str(e)}")
        return False


def test_python_migration_file():
    """Test the Python migration file structure and content"""
    print("\nTesting Python migration file...")
    
    try:
        with open('run_chatbot_migration.py', 'r') as f:
            content = f.read()
        
        # Check for required Python components
        required_components = [
            'def get_database_connection_info():',
            'def check_columns_exist():',
            'def run_migration():',
            'def show_sql_instructions():',
            'def main():',
            'psycopg2.connect',
            'ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS',
            'welcome_message',
            'thank_you_message',
            'connected_account_id',
            'conn.commit()',
            'conn.rollback()',
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        # Check for error handling
        if 'try:' in content and 'except' in content:
            print("  ✓ Error handling present")
        else:
            print("  ✗ Missing error handling")
        
        # Check for logging
        if 'logging' in content and 'logger' in content:
            print("  ✓ Logging configured")
        else:
            print("  ✗ Missing logging")
        
        if len(found_components) >= len(required_components) - 2:
            print("✓ Python migration file is well-structured")
            return True
        else:
            print(f"✗ Python migration file missing {len(required_components) - len(found_components)} components")
            return False
            
    except FileNotFoundError:
        print("✗ run_chatbot_migration.py not found")
        return False
    except Exception as e:
        print(f"✗ Error testing Python file: {str(e)}")
        return False


def test_models_file():
    """Test that models.py has the required columns defined"""
    print("\nTesting models.py for required columns...")
    
    try:
        with open('app/models.py', 'r') as f:
            content = f.read()
        
        # Check for required model columns
        required_columns = [
            'welcome_message = Column(String, nullable=True)',
            'thank_you_message = Column(String, nullable=True)',
            'connected_account_id = Column(String, nullable=True)',
        ]
        
        found_columns = []
        for column in required_columns:
            if column in content:
                found_columns.append(column)
                print(f"  ✓ {column}")
            else:
                print(f"  ✗ {column} not found")
        
        # Check for Pydantic models
        pydantic_models = [
            'class ChatbotCreate(BaseModel):',
            'class ChatbotUpdate(BaseModel):',
        ]
        
        for model in pydantic_models:
            if model in content:
                print(f"  ✓ {model}")
            else:
                print(f"  ✗ {model} not found")
        
        # Check if Pydantic models include the new fields
        if 'welcome_message: str = None' in content or 'welcome_message: str = None' in content:
            print("  ✓ Pydantic models include new fields")
        else:
            print("  ✗ Pydantic models missing new fields")
        
        if len(found_columns) == len(required_columns):
            print("✓ Models.py has all required columns")
            return True
        else:
            print(f"✗ Models.py missing {len(required_columns) - len(found_columns)} columns")
            return False
            
    except FileNotFoundError:
        print("✗ app/models.py not found")
        return False
    except Exception as e:
        print(f"✗ Error testing models file: {str(e)}")
        return False


def test_documentation():
    """Test that documentation is complete"""
    print("\nTesting documentation...")
    
    try:
        with open('DATABASE_MIGRATION_GUIDE.md', 'r') as f:
            content = f.read()
        
        # Check for required documentation sections
        required_sections = [
            '# Database Migration Guide',
            '## Problem Description',
            '## Solution Options',
            '### Option 1: Automated Python Migration',
            '### Option 2: Manual SQL Migration',
            '## Verification',
            '## Troubleshooting',
        ]
        
        found_sections = []
        for section in required_sections:
            if section in content:
                found_sections.append(section)
                print(f"  ✓ {section}")
            else:
                print(f"  ✗ {section} not found")
        
        # Check for code examples
        if '```sql' in content and '```bash' in content:
            print("  ✓ Code examples present")
        else:
            print("  ✗ Missing code examples")
        
        if len(found_sections) >= len(required_sections) - 1:
            print("✓ Documentation is comprehensive")
            return True
        else:
            print(f"✗ Documentation missing {len(required_sections) - len(found_sections)} sections")
            return False
            
    except FileNotFoundError:
        print("✗ DATABASE_MIGRATION_GUIDE.md not found")
        return False
    except Exception as e:
        print(f"✗ Error testing documentation: {str(e)}")
        return False


def test_existing_migration():
    """Test the existing migration script"""
    print("\nTesting existing migration script...")
    
    try:
        with open('migrations/add_chatbot_message_fields.py', 'r') as f:
            content = f.read()
        
        # Check for required components
        required_components = [
            'def run_migration():',
            'def check_migration_needed():',
            'def rollback_migration():',
            'ALTER TABLE chatbots',
            'ADD COLUMN IF NOT EXISTS welcome_message',
            'ADD COLUMN IF NOT EXISTS thank_you_message',
            'ADD COLUMN IF NOT EXISTS connected_account_id',
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        if len(found_components) >= len(required_components) - 1:
            print("✓ Existing migration script is complete")
            return True
        else:
            print(f"✗ Existing migration script missing {len(required_components) - len(found_components)} components")
            return False
            
    except FileNotFoundError:
        print("✗ migrations/add_chatbot_message_fields.py not found")
        return False
    except Exception as e:
        print(f"✗ Error testing existing migration: {str(e)}")
        return False


def main():
    """Run all migration file tests"""
    print("=" * 60)
    print("Migration Files Test Suite")
    print("=" * 60)
    
    tests = [
        test_sql_migration_file,
        test_python_migration_file,
        test_models_file,
        test_documentation,
        test_existing_migration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All migration files are ready!")
        print("\nMigration Options Available:")
        print("  1. SQL file: add_chatbot_columns.sql")
        print("  2. Python script: run_chatbot_migration.py")
        print("  3. Existing script: migrations/add_chatbot_message_fields.py")
        print("  4. Complete documentation: DATABASE_MIGRATION_GUIDE.md")
        print("\nTo run the migration:")
        print("  • Use SQL: psql -f add_chatbot_columns.sql")
        print("  • Use Python: python3 run_chatbot_migration.py")
        print("  • Use existing: python3 migrations/add_chatbot_message_fields.py")
    else:
        print("✗ Some migration files have issues. Please review.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
