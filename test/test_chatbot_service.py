#!/usr/bin/env python3
"""
Test script to verify the ChatbotService functionality
"""

import os
import sys
import logging
import pytest
from unittest.mock import MagicMock, patch, AsyncMock

# Set up basic logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_chatbot_service_import():
    """Test that ChatbotService can be imported successfully"""
    print("Testing ChatbotService import...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        print("✓ ChatbotService imported and instantiated successfully")
        print(f"  - Service type: {type(service).__name__}")
        
        return True
        
    except Exception as e:
        print(f"✗ ChatbotService import failed: {str(e)}")
        return False


def test_chatbot_service_methods():
    """Test that ChatbotService has all expected methods"""
    print("\nTesting ChatbotService methods...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        
        expected_methods = [
            'collect_and_publish_chatbot_usage',
            'get_chatbot_count_by_tenant',
            'get_all_tenant_chatbot_counts',
            'get_chatbot_by_id',
            'get_chatbots_by_tenant',
            'get_chatbot_statistics'
        ]
        
        found_methods = []
        for method_name in expected_methods:
            if hasattr(service, method_name):
                method = getattr(service, method_name)
                if callable(method):
                    found_methods.append(method_name)
                    print(f"  ✓ {method_name} method found")
                else:
                    print(f"  ✗ {method_name} is not callable")
            else:
                print(f"  ✗ {method_name} method not found")
        
        if len(found_methods) == len(expected_methods):
            print("✓ All expected methods are present")
            return True
        else:
            print(f"✗ Missing {len(expected_methods) - len(found_methods)} expected methods")
            return False
            
    except Exception as e:
        print(f"✗ Method testing failed: {str(e)}")
        return False


def test_event_listener_integration():
    """Test that event listeners properly use ChatbotService"""
    print("\nTesting event listener integration...")
    
    try:
        from app.services.event_listeners import SchedulerEventListener
        
        listener = SchedulerEventListener()
        
        # Check if the _perform_usage_collection method exists
        if hasattr(listener, '_perform_usage_collection'):
            print("✓ _perform_usage_collection method found in SchedulerEventListener")
            
            # Read the source to verify it uses ChatbotService
            import inspect
            source = inspect.getsource(listener._perform_usage_collection)
            
            if 'ChatbotService' in source:
                print("✓ SchedulerEventListener uses ChatbotService")
                return True
            else:
                print("✗ SchedulerEventListener does not use ChatbotService")
                return False
        else:
            print("✗ _perform_usage_collection method not found")
            return False
            
    except Exception as e:
        print(f"✗ Event listener integration test failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_chatbot_usage_collection_mock():
    """Test chatbot usage collection with mocked dependencies"""
    print("\nTesting chatbot usage collection (mocked)...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        # Mock the database and RabbitMQ dependencies
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.rabbitmq_service') as mock_rabbitmq:
            
            # Mock database session and query results
            mock_db = MagicMock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock query results - simulate 2 tenants with chatbots
            mock_query_result = [
                ('tenant1', 3),  # tenant1 has 3 chatbots
                ('tenant2', 1)   # tenant2 has 1 chatbot
            ]
            
            mock_db.query.return_value.filter.return_value.group_by.return_value.all.return_value = mock_query_result
            
            # Mock RabbitMQ publish_event as async
            mock_rabbitmq.publish_event = AsyncMock()
            
            # Test the service
            service = ChatbotService()
            await service.collect_and_publish_chatbot_usage()
            
            # Verify the publish_event was called with correct data
            mock_rabbitmq.publish_event.assert_called_once()
            call_args = mock_rabbitmq.publish_event.call_args
            
            expected_payload = [
                {"tenantId": "tenant1", "usageEntity": "CHATBOT", "count": 3},
                {"tenantId": "tenant2", "usageEntity": "CHATBOT", "count": 1}
            ]
            
            assert call_args[0][0] == "usage.collected"  # routing key
            assert call_args[0][1] == expected_payload    # payload
            
            print("✓ Chatbot usage collection works correctly")
            print(f"  - Published data for {len(expected_payload)} tenants")
            print(f"  - Routing key: usage.collected")
            
            return True
            
    except Exception as e:
        print(f"✗ Chatbot usage collection test failed: {str(e)}")
        return False


def test_chatbot_service_structure():
    """Test the overall structure of the ChatbotService"""
    print("\nTesting ChatbotService structure...")
    
    try:
        with open('app/services/chatbot_service.py', 'r') as f:
            content = f.read()
        
        required_components = [
            'class ChatbotService',
            'async def collect_and_publish_chatbot_usage',
            'def get_chatbot_count_by_tenant',
            'def get_all_tenant_chatbot_counts',
            'def get_chatbot_by_id',
            'def get_chatbots_by_tenant',
            'def get_chatbot_statistics',
            'from app.services.rabbitmq_service import rabbitmq_service',
            'logger = logging.getLogger(__name__)'
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        if len(found_components) == len(required_components):
            print("✓ ChatbotService structure is complete")
            return True
        else:
            print(f"✗ Missing {len(required_components) - len(found_components)} required components")
            return False
            
    except FileNotFoundError:
        print("✗ chatbot_service.py not found")
        return False
    except Exception as e:
        print(f"✗ Structure test failed: {str(e)}")
        return False


async def run_async_tests():
    """Run async tests"""
    async_tests = [
        test_chatbot_usage_collection_mock
    ]
    
    results = []
    for test in async_tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Async test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    return results


def main():
    """Run all ChatbotService tests"""
    print("=" * 70)
    print("ChatbotService Test Suite")
    print("=" * 70)
    
    # Sync tests
    sync_tests = [
        test_chatbot_service_import,
        test_chatbot_service_methods,
        test_event_listener_integration,
        test_chatbot_service_structure
    ]
    
    sync_results = []
    for test in sync_tests:
        try:
            result = test()
            sync_results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {str(e)}")
            sync_results.append(False)
    
    # Run async tests
    import asyncio
    async_results = asyncio.run(run_async_tests())
    
    # Combine results
    all_results = sync_results + async_results
    passed = sum(all_results)
    total = len(all_results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! ChatbotService is working correctly.")
        print("\nChatbotService Features:")
        print("  • Collects chatbot usage data by tenant")
        print("  • Publishes usage data to RabbitMQ")
        print("  • Provides chatbot count and statistics methods")
        print("  • Integrates with existing event listener system")
        print("  • Proper error handling and logging")
    else:
        print("✗ Some tests failed. Please review the implementation.")
    
    print("=" * 70)


if __name__ == "__main__":
    main()
