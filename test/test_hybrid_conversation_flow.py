"""
Test Hybrid Conversation Flow

This test demonstrates the new hybrid flow:
1. Start conversation API returns welcome message
2. First question is sent via event
3. Subsequent conversation continues via events
"""

import pytest
import asyncio
import json
import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.message_event_listener import message_event_listener


class TestHybridConversationFlow:
    """Test the hybrid conversation flow"""
    
    def setup_method(self):
        """Setup test client"""
        self.client = TestClient(app)
    
    @pytest.mark.asyncio
    async def test_start_conversation_hybrid_flow(self):
        """Test that start conversation returns welcome message and publishes first question"""
        
        # Mock the event publisher
        with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
            mock_publish.return_value = True
            
            # Mock database and other dependencies
            with patch('app.routers.chatbot.get_db'), \
                 patch('app.routers.chatbot.ChatbotService') as mock_chatbot_service, \
                 patch('app.routers.chatbot.RedisService') as mock_redis, \
                 patch('app.routers.chatbot.ElasticsearchService') as mock_es:
                
                # Setup mocks
                mock_chatbot = MagicMock()
                mock_chatbot.id = "chatbot-123"
                mock_chatbot.name = "Test Bot"
                mock_chatbot.welcome_message = "Welcome to Test Bot!"
                
                mock_chatbot_service_instance = mock_chatbot_service.return_value
                mock_chatbot_service_instance.find_chatbot_by_account.return_value = mock_chatbot
                mock_chatbot_service_instance.get_chatbot_questions_for_conversation.return_value = [
                    MagicMock(id=1, question="What is your name?", name="name", entity_type="LEAD")
                ]
                
                mock_es_instance = mock_es.return_value
                mock_es_instance.select_next_question.return_value = (
                    {"id": 1, "question": "What is your name?", "field_name": "name", "entity_type": "LEAD"},
                    100, 50, "gpt-3.5-turbo"
                )
                
                # Test data
                conversation_request = {
                    "message": "Hi, I need help",
                    "entityDetails": [{"id": 123, "entityType": "LEAD"}],
                    "connectedAccount": {"id": 456, "name": "Test Account"}
                }
                
                # Mock auth context
                with patch('app.routers.chatbot.Request') as mock_request:
                    mock_auth_context = MagicMock()
                    mock_auth_context.tenant_id = "tenant-123"
                    mock_auth_context.user_id = "user-456"
                    mock_request.state.auth_context = mock_auth_context
                    
                    # Make the API call
                    response = self.client.post(
                        "/v1/chatbot/conversations",
                        json=conversation_request,
                        headers={"Authorization": "Bearer test-token"}
                    )
                
                # Verify API response
                assert response.status_code == 200
                response_data = response.json()
                
                # Check that response contains conversation ID and welcome message
                assert "chatbotConversationId" in response_data
                assert response_data["message"] == "Welcome to Test Bot!"
                
                # Verify that first question was published as event
                mock_publish.assert_called_once()
                call_args = mock_publish.call_args[1]
                assert call_args["message"] == "What is your name?"
                assert call_args["completed"] is False
                assert call_args["charge"] == 1
    
    @pytest.mark.asyncio
    async def test_message_event_processing(self):
        """Test processing user messages via events"""
        
        # Mock dependencies
        with patch.object(message_event_listener, '_process_conversation_message') as mock_process:
            mock_process.return_value = None
            
            # Create test payload
            payload = {
                "message": "John Doe",
                "chatbotConversationId": str(uuid.uuid4()),
                "completed": False
            }
            
            # Create mock message
            mock_message = MagicMock()
            mock_message.routing_key = "message.chatbot.user.response"
            
            # Process the event
            await message_event_listener.handle_user_message_event(payload, mock_message)
            
            # Verify processing was called
            mock_process.assert_called_once_with(
                payload["chatbotConversationId"],
                payload["message"]
            )
    
    def test_conversation_flow_documentation(self):
        """Test that the flow matches documentation"""
        
        # This test documents the expected flow:
        
        # Step 1: Client calls start conversation API
        # Expected: API returns conversation ID + welcome message
        # Expected: First question is published to ex.whatsappChatbot exchange
        
        # Step 2: Client publishes user response to ex.message exchange
        # Expected: Message listener processes the response
        # Expected: Next question is published to ex.whatsappChatbot exchange
        
        # Step 3: Process continues via events until completion
        # Expected: Completion message is published to ex.whatsappChatbot exchange
        
        flow_steps = [
            {
                "step": 1,
                "action": "POST /v1/chatbot/conversations",
                "api_response": {
                    "chatbotConversationId": "uuid",
                    "message": "Welcome message"
                },
                "event_published": {
                    "exchange": "ex.whatsappChatbot",
                    "routing_key": "chatbot.conversation.response",
                    "payload": {
                        "chatbotConversationId": "uuid",
                        "message": "First question",
                        "completed": False,
                        "charge": 1
                    }
                }
            },
            {
                "step": 2,
                "action": "Publish user message to ex.message",
                "event_consumed": {
                    "exchange": "ex.message",
                    "routing_key": "message.chatbot.user.response",
                    "payload": {
                        "message": "User answer",
                        "chatbotConversationId": "uuid",
                        "completed": False
                    }
                },
                "event_published": {
                    "exchange": "ex.whatsappChatbot",
                    "routing_key": "chatbot.conversation.response",
                    "payload": {
                        "chatbotConversationId": "uuid",
                        "message": "Next question",
                        "completed": False,
                        "charge": 1
                    }
                }
            },
            {
                "step": 3,
                "action": "Continue until completion",
                "event_published": {
                    "exchange": "ex.whatsappChatbot",
                    "routing_key": "chatbot.conversation.response",
                    "payload": {
                        "chatbotConversationId": "uuid",
                        "message": "Thank you! Conversation completed.",
                        "completed": True,
                        "charge": 0
                    }
                }
            }
        ]
        
        # Verify flow structure
        assert len(flow_steps) == 3
        assert flow_steps[0]["step"] == 1
        assert flow_steps[1]["step"] == 2
        assert flow_steps[2]["step"] == 3
        
        # Verify API response structure
        api_response = flow_steps[0]["api_response"]
        assert "chatbotConversationId" in api_response
        assert "message" in api_response
        
        # Verify event payload structure
        event_payload = flow_steps[0]["event_published"]["payload"]
        assert "chatbotConversationId" in event_payload
        assert "message" in event_payload
        assert "completed" in event_payload
        assert "charge" in event_payload
    
    @pytest.mark.asyncio
    async def test_charge_calculation_in_events(self):
        """Test that charges are correctly calculated and included in events"""
        
        with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
            mock_publish.return_value = True
            
            # Test predefined question charge
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id="test-conv",
                message="What is your name?",  # Predefined question
                completed=False,
                charge=1  # Should be 1 for predefined
            )
            
            # Test custom question charge
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id="test-conv",
                message="Tell me more about your specific requirements",  # Custom question
                completed=False,
                charge=2  # Should be 2 for custom
            )
            
            # Test completion charge
            await conversation_event_publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conv",
                completion_message="Thank you for your responses!",
                charge=0  # Should be 0 for completion
            )
            
            # Verify all calls were made with correct charges
            assert mock_publish.call_count == 3
            
            # Check charges in call arguments
            calls = mock_publish.call_args_list
            assert calls[0][1]["charge"] == 1  # Predefined question
            assert calls[1][1]["charge"] == 2  # Custom question
            assert calls[2][1]["charge"] == 0  # Completion


if __name__ == "__main__":
    pytest.main([__file__])
