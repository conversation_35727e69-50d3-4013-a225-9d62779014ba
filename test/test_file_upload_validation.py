#!/usr/bin/env python3
"""
Test script to verify file upload validation for max file size (10MB) and max file count (5 files)
"""

import asyncio
import sys
import os
import pytest
from io import BytesIO
from unittest.mock import Mock, AsyncMock

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chatbot_service import ChatbotService, MAX_FILE_SIZE, MAX_FILE_COUNT
from fastapi import HTTPException


class MockUploadFile:
    """Mock UploadFile for testing"""
    def __init__(self, filename: str, size: int = None, content: bytes = b"test content"):
        self.filename = filename
        self.size = size
        self.content = content
    
    async def read(self):
        return self.content


@pytest.mark.asyncio
async def test_file_count_validation():
    """Test that uploading more than 5 files raises an error"""
    print("Testing file count validation...")
    
    chatbot_service = ChatbotService()
    
    # Create 6 mock files (exceeds limit of 5)
    files = [
        MockUploadFile(f"test{i}.pdf", size=1024)  # 1KB files
        for i in range(6)
    ]
    
    try:
        await chatbot_service.upload_multiple_knowledgebase_files(
            chatbot_id="test-chatbot",
            files=files,
            tenant_id="test-tenant",
            user_id="test-user"
        )
        print("❌ FAILED: Should have raised HTTPException for too many files")
        return False
    except HTTPException as e:
        if e.status_code == 400 and "Maximum 5 files allowed" in e.detail:
            print("✅ PASSED: Correctly rejected 6 files")
            return True
        else:
            print(f"❌ FAILED: Wrong error - {e.detail}")
            return False
    except Exception as e:
        print(f"❌ FAILED: Unexpected error - {str(e)}")
        return False


@pytest.mark.asyncio
async def test_file_size_validation():
    """Test that uploading files larger than 10MB raises an error"""
    print("Testing file size validation...")
    
    chatbot_service = ChatbotService()
    
    # Create a file that's larger than 10MB
    large_file_size = 11 * 1024 * 1024  # 11MB
    files = [
        MockUploadFile("large_file.pdf", size=large_file_size)
    ]
    
    try:
        await chatbot_service.upload_multiple_knowledgebase_files(
            chatbot_id="test-chatbot",
            files=files,
            tenant_id="test-tenant",
            user_id="test-user"
        )
        print("❌ FAILED: Should have raised HTTPException for large file")
        return False
    except HTTPException as e:
        if e.status_code == 400 and "exceeds maximum size of 10MB" in e.detail:
            print("✅ PASSED: Correctly rejected 11MB file")
            return True
        else:
            print(f"❌ FAILED: Wrong error - {e.detail}")
            return False
    except Exception as e:
        print(f"❌ FAILED: Unexpected error - {str(e)}")
        return False


@pytest.mark.asyncio
async def test_secondary_file_size_validation():
    """Test secondary file size validation when file.size is None"""
    print("Testing secondary file size validation...")
    
    chatbot_service = ChatbotService()
    
    # Create a file with no size property but large content
    large_content = b"x" * (11 * 1024 * 1024)  # 11MB of content
    files = [
        MockUploadFile("large_content.pdf", size=None, content=large_content)
    ]
    
    try:
        await chatbot_service.upload_multiple_knowledgebase_files(
            chatbot_id="test-chatbot",
            files=files,
            tenant_id="test-tenant",
            user_id="test-user"
        )
        print("❌ FAILED: Should have raised HTTPException for large content")
        return False
    except HTTPException as e:
        if e.status_code == 400 and "exceeds maximum size of 10MB" in e.detail:
            print("✅ PASSED: Correctly rejected large content via secondary check")
            return True
        else:
            print(f"❌ FAILED: Wrong error - {e.detail}")
            return False
    except Exception as e:
        print(f"❌ FAILED: Unexpected error - {str(e)}")
        return False


@pytest.mark.asyncio
async def test_valid_upload():
    """Test that valid files pass validation"""
    print("Testing valid file upload...")
    
    chatbot_service = ChatbotService()
    
    # Create valid files (small size, within count limit)
    files = [
        MockUploadFile("test1.pdf", size=1024),  # 1KB
        MockUploadFile("test2.pdf", size=2048),  # 2KB
    ]
    
    try:
        # This will fail at database level since we're not mocking the DB,
        # but it should pass the validation checks
        await chatbot_service.upload_multiple_knowledgebase_files(
            chatbot_id="test-chatbot",
            files=files,
            tenant_id="test-tenant",
            user_id="test-user"
        )
        print("✅ PASSED: Valid files passed validation (expected DB error)")
        return True
    except HTTPException as e:
        if e.status_code == 400 and ("Maximum" in e.detail or "exceeds" in e.detail):
            print(f"❌ FAILED: Valid files rejected - {e.detail}")
            return False
        else:
            # Other errors (like DB connection) are expected
            print("✅ PASSED: Valid files passed validation (expected DB error)")
            return True
    except Exception as e:
        # Database or other errors are expected since we're not mocking everything
        print("✅ PASSED: Valid files passed validation (expected DB error)")
        return True


def test_constants():
    """Test that constants are properly defined"""
    print("Testing constants...")
    
    if MAX_FILE_SIZE == 10 * 1024 * 1024:
        print("✅ PASSED: MAX_FILE_SIZE is 10MB")
    else:
        print(f"❌ FAILED: MAX_FILE_SIZE is {MAX_FILE_SIZE}, expected {10 * 1024 * 1024}")
        return False
    
    if MAX_FILE_COUNT == 5:
        print("✅ PASSED: MAX_FILE_COUNT is 5")
    else:
        print(f"❌ FAILED: MAX_FILE_COUNT is {MAX_FILE_COUNT}, expected 5")
        return False
    
    return True


async def main():
    """Run all tests"""
    print("=" * 60)
    print("File Upload Validation Tests")
    print("=" * 60)
    
    tests = [
        test_constants(),
        await test_file_count_validation(),
        await test_file_size_validation(),
        await test_secondary_file_size_validation(),
        await test_valid_upload(),
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! File upload validation is working correctly.")
        print("\nValidation Rules:")
        print(f"  • Maximum file size: {MAX_FILE_SIZE / (1024*1024):.0f}MB")
        print(f"  • Maximum file count: {MAX_FILE_COUNT} files per upload")
        print("  • File type: PDF only")
    else:
        print("❌ Some tests failed. Please review the implementation.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
